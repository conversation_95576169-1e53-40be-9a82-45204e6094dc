using System;
using System.IO;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;

class Program
{
    static void Main(string[] args)
    {
        string filePath = "manualRow_1.xlsx";
        
        try
        {
            using (var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                XSSFWorkbook workbook = new XSSFWorkbook(fs);
                ISheet sheet = workbook.GetSheetAt(0);
                
                // 输出表头
                IRow headerRow = sheet.GetRow(0);
                if (headerRow != null)
                {
                    for (int i = 0; i < headerRow.LastCellNum; i++)
                    {
                        ICell cell = headerRow.GetCell(i);
                        Console.Write(cell?.ToString() + "\t");
                    }
                    Console.WriteLine();
                }
                
                // 输出数据行
                for (int i = 1; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row != null)
                    {
                        for (int j = 0; j < headerRow.LastCellNum; j++)
                        {
                            ICell cell = row.GetCell(j);
                            Console.Write(cell?.ToString() + "\t");
                        }
                        Console.WriteLine();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("读取Excel文件时发生错误: " + ex.Message);
        }
    }
}
