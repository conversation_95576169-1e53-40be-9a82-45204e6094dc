###network区：HMI display、mode固定内容：
1、直接复制模板xml中3077-3459行代码，不作任何修改，包括每一行的缩进也要保持与模板xml中保持一致；然后插入到之前变量声明代码结尾的后一行；

###network区：实例程序：
1、
  1.1针对输入表格中，只有1对输入点的行内容，即“I点-工作位2”和“I点-原位2”的内容为空，方法为：复制模板xml中3460-3892行代码，作为一个单输入点实例的程序内容；
  1.2针对输入表格中，有2对输入点的行内容，即“I点-工作位2”和“I点-原位2”的内容不为空，方法为：复制模板xml中5625-6099行代码，作为一个双输入点实例的程序内容；
2、根据输入表格的内容，改写这个实例程序内容的相关参数，只改写相关参数，不要改变结构内容；具体需要改写哪些参数，请读取manualRow_2.xlsx和template2.xml的内容，了解表格内容与实例程序内容的对应关系。
3、不管是单输入还是双输入类型，从模板xml中复制的程序代码，最后部分：从“</FlgNet></NetworkSource>”这一行开始，到“          <NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">”这一行结束，这一段代码中包含了若干ID编号，比如 <MultilingualText ID="13" CompositionName="Comment">、<MultilingualTextItem ID="14" CompositionName="Items">、<SW.Blocks.CompileUnit ID="19" CompositionName="CompileUnits">等等；这些行中都包含ID编号；这些ID编号，在生成XML的时候是不能重复的，需要按序号往后排列；比如这个实例程序中这一段内容的ID编号从13到19，那么下一个实例程序中这一段内容的ID编号，就从1A开始，注意ID编号用16进制表示。并且只是这一段内容的ID不能重复，其它部分比如Parts、Wires部分的ID是可以重复的。
4、完成以上2步，就完成了一个实例程序内容的生成；接下来，按照以上两步的方法，根据输入表格的行内容，继续进行剩余实例程序内容的生成，直到把输入表格中所有行内容的实例程序内容都生成并改写完成为止。
5、针对最后一个实例程序，需要把这个实例程序的最后一段内容，从</FlgNet></NetworkSource>行开始，到最后一行：<NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">结束，总共41行，整体替换成模板xml中6967-7023行的内容；替换完之后，再改写对应的参数。