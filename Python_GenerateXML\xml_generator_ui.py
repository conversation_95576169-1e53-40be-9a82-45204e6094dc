#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
XML生成器UI界面启动脚本
"""

import os
import sys
import logging
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from xml_generator import XMLGenerator
import threading
from datetime import datetime

class XmlGeneratorUI:
    def __init__(self, root):
        self.root = root
        self.root.title("XML生成器")
        self.root.geometry("680x520")
        self.root.configure(bg="#F0F2F5")
        
        # 主题颜色
        self.PRIMARY_COLOR = "#1890FF"  # 蓝色主题
        self.SECONDARY_COLOR = "#52C41A"  # 绿色按钮
        self.ERROR_COLOR = "#F5222D"     # 错误颜色
        self.BG_COLOR = "#F0F2F5"        # 背景色
        self.FRAME_BG = "#FFFFFF"        # 框架背景
        
        # 设置图标和窗口样式
        self.root.iconbitmap("" if not os.path.exists("icon.ico") else "icon.ico")
        
        # 为整个应用设置字体
        self.default_font = ('Microsoft YaHei UI', 10)
        self.title_font = ('Microsoft YaHei UI', 12, 'bold')
        self.btn_font = ('Microsoft YaHei UI', 10)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger()
        
        # 默认路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.excel_path = os.path.join(current_dir, "manualRow_2.xlsx")
        self.template_path = os.path.join(current_dir, "template2.xml")
        self.output_path = os.path.join(current_dir, "output_new.xml")
        
        # 进度状态
        self.is_generating = False
        self.progress_value = 0
        
        self.create_widgets()
        
        # 应用样式到所有子控件
        self.apply_style_recursively(self.root)
    
    def apply_style_recursively(self, widget):
        """递归应用样式到所有子控件"""
        if isinstance(widget, (tk.Label, tk.Entry, tk.Button, tk.Checkbutton)):
            widget.configure(font=self.default_font)
        
        for child in widget.winfo_children():
            self.apply_style_recursively(child)
    
    def create_widgets(self):
        # 创建主标题
        title_frame = tk.Frame(self.root, bg=self.BG_COLOR)
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(title_frame, text="TIA Portal XML生成器", 
                              font=('Microsoft YaHei UI', 18, 'bold'),
                              fg=self.PRIMARY_COLOR, bg=self.BG_COLOR)
        title_label.pack(side=tk.LEFT)
        
        # 创建当前时间显示
        self.time_label = tk.Label(title_frame, text=self.get_current_time(), 
                                 font=('Microsoft YaHei UI', 10),
                                 fg="#888888", bg=self.BG_COLOR)
        self.time_label.pack(side=tk.RIGHT)
        self.update_time()
        
        # 创建文件设置区域 - 使用Frame包装
        files_frame = tk.LabelFrame(self.root, text="文件设置", padx=15, pady=15, 
                                 font=self.title_font, fg=self.PRIMARY_COLOR,
                                 bg=self.FRAME_BG, relief=tk.GROOVE)
        files_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Excel文件选择
        excel_frame = tk.Frame(files_frame, bg=self.FRAME_BG)
        excel_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(excel_frame, text="Excel文件:", width=10, anchor="w", 
               bg=self.FRAME_BG).pack(side=tk.LEFT)
        
        self.excel_entry = tk.Entry(excel_frame, width=50, font=self.default_font, 
                                 relief=tk.GROOVE, bd=2)
        self.excel_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.excel_entry.insert(0, self.excel_path)
        
        browse_excel_btn = tk.Button(excel_frame, text="浏览...", 
                                  command=self.browse_excel,
                                  bg=self.PRIMARY_COLOR, fg="white",
                                  activebackground="#40A9FF", 
                                  font=self.btn_font, relief=tk.RAISED,
                                  width=8, cursor="hand2")
        browse_excel_btn.pack(side=tk.RIGHT)
        
        # 模板文件选择
        template_frame = tk.Frame(files_frame, bg=self.FRAME_BG)
        template_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(template_frame, text="模板XML:", width=10, anchor="w", 
               bg=self.FRAME_BG).pack(side=tk.LEFT)
        
        self.template_entry = tk.Entry(template_frame, width=50, font=self.default_font, 
                                     relief=tk.GROOVE, bd=2)
        self.template_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.template_entry.insert(0, self.template_path)
        
        browse_template_btn = tk.Button(template_frame, text="浏览...", 
                                     command=self.browse_template,
                                     bg=self.PRIMARY_COLOR, fg="white",
                                     activebackground="#40A9FF", 
                                     font=self.btn_font, relief=tk.RAISED,
                                     width=8, cursor="hand2")
        browse_template_btn.pack(side=tk.RIGHT)
        
        # 输出文件选择
        output_frame = tk.Frame(files_frame, bg=self.FRAME_BG)
        output_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(output_frame, text="输出XML:", width=10, anchor="w", 
               bg=self.FRAME_BG).pack(side=tk.LEFT)
        
        self.output_entry = tk.Entry(output_frame, width=50, font=self.default_font, 
                                   relief=tk.GROOVE, bd=2)
        self.output_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.output_entry.insert(0, self.output_path)
        
        browse_output_btn = tk.Button(output_frame, text="浏览...", 
                                    command=self.browse_output,
                                    bg=self.PRIMARY_COLOR, fg="white",
                                    activebackground="#40A9FF", 
                                    font=self.btn_font, relief=tk.RAISED,
                                    width=8, cursor="hand2")
        browse_output_btn.pack(side=tk.RIGHT)
        
        # 操作区域
        action_frame = tk.Frame(self.root, bg=self.BG_COLOR)
        action_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 状态区域
        self.status_label = tk.Label(action_frame, text="就绪", 
                                  fg="#888888", bg=self.BG_COLOR,
                                  font=self.default_font)
        self.status_label.pack(side=tk.LEFT)
        
        # 生成按钮
        self.generate_btn = tk.Button(action_frame, text="生成XML文件", 
                                   command=self.generate_xml,
                                   bg=self.SECONDARY_COLOR, fg="white",
                                   activebackground="#73D13D",
                                   font=('Microsoft YaHei UI', 11, 'bold'), 
                                   width=15, height=1, cursor="hand2",
                                   relief=tk.RAISED)
        self.generate_btn.pack(side=tk.RIGHT)
        
        # 进度条
        progress_frame = tk.Frame(self.root, bg=self.BG_COLOR)
        progress_frame.pack(fill=tk.X, padx=20, pady=(0, 10))
        
        self.progress = ttk.Progressbar(progress_frame, orient="horizontal", 
                                     length=100, mode="determinate")
        self.progress.pack(fill=tk.X)
        self.progress["value"] = 0
        
        # 日志显示区域
        log_frame = tk.LabelFrame(self.root, text="操作日志", 
                               padx=10, pady=10, 
                               font=self.title_font, fg=self.PRIMARY_COLOR,
                               bg=self.FRAME_BG, relief=tk.GROOVE)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 创建一个框架来容纳文本和滚动条
        text_frame = tk.Frame(log_frame, bg=self.FRAME_BG)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        # 日志文本框
        self.log_text = tk.Text(text_frame, font=('Consolas', 9), 
                              bg="#FAFAFA", fg="#333333", 
                              wrap=tk.WORD, relief=tk.SUNKEN, bd=1,
                              height=12)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.log_text.configure(state="normal")
        
        # 添加滚动条
        scrollbar = tk.Scrollbar(text_frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # 底部状态栏
        status_bar = tk.Frame(self.root, bg="#E6F7FF", bd=1, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        status_text = tk.Label(status_bar, text="TIA Portal XML生成器 v1.0 | Made with ♥", 
                            bg="#E6F7FF", fg="#888888", 
                            font=('Microsoft YaHei UI', 8), 
                            padx=10, pady=2)
        status_text.pack(side=tk.LEFT)
        
        # 添加初始欢迎消息
        self.log_text.insert(tk.END, "欢迎使用TIA Portal XML生成器!\n")
        self.log_text.insert(tk.END, "请设置文件路径并点击 [生成XML文件] 按钮开始处理\n")
        
        # 自定义日志处理器
        self.log_handler = logging.StreamHandler(self)
        self.log_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        self.log_handler.setFormatter(formatter)
        self.logger.addHandler(self.log_handler)
    
    def update_time(self):
        """更新时间显示"""
        self.time_label.config(text=self.get_current_time())
        self.root.after(1000, self.update_time)  # 每秒更新一次
    
    def get_current_time(self):
        """获取当前时间字符串"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def write(self, message):
        """用于将日志记录到UI中"""
        self.log_text.insert(tk.END, message)
        self.log_text.see(tk.END)
        
        # 更新进度
        if self.is_generating:
            if "成功" in message:
                self.progress["value"] = 100
                self.status_label.config(text="处理完成", fg="#52C41A")
            elif self.progress["value"] < 90:
                self.progress["value"] += 5  # 增加进度
    
    def flush(self):
        """为了兼容logging.StreamHandler"""
        pass
    
    def browse_excel(self):
        filename = filedialog.askopenfilename(filetypes=[("Excel文件", "*.xlsx;*.xls")])
        if filename:
            self.excel_entry.delete(0, tk.END)
            self.excel_entry.insert(0, filename)
    
    def browse_template(self):
        filename = filedialog.askopenfilename(filetypes=[("XML文件", "*.xml")])
        if filename:
            self.template_entry.delete(0, tk.END)
            self.template_entry.insert(0, filename)
    
    def browse_output(self):
        filename = filedialog.asksaveasfilename(defaultextension=".xml", filetypes=[("XML文件", "*.xml")])
        if filename:
            self.output_entry.delete(0, tk.END)
            self.output_entry.insert(0, filename)
    
    def generate_xml(self):
        """在后台线程中生成XML，防止UI卡顿"""
        # 获取路径
        excel_file = self.excel_entry.get()
        template_file = self.template_entry.get()
        output_file = self.output_entry.get()
        
        # 检查文件存在性
        if not os.path.exists(excel_file):
            messagebox.showerror("错误", f"Excel文件不存在: {excel_file}")
            return
        if not os.path.exists(template_file):
            messagebox.showerror("错误", f"模板文件不存在: {template_file}")
            return
        
        # 清空日志并显示开始消息
        self.log_text.delete(1.0, tk.END)
        self.log_text.insert(tk.END, f"=== {self.get_current_time()} 开始生成XML ===\n\n")
        
        # 更新UI状态
        self.is_generating = True
        self.progress["value"] = 10
        self.generate_btn.config(state=tk.DISABLED, text="处理中...")
        self.status_label.config(text="正在处理...", fg=self.PRIMARY_COLOR)
        
        def run_generation():
            try:
                # 创建XML生成器实例
                generator = XMLGenerator(
                    template_filepath=template_file,
                    output_filepath=output_file,
                    excel_filepath=excel_file,
                    logger=self.logger
                )
                
                # 生成XML
                success = generator.generate_xml()
                
                # 更新UI（必须在主线程中）
                self.root.after(0, lambda: self.generation_complete(success, output_file))
                
            except Exception as e:
                # 更新UI（必须在主线程中）
                self.root.after(0, lambda: self.log_error(str(e)))
        
        # 在后台线程中运行
        threading.Thread(target=run_generation).start()
    
    def generation_complete(self, success, output_file):
        """处理生成完成事件"""
        self.is_generating = False
        self.generate_btn.config(state=tk.NORMAL, text="生成XML文件")
        
        if success:
            self.progress["value"] = 100
            self.status_label.config(text="处理完成", fg=self.SECONDARY_COLOR)
            self.log_text.insert(tk.END, f"\n=== {self.get_current_time()} 生成成功! ===\n")
            self.log_text.insert(tk.END, f"文件已保存至: {output_file}\n")
            
            messagebox.showinfo("成功", f"XML生成成功!\n文件已保存至:\n{output_file}")
        else:
            self.progress["value"] = 0
            self.status_label.config(text="处理失败", fg=self.ERROR_COLOR)
            self.log_text.insert(tk.END, f"\n=== {self.get_current_time()} 生成失败! ===\n")
            self.log_text.insert(tk.END, "请检查日志获取详细信息。\n")
            
            messagebox.showerror("错误", "XML生成失败，请检查日志获取详细信息。")
    
    def log_error(self, error_message):
        """记录错误消息"""
        self.is_generating = False
        self.generate_btn.config(state=tk.NORMAL, text="生成XML文件")
        self.progress["value"] = 0
        self.status_label.config(text="处理出错", fg=self.ERROR_COLOR)
        
        self.log_text.insert(tk.END, f"\n=== {self.get_current_time()} 错误! ===\n")
        self.log_text.insert(tk.END, f"错误信息: {error_message}\n")
        
        messagebox.showerror("错误", f"处理时出错:\n{error_message}")

if __name__ == "__main__":
    root = tk.Tk()
    app = XmlGeneratorUI(root)
    root.mainloop() 