# 西门子博图V18 Openness工具

这是一个基于西门子Openness API的小工具，用于方便使用博图V18进行PLC编程。该工具使用西门子提供的Openness API与TIA Portal V18进行交互，实现程序块、变量表和PLC数据类型的导入导出功能。

## 功能描述

1. **主界面功能**
   - 打开博图V18软件并建立连接
   - 打开博图项目文件
   - 以树形结构显示项目中的程序库（程序块）、变量表、PLC数据类型

2. **导出功能**
   - 将程序库中的FB块、FC块或DB块导出为XML文件
   - 将变量表或PLC数据类型导出为Excel文件

3. **导入功能**
   - 导入XML文件，自动生成相应的FB块、FC块或DB块
   - 导入Excel文件，自动生成相应的变量表或PLC数据类型

## 技术要求

- 开发语言：C#
- 框架：.NET Framework 4.8
- 界面：Winform
- 依赖库：
  - 西门子Openness API (Siemens.Engineering.dll, Siemens.Engineering.Hmi.dll)
  - NPOI (用于Excel文件处理)

## 安装与配置

1. 确保已安装西门子TIA Portal V18
2. 确保TIA Portal V18已配置Openness功能
3. 编译此项目前，请确认以下DLL路径是否正确：
   - `C:\Program Files\Siemens\Automation\Portal V18\PublicAPI\V18\Siemens.Engineering.dll`
   - `C:\Program Files\Siemens\Automation\Portal V18\PublicAPI\V18\Siemens.Engineering.Hmi.dll`
   - 如路径不同，请在项目文件中更新路径

## 使用说明

1. 启动应用程序
2. 点击"打开博图"按钮启动TIA Portal V18并建立连接
   - 如果TIA Portal V18已运行，将自动连接到现有实例
   - 如果未运行，将启动新实例
3. 点击"打开项目"按钮选择要打开的TIA项目文件(*.ap*)
4. 在左侧树形结构中浏览项目内容
   - 程序库下显示所有程序块(FB、FC、DB等)
   - 变量表下显示所有PLC变量表
   - PLC数据类型下显示所有自定义数据类型
5. 选择具体项目后，点击导出按钮：
   - 对于程序块，导出为XML文件
   - 对于变量表和PLC数据类型，导出为Excel文件
6. 使用导入按钮将外部XML或Excel文件导入到项目中

## 注意事项

- 本工具需要西门子TIA Portal V18的授权
- 使用Openness功能需要管理员权限
- 如遇到"无法连接到TIA Portal"错误，请检查：
  1. TIA Portal是否已安装
  2. Openness功能是否已启用
  3. 程序是否以管理员身份运行
- 导入的程序块可能需要在TIA Portal中手动编译
- 使用Excel导入时，请确保Excel格式正确：
  - 第一行为标题行
  - 变量表至少包含名称、数据类型、地址三列
  - PLC数据类型至少包含名称和数据类型两列

## Openness API集成

本工具使用西门子Openness API实现以下功能：

1. **与TIA Portal的连接**
   - 使用`TiaPortal`类建立连接
   - 支持连接到现有实例或启动新实例

2. **项目操作**
   - 打开现有项目
   - 读取项目结构
   - 访问PLC对象(设备、软件等)

3. **程序块操作**
   - 读取程序块列表
   - 导出程序块到XML文件
   - 从XML文件导入程序块

4. **变量表操作**
   - 读取变量表列表
   - 导出变量表到Excel文件
   - 从Excel文件导入变量表

5. **PLC数据类型操作**
   - 读取PLC数据类型列表
   - 导出数据类型到Excel文件 