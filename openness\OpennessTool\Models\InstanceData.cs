using System;
using System.Collections.Generic;

namespace OpennessTool.Models
{
    /// <summary>
    /// 实例数据模型，对应Excel表格中的一行数据
    /// </summary>
    public class InstanceData
    {
        /// <summary>
        /// 实例名称 (G列)
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 实例注释 (H列)
        /// </summary>
        public string Comment { get; set; }

        /// <summary>
        /// Excel行号
        /// </summary>
        public int ExcelRow { get; set; }

        /// <summary>
        /// 是否为双输入类型
        /// </summary>
        public bool IsDoubleInputType { get; set; }

        /// <summary>
        /// I点-工作位1 (I列)
        /// </summary>
        public string ForwardSensorAddr { get; set; }

        /// <summary>
        /// I点-原位1 (M列)
        /// </summary>
        public string BackwardSensorAddr { get; set; }

        /// <summary>
        /// I点-工作位2 (K列) - 双输入类型使用
        /// </summary>
        public string ForwardSensor2Addr { get; set; }

        /// <summary>
        /// I点-原位2 (O列) - 双输入类型使用
        /// </summary>
        public string BackwardSensor2Addr { get; set; }

        /// <summary>
        /// Q点-工作位 (Q列)
        /// </summary>
        public string ForwardOutputAddr { get; set; }

        /// <summary>
        /// Q点-原位 (S列)
        /// </summary>
        public string BackwardOutputAddr { get; set; }

        /// <summary>
        /// 手动行DB块名称 (E列)
        /// </summary>
        public string ManualDbName { get; set; }

        /// <summary>
        /// OpMode名称 (B列)
        /// </summary>
        public string OpModeName { get; set; }

        /// <summary>
        /// OpMode编号 (D列)
        /// </summary>
        public string OpModeNumber { get; set; }

        /// <summary>
        /// OpMode注释 (C列)
        /// </summary>
        public string OpModeComment { get; set; }

        /// <summary>
        /// 手动FB块编号 (F列)
        /// </summary>
        public string FbTemplate { get; set; }

        // 偏移量属性
        public string ForwardSensorOffset { get; set; }
        public string BackwardSensorOffset { get; set; }
        public string ForwardSensor2Offset { get; set; }
        public string BackwardSensor2Offset { get; set; }
        public string ForwardOutputOffset { get; set; }
        public string BackwardOutputOffset { get; set; }

        // 生成的描述信息
        public string ForwardSensorDesc => $"{ForwardSensorAddr} {Name}工作位";
        public string BackwardSensorDesc => $"{BackwardSensorAddr} {Name}原位";
        public string ForwardSensor2Desc => $"{ForwardSensor2Addr} {Name}工作位2";
        public string BackwardSensor2Desc => $"{BackwardSensor2Addr} {Name}原位2";
        public string ForwardOutputDesc => $"{ForwardOutputAddr} {Name}工作位输出";
        public string BackwardOutputDesc => $"{BackwardOutputAddr} {Name}原位输出";

        /// <summary>
        /// 获取实例类型字符串
        /// </summary>
        public string Type => IsDoubleInputType ? "DoubleInput" : "SingleInput";

        /// <summary>
        /// 构造函数
        /// </summary>
        public InstanceData()
        {
            Name = string.Empty;
            Comment = string.Empty;
            ForwardSensorAddr = string.Empty;
            BackwardSensorAddr = string.Empty;
            ForwardSensor2Addr = string.Empty;
            BackwardSensor2Addr = string.Empty;
            ForwardOutputAddr = string.Empty;
            BackwardOutputAddr = string.Empty;
            ManualDbName = string.Empty;
            OpModeName = string.Empty;
            OpModeNumber = string.Empty;
            OpModeComment = string.Empty;
            FbTemplate = string.Empty;
            ForwardSensorOffset = string.Empty;
            BackwardSensorOffset = string.Empty;
            ForwardSensor2Offset = string.Empty;
            BackwardSensor2Offset = string.Empty;
            ForwardOutputOffset = string.Empty;
            BackwardOutputOffset = string.Empty;
        }

        /// <summary>
        /// 验证实例数据的完整性
        /// </summary>
        /// <returns>验证结果和错误信息</returns>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            if (string.IsNullOrWhiteSpace(Name))
                return (false, "实例名称不能为空");

            if (string.IsNullOrWhiteSpace(ForwardSensorAddr))
                return (false, "I点-工作位1不能为空");

            if (string.IsNullOrWhiteSpace(BackwardSensorAddr))
                return (false, "I点-原位1不能为空");

            if (string.IsNullOrWhiteSpace(ForwardOutputAddr))
                return (false, "Q点-工作位不能为空");

            if (string.IsNullOrWhiteSpace(BackwardOutputAddr))
                return (false, "Q点-原位不能为空");

            // 双输入类型的额外验证
            if (IsDoubleInputType)
            {
                if (string.IsNullOrWhiteSpace(ForwardSensor2Addr))
                    return (false, "双输入类型的I点-工作位2不能为空");

                if (string.IsNullOrWhiteSpace(BackwardSensor2Addr))
                    return (false, "双输入类型的I点-原位2不能为空");
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// 转换为字典格式，兼容Python版本的数据结构
        /// </summary>
        /// <returns>字典格式的实例数据</returns>
        public Dictionary<string, object> ToDictionary()
        {
            return new Dictionary<string, object>
            {
                ["name"] = Name,
                ["comment"] = Comment,
                ["excel_row"] = ExcelRow,
                ["is_double_input_type"] = IsDoubleInputType,
                ["type"] = Type,
                
                // I/O地址
                ["I点-工作位1"] = ForwardSensorAddr,
                ["I点-原位1"] = BackwardSensorAddr,
                ["I点-工作位2"] = ForwardSensor2Addr,
                ["I点-原位2"] = BackwardSensor2Addr,
                ["Q点-工作位"] = ForwardOutputAddr,
                ["Q点-原位"] = BackwardOutputAddr,
                
                // 偏移量
                ["I点-工作位1偏移量"] = ForwardSensorOffset,
                ["I点-原位1偏移量"] = BackwardSensorOffset,
                ["I点-工作位2偏移量"] = ForwardSensor2Offset,
                ["I点-原位2偏移量"] = BackwardSensor2Offset,
                ["Q点-工作位偏移量"] = ForwardOutputOffset,
                ["Q点-原位偏移量"] = BackwardOutputOffset,
                
                // 其他属性
                ["手动行DB块名称"] = ManualDbName,
                ["OpMode名称"] = OpModeName,
                ["OpMode编号"] = OpModeNumber,
                ["OpMode注释"] = OpModeComment,
                ["手动FB 块编号"] = FbTemplate,
                
                // 兼容旧版本的属性名
                ["forward_sensor_addr"] = ForwardSensorAddr,
                ["backward_sensor_addr"] = BackwardSensorAddr,
                ["forward_sensor2_addr"] = ForwardSensor2Addr,
                ["backward_sensor2_addr"] = BackwardSensor2Addr,
                ["forward_output_addr"] = ForwardOutputAddr,
                ["backward_output_addr"] = BackwardOutputAddr,
                ["forward_sensor_desc"] = ForwardSensorDesc,
                ["backward_sensor_desc"] = BackwardSensorDesc,
                ["forward_sensor2_desc"] = ForwardSensor2Desc,
                ["backward_sensor2_desc"] = BackwardSensor2Desc,
                ["forward_output_desc"] = ForwardOutputDesc,
                ["backward_output_desc"] = BackwardOutputDesc,
                ["manual_db_name"] = ManualDbName,
                ["op_mode_name"] = OpModeName,
                ["op_mode_number"] = OpModeNumber,
                ["fb_template"] = FbTemplate
            };
        }
    }
}
