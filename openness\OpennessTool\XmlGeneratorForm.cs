using System;
using System.ComponentModel;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;
using OpennessTool.Core;

namespace OpennessTool
{
    /// <summary>
    /// XML生成器界面
    /// </summary>
    public partial class XmlGeneratorForm : Form
    {
        private XmlGenerator _xmlGenerator;
        private string _templatePath = "";
        private string _excelPath = "";
        private string _outputPath = "";

        public XmlGeneratorForm()
        {
            InitializeComponent();
            InitializeDefaultPaths();
        }

        /// <summary>
        /// 初始化默认路径
        /// </summary>
        private void InitializeDefaultPaths()
        {
            string currentDir = Application.StartupPath;
            _templatePath = Path.Combine(currentDir, "template2.xml");
            _excelPath = Path.Combine(currentDir, "manualRow_2.xlsx");
            _outputPath = Path.Combine(currentDir, "output_new.xml");

            txtTemplatePath.Text = _templatePath;
            txtExcelPath.Text = _excelPath;
            txtOutputPath.Text = _outputPath;
        }

        /// <summary>
        /// 浏览模板文件
        /// </summary>
        private void btnBrowseTemplate_Click(object sender, EventArgs e)
        {
            using (var dialog = new OpenFileDialog())
            {
                dialog.Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*";
                dialog.Title = "选择模板XML文件";
                dialog.FileName = Path.GetFileName(_templatePath);
                dialog.InitialDirectory = Path.GetDirectoryName(_templatePath);

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    _templatePath = dialog.FileName;
                    txtTemplatePath.Text = _templatePath;
                    UpdateGeneratorStatus();
                }
            }
        }

        /// <summary>
        /// 浏览Excel文件
        /// </summary>
        private void btnBrowseExcel_Click(object sender, EventArgs e)
        {
            using (var dialog = new OpenFileDialog())
            {
                dialog.Filter = "Excel文件 (*.xlsx;*.xls)|*.xlsx;*.xls|所有文件 (*.*)|*.*";
                dialog.Title = "选择Excel数据文件";
                dialog.FileName = Path.GetFileName(_excelPath);
                dialog.InitialDirectory = Path.GetDirectoryName(_excelPath);

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    _excelPath = dialog.FileName;
                    txtExcelPath.Text = _excelPath;
                    UpdateGeneratorStatus();
                    LoadInstanceStatistics();
                }
            }
        }

        /// <summary>
        /// 浏览输出文件
        /// </summary>
        private void btnBrowseOutput_Click(object sender, EventArgs e)
        {
            using (var dialog = new SaveFileDialog())
            {
                dialog.Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*";
                dialog.Title = "选择输出XML文件";
                dialog.FileName = Path.GetFileName(_outputPath);
                dialog.InitialDirectory = Path.GetDirectoryName(_outputPath);

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    _outputPath = dialog.FileName;
                    txtOutputPath.Text = _outputPath;
                }
            }
        }

        /// <summary>
        /// 生成XML文件
        /// </summary>
        private async void btnGenerate_Click(object sender, EventArgs e)
        {
            if (!ValidateInputs())
                return;

            try
            {
                // 禁用界面
                SetUIEnabled(false);
                progressBar.Style = ProgressBarStyle.Marquee;
                lblStatus.Text = "正在生成XML文件...";
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 开始生成XML文件...\r\n");

                // 创建生成器
                _xmlGenerator = new XmlGenerator(_templatePath, _outputPath, _excelPath);

                // 在后台线程中生成
                bool success = await Task.Run(() => _xmlGenerator.GenerateXml());

                if (success)
                {
                    lblStatus.Text = "XML文件生成成功";
                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] XML文件生成成功: {_outputPath}\r\n");
                    MessageBox.Show($"XML文件生成成功！\n\n输出文件: {_outputPath}", 
                        "生成成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    lblStatus.Text = "XML文件生成失败";
                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] XML文件生成失败\r\n");
                    MessageBox.Show("XML文件生成失败，请查看日志获取详细信息。", 
                        "生成失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "生成过程中发生错误";
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 错误: {ex.Message}\r\n");
                MessageBox.Show($"生成XML文件时发生错误:\n\n{ex.Message}", 
                    "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复界面
                SetUIEnabled(true);
                progressBar.Style = ProgressBarStyle.Blocks;
                progressBar.Value = 0;
            }
        }

        /// <summary>
        /// 预览生成结果
        /// </summary>
        private void btnPreview_Click(object sender, EventArgs e)
        {
            if (!ValidateInputs())
                return;

            try
            {
                _xmlGenerator = new XmlGenerator(_templatePath, _outputPath, _excelPath);
                string preview = _xmlGenerator.PreviewGeneration(50);
                
                var previewForm = new Form
                {
                    Text = "XML生成预览",
                    Size = new System.Drawing.Size(800, 600),
                    StartPosition = FormStartPosition.CenterParent
                };

                var textBox = new TextBox
                {
                    Multiline = true,
                    ScrollBars = ScrollBars.Both,
                    Dock = DockStyle.Fill,
                    Font = new System.Drawing.Font("Consolas", 9),
                    Text = preview,
                    ReadOnly = true
                };

                previewForm.Controls.Add(textBox);
                previewForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"预览生成时发生错误:\n\n{ex.Message}", 
                    "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInputs()
        {
            if (string.IsNullOrWhiteSpace(_templatePath) || !File.Exists(_templatePath))
            {
                MessageBox.Show("请选择有效的模板XML文件。", "输入验证", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(_excelPath) || !File.Exists(_excelPath))
            {
                MessageBox.Show("请选择有效的Excel数据文件。", "输入验证", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(_outputPath))
            {
                MessageBox.Show("请指定输出XML文件路径。", "输入验证", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 设置UI启用状态
        /// </summary>
        private void SetUIEnabled(bool enabled)
        {
            btnBrowseTemplate.Enabled = enabled;
            btnBrowseExcel.Enabled = enabled;
            btnBrowseOutput.Enabled = enabled;
            btnGenerate.Enabled = enabled;
            btnPreview.Enabled = enabled;
        }

        /// <summary>
        /// 更新生成器状态
        /// </summary>
        private void UpdateGeneratorStatus()
        {
            try
            {
                if (File.Exists(_templatePath) && File.Exists(_excelPath))
                {
                    _xmlGenerator = new XmlGenerator(_templatePath, _outputPath, _excelPath);
                    var (templatePath, excelPath, outputPath, status) = _xmlGenerator.GetGeneratorInfo();
                    lblStatus.Text = status;
                }
                else
                {
                    lblStatus.Text = "请选择模板文件和Excel文件";
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"状态检查错误: {ex.Message}";
            }
        }

        /// <summary>
        /// 加载实例统计信息
        /// </summary>
        private void LoadInstanceStatistics()
        {
            try
            {
                if (File.Exists(_templatePath) && File.Exists(_excelPath))
                {
                    _xmlGenerator = new XmlGenerator(_templatePath, _outputPath, _excelPath);
                    var (totalInstances, singleInputCount, doubleInputCount, instanceNames) = 
                        _xmlGenerator.GetInstanceStatistics();

                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 实例统计信息:\r\n");
                    txtLog.AppendText($"  总实例数: {totalInstances}\r\n");
                    txtLog.AppendText($"  单输入类型: {singleInputCount}\r\n");
                    txtLog.AppendText($"  双输入类型: {doubleInputCount}\r\n");
                    
                    if (instanceNames.Count > 0)
                    {
                        txtLog.AppendText($"  实例名称: {string.Join(", ", instanceNames)}\r\n");
                    }
                    txtLog.AppendText("\r\n");
                }
            }
            catch (Exception ex)
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 加载统计信息时出错: {ex.Message}\r\n");
            }
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        private void btnClearLog_Click(object sender, EventArgs e)
        {
            txtLog.Clear();
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void XmlGeneratorForm_Load(object sender, EventArgs e)
        {
            UpdateGeneratorStatus();
            txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] XML生成器已启动\r\n");
        }

        /// <summary>
        /// 路径文本框内容改变事件
        /// </summary>
        private void txtTemplatePath_TextChanged(object sender, EventArgs e)
        {
            _templatePath = txtTemplatePath.Text;
            UpdateGeneratorStatus();
        }

        private void txtExcelPath_TextChanged(object sender, EventArgs e)
        {
            _excelPath = txtExcelPath.Text;
            UpdateGeneratorStatus();
            LoadInstanceStatistics();
        }

        private void txtOutputPath_TextChanged(object sender, EventArgs e)
        {
            _outputPath = txtOutputPath.Text;
        }
    }
}
