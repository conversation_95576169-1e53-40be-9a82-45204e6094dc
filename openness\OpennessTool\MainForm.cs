﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using System.Diagnostics;
using System.Xml;
using System.Threading;
using Siemens.Engineering;
using Siemens.Engineering.HW;
using Siemens.Engineering.HW.Features;
using Siemens.Engineering.SW;
using Siemens.Engineering.SW.Blocks;
using Siemens.Engineering.SW.Tags;
using Siemens.Engineering.SW.Types;
using Siemens.Engineering.Compiler;
using System.Linq;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.HSSF.UserModel;
using System.Globalization;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;

namespace OpennessTool
{
    public partial class MainForm : Form
    {
        private TiaPortal tiaPortal;
        private Project project;
        private string tiaPortalPath = @"C:\Program Files\Siemens\Automation\Portal V18\bin\Siemens.Automation.Portal.exe";
        private string manualRowExcelPath;

        public MainForm()
        {
            InitializeComponent();
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            // 初始化界面
            btnExport.Enabled = false;
            btnRefresh.Enabled = true;
            CheckTiaPortalInstallation();
        }

        private void CheckTiaPortalInstallation()
        {
            if (!File.Exists(tiaPortalPath))
            {
                MessageBox.Show("未找到博图V18安装路径，请确认博图V18已正确安装。", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tiaPortalPath = "Siemens.Automation.Portal.exe"; // 尝试使用默认路径
            }
        }

        private void btnOpenTIA_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否已有打开的TIA Portal实例
                IList<TiaPortalProcess> tiaPortalProcesses = TiaPortal.GetProcesses();
                if (tiaPortalProcesses.Count > 0)
                {
                    // 尝试连接到已打开的TIA Portal实例
                    tiaPortal = tiaPortalProcesses[0].Attach();
                    MessageBox.Show("已连接到现有的博图V18实例。", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // 启动博图V18软件
                    tiaPortal = new TiaPortal(TiaPortalMode.WithUserInterface);
                    Process.Start(tiaPortalPath, "/Open");
                    MessageBox.Show("博图V18已启动。", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动博图V18失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnOpenProject_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "博图项目文件 (*.ap*)|*.ap*";
                openFileDialog.Title = "选择博图项目文件";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string projectPath = openFileDialog.FileName;
                    try
                    {
                        // 确保已有TIA Portal实例
                        if (tiaPortal == null)
                        {
                            // 首先尝试连接到已打开的实例
                            IList<TiaPortalProcess> tiaPortalProcesses = TiaPortal.GetProcesses();
                            if (tiaPortalProcesses.Count > 0)
                            {
                                tiaPortal = tiaPortalProcesses[0].Attach();
                                MessageBox.Show("已连接到现有的博图V18实例。", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                            else
                            {
                                // 如果没有打开的实例，则自动启动TIA Portal
                                tiaPortal = new TiaPortal(TiaPortalMode.WithUserInterface);
                                Process.Start(tiaPortalPath, "/Open");
                                MessageBox.Show("博图V18已自动启动。", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);

                                // 等待一段时间让TIA Portal完全启动
                                Thread.Sleep(3000);
                            }
                        }

                        // 使用Openness API打开项目
                        OpenProject(projectPath);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"打开项目失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void OpenProject(string projectPath)
        {
            Cursor = Cursors.WaitCursor;
            treeViewProject.Nodes.Clear();

            try
            {
                // 检查项目是否已经打开
                if (tiaPortal.Projects.Count > 0)
                {
                    // 创建项目集合的副本，避免在遍历时修改集合
                    var openProjects = tiaPortal.Projects.ToArray();

                    // 关闭已打开的项目
                    foreach (var openProject in openProjects)
                    {
                        openProject.Close();
                    }
                }

                // 打开项目
                project = tiaPortal.Projects.Open(new FileInfo(projectPath));

                if (project != null)
                {
                    // 加载项目数据
                    LoadProjectData();
                    treeViewProject.ExpandAll();

                    MessageBox.Show($"项目 '{project.Name}' 已成功打开。", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开项目时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        private void LoadProjectData()
        {
            try
            {
                // 清空现有节点
                treeViewProject.Nodes.Clear();

                // 创建根节点
                TreeNode rootNode = new TreeNode(project.Name);
                treeViewProject.Nodes.Add(rootNode);

                // 遍历项目中的设备
                var devices = project.Devices.ToArray();
                foreach (Device device in devices)
                {
                    // 遍历设备中的设备项
                    var deviceItems = device.DeviceItems.ToArray();
                    foreach (DeviceItem deviceItem in deviceItems)
                    {
                        // 查找包含软件的设备项（如PLC）
                        SoftwareContainer softwareContainer = deviceItem.GetService<SoftwareContainer>();
                        if (softwareContainer != null)
                        {
                            // 获取PLC软件
                            PlcSoftware plcSoftware = softwareContainer.Software as PlcSoftware;
                            if (plcSoftware != null)
                            {
                                // 创建此PLC的节点
                                string plcName = plcSoftware.Name;
                                TreeNode plcNode = new TreeNode(plcName);
                                rootNode.Nodes.Add(plcNode);

                                // 为此PLC创建子节点
                                TreeNode plcProgramLibraryNode = new TreeNode("程序库");
                                TreeNode plcTagTablesNode = new TreeNode("变量表");
                                TreeNode plcDataTypesNode = new TreeNode("PLC数据类型");

                                plcNode.Nodes.Add(plcProgramLibraryNode);
                                plcNode.Nodes.Add(plcTagTablesNode);
                                plcNode.Nodes.Add(plcDataTypesNode);

                                // 加载此PLC的数据
                                LoadProgramBlocks(plcProgramLibraryNode, plcSoftware.BlockGroup);
                                LoadTagTables(plcTagTablesNode, plcSoftware.TagTableGroup);
                                LoadPlcDataTypes(plcDataTypesNode, plcSoftware.TypeGroup);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载项目数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadProgramBlocks(TreeNode parentNode, PlcBlockGroup blockGroup)
        {
            try
            {
                // 添加当前组中的程序块
                var blocks = blockGroup.Blocks.ToArray();
                foreach (PlcBlock block in blocks)
                {
                    TreeNode blockNode = new TreeNode(block.Name);
                    blockNode.Tag = block; // 保存对象引用以便后续使用
                    parentNode.Nodes.Add(blockNode);
                }

                // 递归处理子组
                var groups = blockGroup.Groups.ToArray();
                foreach (PlcBlockGroup subGroup in groups)
                {
                    TreeNode groupNode = new TreeNode(subGroup.Name);
                    parentNode.Nodes.Add(groupNode);
                    LoadProgramBlocks(groupNode, subGroup);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载程序块时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadTagTables(TreeNode parentNode, PlcTagTableGroup tagTableGroup)
        {
            try
            {
                // 添加当前组中的变量表
                var tagTables = tagTableGroup.TagTables.ToArray();
                foreach (PlcTagTable tagTable in tagTables)
                {
                    TreeNode tagTableNode = new TreeNode(tagTable.Name);
                    tagTableNode.Tag = tagTable; // 保存对象引用以便后续使用
                    parentNode.Nodes.Add(tagTableNode);
                }

                // 递归处理子组
                var groups = tagTableGroup.Groups.ToArray();
                foreach (PlcTagTableGroup subGroup in groups)
                {
                    TreeNode groupNode = new TreeNode(subGroup.Name);
                    parentNode.Nodes.Add(groupNode);
                    LoadTagTables(groupNode, subGroup);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载变量表时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadPlcDataTypes(TreeNode parentNode, PlcTypeGroup typeGroup)
        {
            try
            {
                // 添加当前组中的PLC数据类型
                var types = typeGroup.Types.ToArray();
                foreach (PlcType plcType in types)
                {
                    TreeNode typeNode = new TreeNode(plcType.Name);
                    typeNode.Tag = plcType; // 保存对象引用以便后续使用
                    parentNode.Nodes.Add(typeNode);
                }

                // 递归处理子组
                var groups = typeGroup.Groups.ToArray();
                foreach (PlcTypeGroup subGroup in groups)
                {
                    TreeNode groupNode = new TreeNode(subGroup.Name);
                    parentNode.Nodes.Add(groupNode);
                    LoadPlcDataTypes(groupNode, subGroup);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载PLC数据类型时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void treeViewProject_AfterSelect(object sender, TreeViewEventArgs e)
        {
            // 当选择树节点时更新按钮状态
            UpdateExportButtonState();
        }

        private void UpdateExportButtonState()
        {
            if (treeViewProject.SelectedNode == null)
            {
                btnExport.Enabled = false;
                return;
            }

            TreeNode selectedNode = treeViewProject.SelectedNode;

            // 检查节点是否有Tag属性（即是否是实际对象）
            if (selectedNode.Tag != null)
            {
                btnExport.Enabled = true;

                // 根据选中的节点类型设置导出按钮文本
                if (selectedNode.Tag is PlcBlock || selectedNode.Tag is PlcType)
                {
                    btnExport.Text = "导出为XML";
                }
                else if (selectedNode.Tag is PlcTagTable)
                {
                    btnExport.Text = "导出为Excel";
                }
                else
                {
                    btnExport.Enabled = false;
                }
            }
            else
            {
                btnExport.Enabled = false;
            }
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            if (treeViewProject.SelectedNode == null || treeViewProject.SelectedNode.Tag == null)
                return;

            TreeNode selectedNode = treeViewProject.SelectedNode;

            try
            {
                if (selectedNode.Tag is PlcBlock || selectedNode.Tag is PlcType)
                {
                    ExportToXML(selectedNode);
                }
                else if (selectedNode.Tag is PlcTagTable)
                {
                    ExportToExcel(selectedNode);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToXML(TreeNode node)
        {
            using (SaveFileDialog saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "XML文件 (*.xml)|*.xml";
                saveFileDialog.Title = $"导出 {node.Text} 为XML";
                saveFileDialog.FileName = $"{node.Text}.xml";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string filePath = saveFileDialog.FileName;

                    Cursor = Cursors.WaitCursor;
                    try
                    {
                        // 创建临时导出目录
                        string tempDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
                        Directory.CreateDirectory(tempDir);
                        string tempFile = Path.Combine(tempDir, $"{node.Text}.xml");
                        FileInfo xmlFileInfo = new FileInfo(tempFile);

                        if (node.Tag is PlcBlock)
                        {
                            // 导出程序块到XML
                            PlcBlock block = node.Tag as PlcBlock;
                            try
                            {
                                // 尝试使用不同的导出选项
                                try
                                {
                                    block.Export(xmlFileInfo, ExportOptions.WithDefaults | ExportOptions.WithReadOnly);
                                }
                                catch (Exception)
                                {
                                    // 如果失败，使用其他选项组合尝试
                                    try
                                    {
                                        block.Export(xmlFileInfo, ExportOptions.None);
                                    }
                                    catch
                                    {
                                        // 最后尝试
                                        block.Export(xmlFileInfo, ExportOptions.WithDefaults);
                                    }
                                }

                                // 复制到用户指定位置
                                File.Copy(tempFile, filePath, true);
                                MessageBox.Show($"{node.Text} 已成功导出为XML文件。", "导出成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show($"导出程序块失败: {ex.Message}\n\n请确保程序块已编译且状态一致。如果程序块有错误，请先修复后再导出。",
                                    "导出错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        else if (node.Tag is PlcType)
                        {
                            // 导出PLC数据类型到XML
                            PlcType plcType = node.Tag as PlcType;
                            try
                            {
                                // 尝试使用不同的导出选项
                                try
                                {
                                    plcType.Export(xmlFileInfo, ExportOptions.WithDefaults | ExportOptions.WithReadOnly);
                                }
                                catch (Exception)
                                {
                                    // 如果失败，使用其他选项组合尝试
                                    try
                                    {
                                        plcType.Export(xmlFileInfo, ExportOptions.None);
                                    }
                                    catch
                                    {
                                        // 最后尝试
                                        plcType.Export(xmlFileInfo, ExportOptions.WithDefaults);
                                    }
                                }

                                // 复制到用户指定位置
                                File.Copy(tempFile, filePath, true);
                                MessageBox.Show($"{node.Text} 已成功导出为XML文件。", "导出成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show($"导出PLC数据类型失败: {ex.Message}\n\n请确保数据类型已编译且状态一致。如果数据类型有错误，请先修复后再导出。",
                                    "导出错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }

                        // 清理临时文件
                        try
                        {
                            Directory.Delete(tempDir, true);
                        }
                        catch { /* 忽略清理错误 */ }
                    }
                    finally
                    {
                        Cursor = Cursors.Default;
                    }
                }
            }
        }

        private void ExportToExcel(TreeNode node)
        {
            using (SaveFileDialog saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "Excel文件 (*.xlsx)|*.xlsx";
                saveFileDialog.Title = $"导出 {node.Text} 为Excel";
                saveFileDialog.FileName = $"{node.Text}.xlsx";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string filePath = saveFileDialog.FileName;
                    Cursor = Cursors.WaitCursor;

                    try
                    {
                        if (node.Tag is PlcTagTable)
                        {
                            ExportTagTableToExcel(node.Tag as PlcTagTable, filePath);
                        }
                        else if (node.Tag is PlcType)
                        {
                            ExportPlcTypeToExcel(node.Tag as PlcType, filePath);
                        }

                        MessageBox.Show($"{node.Text} 已成功导出为Excel文件。", "导出成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    finally
                    {
                        Cursor = Cursors.Default;
                    }
                }
            }
        }

        private void ExportTagTableToExcel(PlcTagTable tagTable, string filePath)
        {
            // 创建新的Excel工作簿
            var workbook = new XSSFWorkbook();
            var sheet = workbook.CreateSheet("变量表");

            // 创建标题行
            var headerRow = sheet.CreateRow(0);
            headerRow.CreateCell(0).SetCellValue("名称");
            headerRow.CreateCell(1).SetCellValue("数据类型");
            headerRow.CreateCell(2).SetCellValue("地址");
            headerRow.CreateCell(3).SetCellValue("注释");

            // 填充数据
            int rowIndex = 1;
            foreach (PlcTag tag in tagTable.Tags)
            {
                var row = sheet.CreateRow(rowIndex++);
                row.CreateCell(0).SetCellValue(tag.Name);
                row.CreateCell(1).SetCellValue(tag.DataTypeName);
                row.CreateCell(2).SetCellValue(tag.LogicalAddress);

                // 修复MultilingualText处理
                string comment = "";
                if (tag.Comment != null && tag.Comment.Items.Count > 0)
                {
                    // 尝试获取当前文化的注释，如果没有则获取第一个
                    string currentCulture = CultureInfo.CurrentCulture.Name;
                    bool found = false;

                    foreach (var item in tag.Comment.Items)
                    {
                        if (item.Language.Culture.Name == currentCulture)
                        {
                            comment = item.Text;
                            found = true;
                            break;
                        }
                    }

                    if (!found && tag.Comment.Items.Count > 0)
                    {
                        comment = tag.Comment.Items[0].Text;
                    }
                }

                row.CreateCell(3).SetCellValue(comment);
            }

            // 调整列宽
            for (int i = 0; i < 4; i++)
            {
                sheet.AutoSizeColumn(i);
            }

            // 保存工作簿
            using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(fileStream);
            }
        }

        private void ExportPlcTypeToExcel(PlcType plcType, string filePath)
        {
            // 创建新的Excel工作簿
            var workbook = new XSSFWorkbook();
            var sheet = workbook.CreateSheet("PLC数据类型");

            // 创建标题行
            var headerRow = sheet.CreateRow(0);
            headerRow.CreateCell(0).SetCellValue("名称");
            headerRow.CreateCell(1).SetCellValue("数据类型");
            headerRow.CreateCell(2).SetCellValue("偏移量");
            headerRow.CreateCell(3).SetCellValue("注释");

            // 保存工作簿
            using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(fileStream);
            }
        }

        private void btnImportXML_Click(object sender, EventArgs e)
        {
            if (project == null)
            {
                MessageBox.Show("请先打开一个项目。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 检查是否选中了目标组
            TreeNode selectedNode = treeViewProject.SelectedNode;
            if (selectedNode == null)
            {
                MessageBox.Show("请选中待导入的组目录。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 检查选中节点是什么类型
            PlcBlockGroup targetBlockGroup = null;
            PlcSoftware plcSoftware = null;
            string targetNodeName = "";

            // 确定目标组和PLC
            if (IsBlockGroupNode(selectedNode, out targetBlockGroup, out plcSoftware))
            {
                targetNodeName = selectedNode.Text;
            }
            // 如果选中的是程序块，则使用其父组作为目标
            else if (selectedNode.Tag is PlcBlock && selectedNode.Parent != null)
            {
                TreeNode parentNode = selectedNode.Parent;
                if (IsBlockGroupNode(parentNode, out targetBlockGroup, out plcSoftware))
                {
                    targetNodeName = parentNode.Text;
                }
            }
            else
            {
                MessageBox.Show("请选中程序块组或程序块。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (targetBlockGroup == null || plcSoftware == null)
            {
                MessageBox.Show("无法确定目标导入位置。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "XML文件 (*.xml)|*.xml";
                openFileDialog.Title = "选择要导入的XML文件";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        string filePath = openFileDialog.FileName;
                        Cursor = Cursors.WaitCursor;

                        // 导入XML文件到目标程序块组
                        FileInfo xmlFileInfo = new FileInfo(filePath);

                        // 使用Import方法导入XML
                        targetBlockGroup.Blocks.Import(xmlFileInfo, ImportOptions.Override);

                        // 提示用户
                        try
                        {
                            MessageBox.Show($"导入完成，XML文件已导入到\"{targetNodeName}\"组。请在TIA Portal中手动编译。",
                                "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        catch
                        {
                            MessageBox.Show("无法编译导入的程序块，可能需要在TIA Portal中手动编译。",
                                "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }

                        // 刷新树视图
                        LoadProjectData();
                        treeViewProject.ExpandAll();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"导入XML失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    finally
                    {
                        Cursor = Cursors.Default;
                    }
                }
            }
        }

        private void btnImportExcel_Click(object sender, EventArgs e)
        {
            if (project == null)
            {
                MessageBox.Show("请先打开一个项目。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 检查是否选中了目标组
            TreeNode selectedNode = treeViewProject.SelectedNode;
            if (selectedNode == null)
            {
                MessageBox.Show("请选中待导入的组目录。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 检查选中节点是什么类型
            PlcTagTableGroup targetTagTableGroup = null;
            PlcTypeGroup targetTypeGroup = null;
            PlcSoftware plcSoftware = null;
            string targetNodeName = "";
            bool isTagTable = false;

            // 确定目标组和PLC
            if (IsTagTableGroupNode(selectedNode, out targetTagTableGroup, out plcSoftware))
            {
                targetNodeName = selectedNode.Text;
                isTagTable = true;
            }
            // 如果选中的是变量表，则使用其父组作为目标
            else if (selectedNode.Tag is PlcTagTable && selectedNode.Parent != null)
            {
                TreeNode parentNode = selectedNode.Parent;
                if (IsTagTableGroupNode(parentNode, out targetTagTableGroup, out plcSoftware))
                {
                    targetNodeName = parentNode.Text;
                    isTagTable = true;
                }
            }
            // 如果是PLC数据类型组
            else if (IsTypeGroupNode(selectedNode, out targetTypeGroup, out plcSoftware))
            {
                targetNodeName = selectedNode.Text;
                isTagTable = false;
            }
            // 如果选中的是PLC数据类型，则使用其父组作为目标
            else if (selectedNode.Tag is PlcType && selectedNode.Parent != null)
            {
                TreeNode parentNode = selectedNode.Parent;
                if (IsTypeGroupNode(parentNode, out targetTypeGroup, out plcSoftware))
                {
                    targetNodeName = parentNode.Text;
                    isTagTable = false;
                }
            }
            else
            {
                MessageBox.Show("请选中变量表组、变量表、PLC数据类型组或PLC数据类型。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if ((isTagTable && (targetTagTableGroup == null || plcSoftware == null)) ||
                (!isTagTable && (targetTypeGroup == null || plcSoftware == null)))
            {
                MessageBox.Show("无法确定目标导入位置。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Excel文件 (*.xlsx)|*.xlsx|Excel 97-2003 (*.xls)|*.xls";
                openFileDialog.Title = "选择要导入的Excel文件";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        string filePath = openFileDialog.FileName;
                        Cursor = Cursors.WaitCursor;

                        if (isTagTable)
                        {
                            // 导入为变量表到指定组
                            ImportExcelAsTagTable(filePath, plcSoftware, targetTagTableGroup);
                            MessageBox.Show($"Excel文件已成功导入到\"{targetNodeName}\"变量表组。", "导入成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            // 导入为PLC数据类型到指定组
                            ImportExcelAsPlcDataType(filePath, plcSoftware, targetTypeGroup);
                            MessageBox.Show($"Excel文件已成功导入到\"{targetNodeName}\"PLC数据类型组。", "导入成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }

                        // 刷新树视图
                        LoadProjectData();
                        treeViewProject.ExpandAll();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"导入Excel失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    finally
                    {
                        Cursor = Cursors.Default;
                    }
                }
            }
        }

        /// <summary>
        /// 判断节点是否为程序块组节点
        /// </summary>
        private bool IsBlockGroupNode(TreeNode node, out PlcBlockGroup blockGroup, out PlcSoftware plcSoftware)
        {
            blockGroup = null;
            plcSoftware = null;

            // 首先判断是否为程序块组节点（查找特定的路径结构）
            TreeNode currentNode = node;
            TreeNode programLibraryNode = null;
            TreeNode plcNode = null;

            // 查找"程序库"节点
            while (currentNode != null)
            {
                if (currentNode.Text == "程序库")
                {
                    programLibraryNode = currentNode;
                    if (currentNode.Parent != null)
                    {
                        plcNode = currentNode.Parent;
                    }
                    break;
                }
                currentNode = currentNode.Parent;
            }

            if (programLibraryNode == null || plcNode == null)
                return false;

            // 获取PLC
            plcSoftware = GetPlcSoftwareFromNode(plcNode);
            if (plcSoftware == null)
                return false;

            // 从当前节点回溯到程序库节点，构建组路径
            currentNode = node;
            var groupPath = new List<string>();

            while (currentNode != programLibraryNode && currentNode != null)
            {
                groupPath.Insert(0, currentNode.Text);
                currentNode = currentNode.Parent;
            }

            // 如果找到了有效路径，则获取对应的程序块组
            if (groupPath.Count > 0)
            {
                blockGroup = plcSoftware.BlockGroup;

                // 沿路径查找组
                foreach (string groupName in groupPath)
                {
                    var found = false;
                    var groups = blockGroup.Groups.ToArray();
                    foreach (var group in groups)
                    {
                        if (group.Name == groupName)
                        {
                            blockGroup = group;
                            found = true;
                            break;
                        }
                    }

                    if (!found)
                        return false;
                }

                return true;
            }

            // 如果是程序库节点本身，返回根程序块组
            if (node == programLibraryNode)
            {
                blockGroup = plcSoftware.BlockGroup;
                return true;
            }

            return false;
        }

        /// <summary>
        /// 判断节点是否为变量表组节点
        /// </summary>
        private bool IsTagTableGroupNode(TreeNode node, out PlcTagTableGroup tagTableGroup, out PlcSoftware plcSoftware)
        {
            tagTableGroup = null;
            plcSoftware = null;

            // 首先判断是否为变量表组节点（查找特定的路径结构）
            TreeNode currentNode = node;
            TreeNode tagTablesNode = null;
            TreeNode plcNode = null;

            // 查找"变量表"节点
            while (currentNode != null)
            {
                if (currentNode.Text == "变量表")
                {
                    tagTablesNode = currentNode;
                    if (currentNode.Parent != null)
                    {
                        plcNode = currentNode.Parent;
                    }
                    break;
                }
                currentNode = currentNode.Parent;
            }

            if (tagTablesNode == null || plcNode == null)
                return false;

            // 获取PLC
            plcSoftware = GetPlcSoftwareFromNode(plcNode);
            if (plcSoftware == null)
                return false;

            // 从当前节点回溯到变量表节点，构建组路径
            currentNode = node;
            var groupPath = new List<string>();

            while (currentNode != tagTablesNode && currentNode != null)
            {
                groupPath.Insert(0, currentNode.Text);
                currentNode = currentNode.Parent;
            }

            // 如果找到了有效路径，则获取对应的变量表组
            if (groupPath.Count > 0)
            {
                tagTableGroup = plcSoftware.TagTableGroup;

                // 沿路径查找组
                foreach (string groupName in groupPath)
                {
                    var found = false;
                    var groups = tagTableGroup.Groups.ToArray();
                    foreach (var group in groups)
                    {
                        if (group.Name == groupName)
                        {
                            tagTableGroup = group;
                            found = true;
                            break;
                        }
                    }

                    if (!found)
                        return false;
                }

                return true;
            }

            // 如果是变量表节点本身，返回根变量表组
            if (node == tagTablesNode)
            {
                tagTableGroup = plcSoftware.TagTableGroup;
                return true;
            }

            return false;
        }

        /// <summary>
        /// 判断节点是否为PLC数据类型组节点
        /// </summary>
        private bool IsTypeGroupNode(TreeNode node, out PlcTypeGroup typeGroup, out PlcSoftware plcSoftware)
        {
            typeGroup = null;
            plcSoftware = null;

            // 首先判断是否为PLC数据类型组节点（查找特定的路径结构）
            TreeNode currentNode = node;
            TreeNode plcDataTypesNode = null;
            TreeNode plcNode = null;

            // 查找"PLC数据类型"节点
            while (currentNode != null)
            {
                if (currentNode.Text == "PLC数据类型")
                {
                    plcDataTypesNode = currentNode;
                    if (currentNode.Parent != null)
                    {
                        plcNode = currentNode.Parent;
                    }
                    break;
                }
                currentNode = currentNode.Parent;
            }

            if (plcDataTypesNode == null || plcNode == null)
                return false;

            // 获取PLC
            plcSoftware = GetPlcSoftwareFromNode(plcNode);
            if (plcSoftware == null)
                return false;

            // 从当前节点回溯到PLC数据类型节点，构建组路径
            currentNode = node;
            var groupPath = new List<string>();

            while (currentNode != plcDataTypesNode && currentNode != null)
            {
                groupPath.Insert(0, currentNode.Text);
                currentNode = currentNode.Parent;
            }

            // 如果找到了有效路径，则获取对应的PLC数据类型组
            if (groupPath.Count > 0)
            {
                typeGroup = plcSoftware.TypeGroup;

                // 沿路径查找组
                foreach (string groupName in groupPath)
                {
                    var found = false;
                    var groups = typeGroup.Groups.ToArray();
                    foreach (var group in groups)
                    {
                        if (group.Name == groupName)
                        {
                            typeGroup = group;
                            found = true;
                            break;
                        }
                    }

                    if (!found)
                        return false;
                }

                return true;
            }

            // 如果是PLC数据类型节点本身，返回根PLC数据类型组
            if (node == plcDataTypesNode)
            {
                typeGroup = plcSoftware.TypeGroup;
                return true;
            }

            return false;
        }

        /// <summary>
        /// 将Excel导入为变量表到指定组
        /// </summary>
        private void ImportExcelAsTagTable(string filePath, PlcSoftware plcSoftware, PlcTagTableGroup targetGroup)
        {
            // 读取Excel文件
            using (var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                IWorkbook workbook;
                if (Path.GetExtension(filePath).ToLower() == ".xlsx")
                {
                    workbook = new XSSFWorkbook(fs);
                }
                else
                {
                    workbook = new HSSFWorkbook(fs);
                }

                ISheet sheet = workbook.GetSheetAt(0);

                // 创建新的变量表
                string tagTableName = Path.GetFileNameWithoutExtension(filePath);
                PlcTagTable tagTable = targetGroup.TagTables.Create(tagTableName);

                // 遍历Excel数据行，从第二行开始（跳过标题行）
                for (int i = 1; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null) continue;

                    // 读取单元格数据
                    string name = row.GetCell(0)?.StringCellValue?.Trim();
                    string dataType = row.GetCell(1)?.StringCellValue?.Trim();
                    string address = row.GetCell(2)?.StringCellValue?.Trim();
                    string comment = row.GetCell(3)?.StringCellValue?.Trim();

                    if (string.IsNullOrEmpty(name)) continue;

                    // 创建新的PLC变量
                    try
                    {
                        PlcTag tag = tagTable.Tags.Create(name, dataType, address);
                        if (!string.IsNullOrEmpty(comment))
                        {
                            // 处理多语言文本
                            try
                            {
                                // 创建语言列表的副本，防止枚举过程中被修改
                                var languages = project.LanguageSettings.Languages.ToArray();
                                foreach (var lang in languages)
                                {
                                    // 查找匹配当前文化的语言
                                    if (lang.Culture.Name == CultureInfo.CurrentCulture.Name)
                                    {
                                        // 使用Find方法查找语言对应的文本项
                                        var textItem = tag.Comment.Items.Find(lang);
                                        if (textItem != null)
                                        {
                                            // 如果已存在，则更新文本
                                            textItem.Text = comment;
                                        }
                                        else
                                        {
                                            // 如果不存在，则尝试使用其他方式创建
                                            try
                                            {
                                                var method = tag.Comment.Items.GetType().GetMethod("Create");
                                                if (method != null)
                                                {
                                                    var newTextItem = method.Invoke(tag.Comment.Items, new object[] { lang });
                                                    if (newTextItem != null)
                                                    {
                                                        var textProperty = newTextItem.GetType().GetProperty("Text");
                                                        if (textProperty != null)
                                                        {
                                                            textProperty.SetValue(newTextItem, comment);
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    // 如果没有Create方法，记录警告
                                                    MessageBox.Show($"无法为变量 {name} 添加注释，API不支持Create方法。", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                                                }
                                            }
                                            catch
                                            {
                                                MessageBox.Show($"为变量 {name} 添加注释时发生错误。", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                                            }
                                        }
                                        break;
                                    }
                                }
                            }
                            catch (Exception commentEx)
                            {
                                MessageBox.Show($"添加注释时发生错误: {commentEx.Message}", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"创建变量 {name} 时发生错误: {ex.Message}", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
        }

        /// <summary>
        /// 将Excel导入为PLC数据类型到指定组
        /// </summary>
        private void ImportExcelAsPlcDataType(string filePath, PlcSoftware plcSoftware, PlcTypeGroup targetGroup)
        {
            // 简化版，实际实现需要根据西门子Openness API文档详细了解PLC数据类型的创建方法
            MessageBox.Show($"导入Excel为PLC数据类型功能需要根据实际API文档实现。已选择目标组：{targetGroup.Name}", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnQuickOpenProject_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "博图项目文件 (*.ap*)|*.ap*";
                openFileDialog.Title = "选择博图项目文件";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string projectPath = openFileDialog.FileName;
                    Cursor = Cursors.WaitCursor;

                    try
                    {
                        // 1. 检查是否已有打开的TIA Portal实例
                        var tiaPortalProcesses = TiaPortal.GetProcesses().ToArray();

                        // 2. 如果有已打开的实例，则连接到它
                        if (tiaPortalProcesses.Length > 0)
                        {
                            tiaPortal = tiaPortalProcesses[0].Attach();
                            MessageBox.Show("已连接到现有的博图V18实例。", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        // 3. 如果没有，则启动一个新实例
                        else
                        {
                            tiaPortal = new TiaPortal(TiaPortalMode.WithUserInterface);
                            Process.Start(tiaPortalPath, "/Open");
                            MessageBox.Show("博图V18已启动。", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            // 等待TIA Portal启动完成
                            Thread.Sleep(3000);
                        }

                        // 4. 打开项目
                        OpenProject(projectPath);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"快速打开项目失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    finally
                    {
                        Cursor = Cursors.Default;
                    }
                }
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            if (project == null)
            {
                MessageBox.Show("请先打开一个项目。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            Cursor = Cursors.WaitCursor;
            try
            {
                // 刷新项目结构
                LoadProjectData();
                treeViewProject.ExpandAll();
                MessageBox.Show("项目结构已刷新。", "刷新成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"刷新项目结构失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// 从节点获取对应的PlcSoftware对象
        /// </summary>
        private PlcSoftware GetPlcSoftwareFromNode(TreeNode node)
        {
            if (project == null)
                return null;

            string plcName = node.Text;

            // 遍历项目中的设备寻找匹配的PLC
            var devices = project.Devices.ToArray();
            foreach (Device device in devices)
            {
                var deviceItems = device.DeviceItems.ToArray();
                foreach (DeviceItem deviceItem in deviceItems)
                {
                    SoftwareContainer softwareContainer = deviceItem.GetService<SoftwareContainer>();
                    if (softwareContainer != null)
                    {
                        PlcSoftware plcSoftware = softwareContainer.Software as PlcSoftware;
                        if (plcSoftware != null && plcSoftware.Name == plcName)
                        {
                            return plcSoftware;
                        }
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 选择手动行Excel或CSV文件
        /// </summary>
        private void btnSelectManualRowExcel_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Excel或CSV文件 (*.xlsx;*.xls;*.csv)|*.xlsx;*.xls;*.csv|Excel文件 (*.xlsx;*.xls)|*.xlsx;*.xls|CSV文件 (*.csv)|*.csv";
                openFileDialog.Title = "选择手动行Excel或CSV文件";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    manualRowExcelPath = openFileDialog.FileName;
                    lblManualRowExcelPath.Text = "已选择: " + Path.GetFileName(manualRowExcelPath);

                    // 在开发过程中分析文件结构，发布时可以注释掉这行
                    // if (Path.GetExtension(manualRowExcelPath).ToLower() == ".csv")
                    // {
                    //     AnalyzeCsvFile(manualRowExcelPath);
                    // }
                    // else
                    // {
                    //     AnalyzeExcelFile(manualRowExcelPath);
                    // }
                }
            }
        }

        /// <summary>
        /// 生成手动行FB块
        /// </summary>
        private void btnGenerateManualRowFB_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(manualRowExcelPath))
            {
                MessageBox.Show("请先选择手动行Excel文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (project == null)
            {
                MessageBox.Show("请先打开一个项目", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 检查是否选中了目标组
            TreeNode selectedNode = treeViewProject.SelectedNode;
            if (selectedNode == null)
            {
                MessageBox.Show("请选中待导入的程序块组或程序块", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 检查选中节点是什么类型
            PlcBlockGroup targetBlockGroup = null;
            PlcSoftware plcSoftware = null;

            // 确定目标组和PLC
            if (IsBlockGroupNode(selectedNode, out targetBlockGroup, out plcSoftware))
            {
                // 已找到目标组
            }
            // 如果选中的是程序块，则使用其父组作为目标
            else if (selectedNode.Tag is PlcBlock && selectedNode.Parent != null)
            {
                TreeNode parentNode = selectedNode.Parent;
                if (IsBlockGroupNode(parentNode, out targetBlockGroup, out plcSoftware))
                {
                    // 已找到目标组
                }
            }
            else
            {
                MessageBox.Show("请选中程序块组或程序块", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (targetBlockGroup == null || plcSoftware == null)
            {
                MessageBox.Show("无法确定目标导入位置", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                Cursor = Cursors.WaitCursor;

                // 根据Excel文件名确定对应的XML文件名
                string excelFileName = Path.GetFileNameWithoutExtension(manualRowExcelPath);
                string xmlFileName = DetermineXmlFileName(excelFileName);

                if (string.IsNullOrEmpty(xmlFileName))
                {
                    MessageBox.Show("无法确定对应的XML文件名", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 生成XML文件
                string xmlFilePath = GenerateXmlFromExcel(manualRowExcelPath, xmlFileName);

                if (string.IsNullOrEmpty(xmlFilePath))
                {
                    MessageBox.Show("生成XML文件失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 导入XML文件到目标程序块组
                FileInfo xmlFileInfo = new FileInfo(xmlFilePath);
                targetBlockGroup.Blocks.Import(xmlFileInfo, ImportOptions.Override);

                // 提示用户
                MessageBox.Show($"手动行FB块已成功生成并导入", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // 刷新树视图
                LoadProjectData();
                treeViewProject.ExpandAll();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成手动行FB块失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// 根据Excel文件名确定对应的XML文件名
        /// </summary>
        private string DetermineXmlFileName(string excelFileName)
        {
            // 固定使用"xml模板.xml"作为模板文件
            return "xml模板";
        }

        /// <summary>
        /// 读取并分析Excel文件的内容，以便更好地理解其结构
        /// </summary>
        private void AnalyzeExcelFile(string excelFilePath)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"Excel文件分析: {Path.GetFileName(excelFilePath)}");
                sb.AppendLine("----------------------------------------");

                using (var fs = new FileStream(excelFilePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook;
                    if (Path.GetExtension(excelFilePath).ToLower() == ".xlsx")
                    {
                        workbook = new XSSFWorkbook(fs);
                    }
                    else
                    {
                        workbook = new HSSFWorkbook(fs);
                    }

                    ISheet sheet = workbook.GetSheetAt(0);
                    sb.AppendLine($"工作表名称: {sheet.SheetName}");
                    sb.AppendLine($"总行数: {sheet.LastRowNum + 1}");

                    // 获取表头信息
                    IRow headerRow = sheet.GetRow(0);
                    if (headerRow != null)
                    {
                        sb.AppendLine("\n表头信息:");
                        for (int i = 0; i < headerRow.LastCellNum; i++)
                        {
                            ICell cell = headerRow.GetCell(i);
                            if (cell != null)
                            {
                                sb.AppendLine($"  列 {i+1}: {cell.StringCellValue}");
                            }
                        }
                    }

                    // 获取前5行数据作为示例
                    sb.AppendLine("\n数据示例 (前5行):");
                    for (int i = 1; i <= Math.Min(5, sheet.LastRowNum); i++)
                    {
                        IRow row = sheet.GetRow(i);
                        if (row != null)
                        {
                            sb.Append($"  行 {i+1}: ");
                            for (int j = 0; j < headerRow.LastCellNum; j++)
                            {
                                ICell cell = row.GetCell(j);
                                if (cell != null)
                                {
                                    sb.Append($"[{cell.ToString()}] ");
                                }
                                else
                                {
                                    sb.Append("[] ");
                                }
                            }
                            sb.AppendLine();
                        }
                    }

                    MessageBox.Show(sb.ToString(), $"Excel文件分析: {Path.GetFileName(excelFilePath)}", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"分析Excel文件时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 读取并分析CSV文件的内容，以便更好地理解其结构
        /// </summary>
        private void AnalyzeCsvFile(string csvFilePath)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"CSV文件分析: {Path.GetFileName(csvFilePath)}");
                sb.AppendLine("----------------------------------------");

                // 读取CSV文件
                string[] lines = File.ReadAllLines(csvFilePath, Encoding.UTF8);
                sb.AppendLine($"总行数: {lines.Length}");

                // 获取表头信息
                if (lines.Length > 0)
                {
                    string[] headers = lines[0].Split(',');
                    sb.AppendLine("\n表头信息:");
                    for (int i = 0; i < headers.Length; i++)
                    {
                        sb.AppendLine($"  列 {i+1}: {headers[i]}");
                    }

                    // 获取前5行数据作为示例
                    sb.AppendLine("\n数据示例 (前5行):");
                    for (int i = 1; i < Math.Min(6, lines.Length); i++)
                    {
                        string[] cells = lines[i].Split(',');
                        sb.Append($"  行 {i+1}: ");
                        foreach (string cell in cells)
                        {
                            sb.Append($"[{cell}] ");
                        }
                        sb.AppendLine();
                    }
                }

                MessageBox.Show(sb.ToString(), $"CSV文件分析: {Path.GetFileName(csvFilePath)}", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"分析CSV文件时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 根据Excel或CSV生成XML文件
        /// </summary>
        private string GenerateXmlFromExcel(string filePath, string xmlFileName)
        {
            try
            {
                // 创建临时目录用于存放生成的XML文件
                string tempDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
                Directory.CreateDirectory(tempDir);
                string xmlFilePath = Path.Combine(tempDir, xmlFileName + ".xml");

                // 查找执行文件目录下的Templates子目录中的XML模板文件
                string executableDir = Path.GetDirectoryName(Application.ExecutablePath);
                string templatesDir = Path.Combine(executableDir, "Templates");

                // 确保Templates目录存在
                if (!Directory.Exists(templatesDir))
                {
                    Directory.CreateDirectory(templatesDir);
                }

                string templateXmlPath = Path.Combine(templatesDir, xmlFileName + ".xml");

                if (!File.Exists(templateXmlPath))
                {
                    MessageBox.Show($"找不到XML模板文件: {xmlFileName}.xml", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return null;
                }

                // 复制模板文件到临时目录
                File.Copy(templateXmlPath, xmlFilePath, true);

                // 读取文件内容并修改XML文件
                ModifyXmlWithExcelData(xmlFilePath, filePath);

                return xmlFilePath;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成XML文件时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        /// <summary>
        /// 根据Excel数据修改XML文件
        /// </summary>
        private void ModifyXmlWithExcelData(string xmlFilePath, string excelFilePath)
        {
            try
            {
                // 读取Excel文件内容
                using (var fs = new FileStream(excelFilePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook;
                    if (Path.GetExtension(excelFilePath).ToLower() == ".xlsx")
                    {
                        workbook = new XSSFWorkbook(fs);
                    }
                    else
                    {
                        workbook = new HSSFWorkbook(fs);
                    }

                    ISheet sheet = workbook.GetSheetAt(0);

                    // 读取XML文件
                    XmlDocument xmlDoc = new XmlDocument();
                    xmlDoc.Load(xmlFilePath);

                    // 获取表头信息
                    IRow headerRow = sheet.GetRow(4); // 根据CSV文件分析，标题行在第5行（索引4）
                    if (headerRow == null)
                    {
                        throw new Exception("无法读取Excel文件的表头信息");
                    }

                    // 创建列名到列索引的映射
                    Dictionary<string, int> columnMap = new Dictionary<string, int>();
                    for (int i = 0; i < headerRow.LastCellNum; i++)
                    {
                        ICell cell = headerRow.GetCell(i);
                        if (cell != null && !string.IsNullOrEmpty(cell.StringCellValue))
                        {
                            columnMap[cell.StringCellValue.Trim()] = i;
                        }
                    }

                    // 检查必要的列
                    string[] requiredColumns = new string[] { "手动行号", "实例名称", "实例注释", "I点-工作位", "I点-原位", "Q点-工作位", "Q点-原位" };
                    foreach (string column in requiredColumns)
                    {
                        if (!columnMap.ContainsKey(column))
                        {
                            // 检查是否有替代列（如I点-工作位1、I点-工作位2等）
                            if (column == "I点-工作位" && (columnMap.ContainsKey("I点-工作位1") || columnMap.ContainsKey("I点-工作位2")))
                                continue;
                            if (column == "I点-原位" && (columnMap.ContainsKey("I点-原位1") || columnMap.ContainsKey("I点-原位2")))
                                continue;

                            throw new Exception($"表格缺少必要的列：{column}");
                        }
                    }

                    // 获取XML中的Static节点，这是我们需要添加成员的地方
                    XmlNamespaceManager nsManager = new XmlNamespaceManager(xmlDoc.NameTable);
                    nsManager.AddNamespace("ns", "http://www.siemens.com/automation/Openness/SW/Interface/v5");
                    XmlNode staticSection = xmlDoc.SelectSingleNode("//ns:Section[@Name='Static']", nsManager);

                    if (staticSection == null)
                    {
                        throw new Exception("无法在XML文件中找到Static节点");
                    }

                    // 清除现有的所有Member节点
                    List<XmlNode> nodesToRemove = new List<XmlNode>();
                    foreach (XmlNode node in staticSection.ChildNodes)
                    {
                        if (node.Name == "Member")
                        {
                            nodesToRemove.Add(node);
                        }
                    }

                    foreach (XmlNode node in nodesToRemove)
                    {
                        staticSection.RemoveChild(node);
                    }

                    // 获取OpMode信息（从第一行数据中获取）
                    IRow firstDataRow = sheet.GetRow(5); // 第一行数据在第6行（索引5）
                    string opModeName = "";
                    string opModeComment = "";
                    string opModeNumber = "";
                    string fbBlockName = "";
                    string fbBlockNumber = "";

                    if (firstDataRow != null)
                    {
                        if (columnMap.ContainsKey("OpMode名称"))
                            opModeName = firstDataRow.GetCell(columnMap["OpMode名称"])?.StringCellValue?.Trim() ?? "";
                        if (columnMap.ContainsKey("OpMode注释"))
                            opModeComment = firstDataRow.GetCell(columnMap["OpMode注释"])?.StringCellValue?.Trim() ?? "";
                        if (columnMap.ContainsKey("OpMode编号"))
                            opModeNumber = firstDataRow.GetCell(columnMap["OpMode编号"])?.StringCellValue?.Trim() ?? "";
                        if (columnMap.ContainsKey("手动FB块名称"))
                            fbBlockName = firstDataRow.GetCell(columnMap["手动FB块名称"])?.StringCellValue?.Trim() ?? "";
                        if (columnMap.ContainsKey("手动FB块编号"))
                            fbBlockNumber = firstDataRow.GetCell(columnMap["手动FB块编号"])?.StringCellValue?.Trim() ?? "";
                    }

                    // 更新XML文件中的OpMode信息
                    UpdateOpModeInfo(xmlDoc, opModeComment);

                    // 根据Excel数据添加新的Member节点
                    // 从第6行开始（索引5），跳过标题行
                    for (int i = 5; i <= sheet.LastRowNum; i++)
                    {
                        IRow row = sheet.GetRow(i);
                        if (row == null) continue;

                        // 读取单元格数据
                        string instanceName = "";
                        string instanceComment = "";
                        string iWrk = "";
                        string iBas = "";
                        string qWrk = "";
                        string qBas = "";
                        string iWrk1 = "";
                        string iWrk2 = "";
                        string iBas1 = "";
                        string iBas2 = "";

                        // 获取实例名称和注释
                        if (columnMap.ContainsKey("实例名称"))
                            instanceName = GetCellValueAsString(row.GetCell(columnMap["实例名称"])) ?? "";
                        if (columnMap.ContainsKey("实例注释"))
                            instanceComment = GetCellValueAsString(row.GetCell(columnMap["实例注释"])) ?? "";

                        // 获取I/O点信息
                        if (columnMap.ContainsKey("I点-工作位"))
                            iWrk = GetCellValueAsString(row.GetCell(columnMap["I点-工作位"])) ?? "";
                        else
                        {
                            if (columnMap.ContainsKey("I点-工作位1"))
                                iWrk1 = GetCellValueAsString(row.GetCell(columnMap["I点-工作位1"])) ?? "";
                            if (columnMap.ContainsKey("I点-工作位2"))
                                iWrk2 = GetCellValueAsString(row.GetCell(columnMap["I点-工作位2"])) ?? "";
                        }

                        if (columnMap.ContainsKey("I点-原位"))
                            iBas = GetCellValueAsString(row.GetCell(columnMap["I点-原位"])) ?? "";
                        else
                        {
                            if (columnMap.ContainsKey("I点-原位1"))
                                iBas1 = GetCellValueAsString(row.GetCell(columnMap["I点-原位1"])) ?? "";
                            if (columnMap.ContainsKey("I点-原位2"))
                                iBas2 = GetCellValueAsString(row.GetCell(columnMap["I点-原位2"])) ?? "";
                        }

                        if (columnMap.ContainsKey("Q点-工作位"))
                            qWrk = GetCellValueAsString(row.GetCell(columnMap["Q点-工作位"])) ?? "";
                        if (columnMap.ContainsKey("Q点-原位"))
                            qBas = GetCellValueAsString(row.GetCell(columnMap["Q点-原位"])) ?? "";

                        // 如果实例名称为空，跳过该行
                        if (string.IsNullOrEmpty(instanceName)) continue;

                        // 创建Member节点
                        XmlElement memberElement = xmlDoc.CreateElement("Member", staticSection.NamespaceURI);
                        memberElement.SetAttribute("Name", instanceName);
                        memberElement.SetAttribute("Datatype", $"\"FB4000_Valve\""); // 根据XML模板中的格式设置
                        memberElement.SetAttribute("Accessibility", "Public");

                        // 创建AttributeList节点
                        XmlElement attributeListElement = xmlDoc.CreateElement("AttributeList", staticSection.NamespaceURI);

                        // 添加各种属性
                        AddIntegerAttribute(xmlDoc, attributeListElement, "Offset", "0", true, true);
                        AddBooleanAttribute(xmlDoc, attributeListElement, "ExternalAccessible", "true", true);
                        AddBooleanAttribute(xmlDoc, attributeListElement, "ExternalVisible", "true", true);
                        AddBooleanAttribute(xmlDoc, attributeListElement, "ExternalWritable", "true", true);
                        AddBooleanAttribute(xmlDoc, attributeListElement, "UserVisible", "true", true, true);
                        AddBooleanAttribute(xmlDoc, attributeListElement, "UserReadOnly", "false", true, true);
                        AddBooleanAttribute(xmlDoc, attributeListElement, "UserDeletable", "true", true, true);
                        AddBooleanAttribute(xmlDoc, attributeListElement, "SetPoint", "true", true);

                        memberElement.AppendChild(attributeListElement);

                        // 添加注释
                        if (!string.IsNullOrEmpty(instanceComment))
                        {
                            XmlElement commentElement = xmlDoc.CreateElement("Comment", staticSection.NamespaceURI);

                            // 添加中文注释
                            XmlElement zhCNText = xmlDoc.CreateElement("MultiLanguageText", staticSection.NamespaceURI);
                            zhCNText.SetAttribute("Lang", "zh-CN");
                            zhCNText.InnerText = instanceComment;
                            commentElement.AppendChild(zhCNText);

                            // 添加英文注释
                            XmlElement enGBText = xmlDoc.CreateElement("MultiLanguageText", staticSection.NamespaceURI);
                            enGBText.SetAttribute("Lang", "en-GB");
                            enGBText.InnerText = instanceComment; // 使用中文注释作为英文注释
                            commentElement.AppendChild(enGBText);

                            memberElement.AppendChild(commentElement);
                        }

                        // 添加Sections节点，包含标准的Input部分
                        XmlElement sectionsElement = xmlDoc.CreateElement("Sections", staticSection.NamespaceURI);

                        // 添加Input部分
                        XmlElement inputSectionElement = xmlDoc.CreateElement("Section", staticSection.NamespaceURI);
                        inputSectionElement.SetAttribute("Name", "Input");

                        // 添加标准的输入成员
                        AddInputMember(xmlDoc, inputSectionElement, "I_WRK", "Bool", 0);
                        AddInputMember(xmlDoc, inputSectionElement, "I_BAS", "Bool", 1);
                        AddInputMember(xmlDoc, inputSectionElement, "FwdAuto", "Bool", 2);
                        AddInputMember(xmlDoc, inputSectionElement, "BkdAuto", "Bool", 3);
                        AddInputMember(xmlDoc, inputSectionElement, "VentValveAuto", "Bool", 4);
                        AddInputMember(xmlDoc, inputSectionElement, "FwdPmt", "Bool", 5);
                        AddInputMember(xmlDoc, inputSectionElement, "BkdPmt", "Bool", 6);
                        AddInputMember(xmlDoc, inputSectionElement, "ManualMod", "Bool", 7);
                        AddInputMember(xmlDoc, inputSectionElement, "AutoRunMod", "Bool", 8);
                        AddInputMember(xmlDoc, inputSectionElement, "E_StopStatus", "Bool", 9);
                        AddInputMember(xmlDoc, inputSectionElement, "ResetStatus", "Bool", 10);
                        AddInputMember(xmlDoc, inputSectionElement, "exFwdBtn", "Bool", 11);
                        AddInputMember(xmlDoc, inputSectionElement, "exBkdBtn", "Bool", 12);
                        AddInputMember(xmlDoc, inputSectionElement, "CmdClearFoolProofing", "Bool", 13);

                        sectionsElement.AppendChild(inputSectionElement);

                        // 添加Output部分
                        XmlElement outputSectionElement = xmlDoc.CreateElement("Section", staticSection.NamespaceURI);
                        outputSectionElement.SetAttribute("Name", "Output");

                        // 添加标准的输出成员
                        AddInputMember(xmlDoc, outputSectionElement, "Q_WRK", "Bool", 0);
                        AddInputMember(xmlDoc, outputSectionElement, "Q_BAS", "Bool", 1);

                        sectionsElement.AppendChild(outputSectionElement);
                        memberElement.AppendChild(sectionsElement);

                        // 将新创建的Member节点添加到Static节点
                        staticSection.AppendChild(memberElement);
                    }

                    // 保存修改后的XML文件
                    xmlDoc.Save(xmlFilePath);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"修改XML文件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新XML文件中的OpMode信息
        /// </summary>
        private void UpdateOpModeInfo(XmlDocument xmlDoc, string opModeComment)
        {
            try
            {
                // 查找文件末尾的Title节点
                XmlNodeList titleNodes = xmlDoc.GetElementsByTagName("MultilingualText");
                foreach (XmlNode titleNode in titleNodes)
                {
                    XmlAttribute compositionName = titleNode.Attributes["CompositionName"];
                    if (compositionName != null && compositionName.Value == "Title")
                    {
                        // 查找中文标题节点
                        foreach (XmlNode childNode in titleNode.ChildNodes)
                        {
                            if (childNode.Name == "ObjectList")
                            {
                                foreach (XmlNode itemNode in childNode.ChildNodes)
                                {
                                    if (itemNode.Name == "MultilingualTextItem")
                                    {
                                        foreach (XmlNode attrListNode in itemNode.ChildNodes)
                                        {
                                            if (attrListNode.Name == "AttributeList")
                                            {
                                                foreach (XmlNode attrNode in attrListNode.ChildNodes)
                                                {
                                                    if (attrNode.Name == "Culture" && attrNode.InnerText == "zh-CN")
                                                    {
                                                        // 找到了中文标题节点，更新其文本
                                                        foreach (XmlNode textNode in attrListNode.ChildNodes)
                                                        {
                                                            if (textNode.Name == "Text")
                                                            {
                                                                textNode.InnerText = opModeComment;
                                                                return;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新OpMode信息时发生错误: {ex.Message}", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 添加整数属性
        /// </summary>
        private void AddIntegerAttribute(XmlDocument xmlDoc, XmlElement parent, string name, string value, bool informative, bool systemDefined)
        {
            XmlElement attribute = xmlDoc.CreateElement("IntegerAttribute", parent.NamespaceURI);
            attribute.SetAttribute("Name", name);
            if (informative)
                attribute.SetAttribute("Informative", "true");
            if (systemDefined)
                attribute.SetAttribute("SystemDefined", "true");
            attribute.InnerText = value;
            parent.AppendChild(attribute);
        }

        /// <summary>
        /// 添加布尔属性
        /// </summary>
        private void AddBooleanAttribute(XmlDocument xmlDoc, XmlElement parent, string name, string value, bool systemDefined, bool informative = false)
        {
            XmlElement attribute = xmlDoc.CreateElement("BooleanAttribute", parent.NamespaceURI);
            attribute.SetAttribute("Name", name);
            if (informative)
                attribute.SetAttribute("Informative", "true");
            if (systemDefined)
                attribute.SetAttribute("SystemDefined", "true");
            attribute.InnerText = value;
            parent.AppendChild(attribute);
        }

        /// <summary>
        /// 添加输入成员
        /// </summary>
        private void AddInputMember(XmlDocument xmlDoc, XmlElement parent, string name, string dataType, int offset)
        {
            XmlElement member = xmlDoc.CreateElement("Member", parent.NamespaceURI);
            member.SetAttribute("Name", name);
            member.SetAttribute("Datatype", dataType);

            XmlElement attributeList = xmlDoc.CreateElement("AttributeList", parent.NamespaceURI);
            XmlElement intAttribute = xmlDoc.CreateElement("IntegerAttribute", parent.NamespaceURI);
            intAttribute.SetAttribute("Name", "Offset");
            intAttribute.SetAttribute("Informative", "true");
            intAttribute.SetAttribute("SystemDefined", "true");
            intAttribute.InnerText = offset.ToString();
            attributeList.AppendChild(intAttribute);

            member.AppendChild(attributeList);
            parent.AppendChild(member);
        }

        /// <summary>
        /// 获取单元格的值并转换为字符串，处理不同类型的单元格
        /// </summary>
        private string GetCellValueAsString(ICell cell)
        {
            if (cell == null)
                return null;

            switch (cell.CellType)
            {
                case CellType.String:
                    return cell.StringCellValue?.Trim();
                case CellType.Numeric:
                    // 如果是日期类型
                    if (DateUtil.IsCellDateFormatted(cell))
                        return cell.DateCellValue.ToString();
                    else
                        return cell.NumericCellValue.ToString();
                case CellType.Boolean:
                    return cell.BooleanCellValue.ToString();
                case CellType.Formula:
                    // 尝试获取公式计算结果
                    try
                    {
                        return cell.StringCellValue?.Trim();
                    }
                    catch
                    {
                        try
                        {
                            return cell.NumericCellValue.ToString();
                        }
                        catch
                        {
                            try
                            {
                                return cell.BooleanCellValue.ToString();
                            }
                            catch
                            {
                                return cell.CellFormula;
                            }
                        }
                    }
                case CellType.Blank:
                    return string.Empty;
                case CellType.Error:
                    return "#ERROR#";
                default:
                    return cell.ToString();
            }
        }

        /// <summary>
        /// XML生成器按钮点击事件
        /// </summary>
        private void btnXmlGenerator_Click(object sender, EventArgs e)
        {
            try
            {
                var xmlGeneratorForm = new XmlGeneratorForm();
                xmlGeneratorForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开XML生成器时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}