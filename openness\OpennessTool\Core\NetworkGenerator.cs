using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using OpennessTool.Models;

namespace OpennessTool.Core
{
    /// <summary>
    /// 网络代码生成器，对应Python版本的NetworkGenerator类
    /// </summary>
    public class NetworkGenerator
    {
        private readonly TemplateProcessor _templateProcessor;
        private readonly IdGenerator _idGenerator;

        /// <summary>
        /// 单输入类型的参数修改规则
        /// </summary>
        private readonly Dictionary<string, PartsModificationRule> _singleInputRules;

        /// <summary>
        /// 双输入类型的参数修改规则
        /// </summary>
        private readonly Dictionary<string, PartsModificationRule> _doubleInputRules;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="templateProcessor">模板处理器</param>
        /// <param name="idGenerator">ID生成器</param>
        public NetworkGenerator(TemplateProcessor templateProcessor, IdGenerator idGenerator)
        {
            _templateProcessor = templateProcessor ?? throw new ArgumentNullException(nameof(templateProcessor));
            _idGenerator = idGenerator ?? throw new ArgumentNullException(nameof(idGenerator));

            _singleInputRules = InitializeSingleInputRules();
            _doubleInputRules = InitializeDoubleInputRules();
        }

        /// <summary>
        /// 生成所有网络代码
        /// </summary>
        /// <param name="instances">实例数据列表</param>
        /// <param name="opModeComment">OpMode注释</param>
        /// <returns>生成的网络代码行列表</returns>
        public List<string> GenerateAllNetworks(List<InstanceData> instances, string opModeComment)
        {
            var result = new List<string>();

            // 1. 添加HMI显示和模式固定内容（3077-3459行）
            var hmiFixedContent = _templateProcessor.GetHmiFixedContent();
            result.AddRange(hmiFixedContent);

            // 2. 为每个实例生成网络代码
            for (int i = 0; i < instances.Count; i++)
            {
                var instance = instances[i];
                bool isLastInstance = (i == instances.Count - 1);

                var networkLines = GenerateInstanceNetwork(instance, isLastInstance);
                result.AddRange(networkLines);
            }

            return result;
        }

        /// <summary>
        /// 为单个实例生成网络代码
        /// </summary>
        /// <param name="instance">实例数据</param>
        /// <param name="isLastInstance">是否为最后一个实例</param>
        /// <returns>生成的网络代码行列表</returns>
        public List<string> GenerateInstanceNetwork(InstanceData instance, bool isLastInstance)
        {
            List<string> templateLines;

            // 根据实例类型选择模板
            if (instance.IsDoubleInputType)
            {
                templateLines = _templateProcessor.GetDoubleInputProgramTemplate();
            }
            else
            {
                templateLines = _templateProcessor.GetSingleInputProgramTemplate();
            }

            // 修复XML格式
            templateLines = _templateProcessor.FixXmlFormat(templateLines);

            // 修改参数
            var modifiedLines = ModifyNetworkParameters(templateLines, instance, isLastInstance);

            // 如果是最后一个实例，替换结尾部分
            if (isLastInstance)
            {
                modifiedLines = ReplaceLastInstanceEnding(modifiedLines, instance);
            }

            // 重新生成ID
            modifiedLines = RegenerateNetworkIds(modifiedLines);

            return modifiedLines;
        }

        /// <summary>
        /// 修改网络参数
        /// </summary>
        /// <param name="networkLines">网络代码行</param>
        /// <param name="instance">实例数据</param>
        /// <param name="isLastInstance">是否为最后一个实例</param>
        /// <returns>修改后的网络代码行</returns>
        private List<string> ModifyNetworkParameters(List<string> networkLines, InstanceData instance, bool isLastInstance)
        {
            var result = new List<string>(networkLines);
            var rules = instance.IsDoubleInputType ? _doubleInputRules : _singleInputRules;

            string currentUid = null;
            int componentCount = 0;

            for (int i = 0; i < result.Count; i++)
            {
                string line = result[i];

                // 检测UID
                var uidMatch = Regex.Match(line, @'<Access[^>]+UId="([^"]+)"');
                if (uidMatch.Success)
                {
                    currentUid = uidMatch.Groups[1].Value;
                    componentCount = 0;
                }

                // 处理Symbol标签
                if (line.Contains("<Symbol>"))
                {
                    componentCount = 0;
                }

                // 处理Component标签
                if (line.Contains("<Component Name=") && currentUid != null && rules.ContainsKey(currentUid))
                {
                    componentCount++;
                    var rule = rules[currentUid];
                    
                    if (rule.Components != null)
                    {
                        foreach (var compRule in rule.Components)
                        {
                            if (ShouldApplyComponentRule(compRule, componentCount))
                            {
                                var newValue = GetInstanceValue(instance, compRule.NewNameExcelCol);
                                if (!string.IsNullOrEmpty(newValue))
                                {
                                    var (modifiedLine, success) = _templateProcessor.ModifyLineAttribute(
                                        line, "Component", "Name", newValue);
                                    if (success)
                                    {
                                        result[i] = modifiedLine;
                                    }
                                }
                            }
                        }
                    }
                }

                // 处理Address标签
                if (line.Contains("BitOffset=") && currentUid != null && rules.ContainsKey(currentUid))
                {
                    var rule = rules[currentUid];
                    if (rule.Addresses != null)
                    {
                        foreach (var addrRule in rule.Addresses)
                        {
                            var offsetValue = GetInstanceValue(instance, addrRule.NewBitOffsetExcelCol);
                            if (!string.IsNullOrEmpty(offsetValue))
                            {
                                var (modifiedLine, success) = _templateProcessor.ModifyLineAttribute(
                                    line, "Address", "BitOffset", offsetValue);
                                if (success)
                                {
                                    result[i] = modifiedLine;
                                }
                            }
                        }
                    }
                }

                // 处理ConstantValue标签
                if (line.Contains("<ConstantValue>") && currentUid != null && rules.ContainsKey(currentUid))
                {
                    var rule = rules[currentUid];
                    if (rule.Constants != null)
                    {
                        foreach (var constRule in rule.Constants)
                        {
                            var newValue = GetInstanceValue(instance, constRule.NewValueExcelCol);
                            if (!string.IsNullOrEmpty(newValue))
                            {
                                var (modifiedLine, success) = _templateProcessor.ModifyLineTextContent(
                                    line, "ConstantValue", newValue);
                                if (success)
                                {
                                    result[i] = modifiedLine;
                                }
                            }
                        }
                    }
                }

                // 处理网络标题
                if (line.Contains("<Text>") && line.Contains("zh-CN"))
                {
                    // 查找前面是否有Title相关的MultilingualText
                    for (int j = Math.Max(0, i - 10); j < i; j++)
                    {
                        if (result[j].Contains("CompositionName=\"Title\""))
                        {
                            var (modifiedLine, success) = _templateProcessor.ModifyLineTextContent(
                                line, "Text", instance.Name);
                            if (success)
                            {
                                result[i] = modifiedLine;
                            }
                            break;
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 替换最后一个实例的结尾部分
        /// </summary>
        /// <param name="networkLines">网络代码行</param>
        /// <param name="instance">实例数据</param>
        /// <returns>替换后的网络代码行</returns>
        private List<string> ReplaceLastInstanceEnding(List<string> networkLines, InstanceData instance)
        {
            var result = new List<string>();
            bool foundEndingStart = false;
            int endingStartIndex = -1;

            // 查找结尾开始位置
            for (int i = 0; i < networkLines.Count; i++)
            {
                if (networkLines[i].Contains("</FlgNet></NetworkSource>"))
                {
                    foundEndingStart = true;
                    endingStartIndex = i;
                    break;
                }
            }

            if (foundEndingStart)
            {
                // 添加结尾开始之前的内容
                result.AddRange(networkLines.Take(endingStartIndex));

                // 添加替换内容
                var replacementLines = _templateProcessor.GetLastInstanceReplacement();
                
                // 修改替换内容中的参数
                var modifiedReplacement = ModifyLastInstanceReplacement(replacementLines, instance);
                result.AddRange(modifiedReplacement);
            }
            else
            {
                // 如果没有找到结尾标记，返回原内容
                result.AddRange(networkLines);
            }

            return result;
        }

        /// <summary>
        /// 修改最后实例替换内容的参数
        /// </summary>
        /// <param name="replacementLines">替换内容行</param>
        /// <param name="instance">实例数据</param>
        /// <returns>修改后的替换内容行</returns>
        private List<string> ModifyLastInstanceReplacement(List<string> replacementLines, InstanceData instance)
        {
            var result = new List<string>(replacementLines);

            for (int i = 0; i < result.Count; i++)
            {
                string line = result[i];

                // 修改标题文本
                if (line.Contains("<Text>") && line.Contains("zh-CN"))
                {
                    var (modifiedLine, success) = _templateProcessor.ModifyLineTextContent(
                        line, "Text", instance.Name);
                    if (success)
                    {
                        result[i] = modifiedLine;
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 重新生成网络ID
        /// </summary>
        /// <param name="networkLines">网络代码行</param>
        /// <returns>重新生成ID后的网络代码行</returns>
        private List<string> RegenerateNetworkIds(List<string> networkLines)
        {
            var result = new List<string>(networkLines);
            var idMappings = new Dictionary<string, string>();

            // 第一遍：收集所有需要重新生成的ID
            foreach (var line in result)
            {
                var matches = Regex.Matches(line, @'ID="([^"]+)"');
                foreach (Match match in matches)
                {
                    string oldId = match.Groups[1].Value;
                    if (!idMappings.ContainsKey(oldId))
                    {
                        string newId = _idGenerator.GetNextId();
                        idMappings[oldId] = newId;
                    }
                }
            }

            // 第二遍：替换所有ID
            for (int i = 0; i < result.Count; i++)
            {
                string line = result[i];
                foreach (var mapping in idMappings)
                {
                    line = line.Replace($'ID="{mapping.Key}"', $'ID="{mapping.Value}"');
                }
                result[i] = line;
            }

            return result;
        }

        /// <summary>
        /// 判断是否应用组件规则
        /// </summary>
        /// <param name="rule">组件规则</param>
        /// <param name="componentCount">当前组件计数</param>
        /// <returns>是否应用</returns>
        private bool ShouldApplyComponentRule(ComponentRule rule, int componentCount)
        {
            // 这里可以根据具体需求实现更复杂的逻辑
            // 目前简化为总是应用第一个匹配的规则
            return true;
        }

        /// <summary>
        /// 从实例数据中获取指定列的值
        /// </summary>
        /// <param name="instance">实例数据</param>
        /// <param name="columnName">列名</param>
        /// <returns>列值</returns>
        private string GetInstanceValue(InstanceData instance, string columnName)
        {
            var dict = instance.ToDictionary();
            return dict.ContainsKey(columnName) ? dict[columnName]?.ToString() ?? string.Empty : string.Empty;
        }

        /// <summary>
        /// 初始化单输入类型的修改规则
        /// </summary>
        private Dictionary<string, PartsModificationRule> InitializeSingleInputRules()
        {
            return new Dictionary<string, PartsModificationRule>
            {
                ["21"] = new PartsModificationRule
                {
                    Components = new[] { new ComponentRule { OriginalName = "I132.0 左UPIN缓存挡料1气缸 伸出到位 NA1-C101a", NewNameExcelCol = "I点-工作位1" } },
                    Addresses = new[] { new AddressRule { NewBitOffsetExcelCol = "I点-工作位1偏移量" } }
                },
                ["22"] = new PartsModificationRule
                {
                    Components = new[] { new ComponentRule { OriginalName = "I132.1 左UPIN缓存挡料1气缸 缩回到位 NA1-C101b", NewNameExcelCol = "I点-原位1" } },
                    Addresses = new[] { new AddressRule { NewBitOffsetExcelCol = "I点-原位1偏移量" } }
                },
                ["23"] = new PartsModificationRule
                {
                    Components = new[]
                    {
                        new ComponentRule { OriginalName = "DB_ControlNodesC5", NewNameExcelCol = "手动行DB块名称" },
                        new ComponentRule { OriginalName = "em12", NewNameExcelCol = "OpMode名称" },
                        new ComponentRule { OriginalName = "NA1C101左缓存挡料气缸1", NewNameExcelCol = "name" }
                    }
                },
                ["24"] = new PartsModificationRule
                {
                    Components = new[]
                    {
                        new ComponentRule { OriginalName = "DB_ControlNodesC5", NewNameExcelCol = "手动行DB块名称" },
                        new ComponentRule { OriginalName = "em12", NewNameExcelCol = "OpMode名称" },
                        new ComponentRule { OriginalName = "NA1C101左缓存挡料气缸1", NewNameExcelCol = "name" }
                    }
                },
                ["29"] = new PartsModificationRule
                {
                    Constants = new[] { new ConstantRule { OriginalValue = "12", NewValueExcelCol = "OpMode编号" } }
                },
                ["35"] = new PartsModificationRule
                {
                    Components = new[] { new ComponentRule { OriginalName = "Q216.4 NA1C101挡料气缸1伸出", NewNameExcelCol = "Q点-工作位" } },
                    Addresses = new[] { new AddressRule { NewBitOffsetExcelCol = "Q点-工作位偏移量" } }
                },
                ["36"] = new PartsModificationRule
                {
                    Components = new[] { new ComponentRule { OriginalName = "Q216.5 NA1C101挡料气缸1缩回", NewNameExcelCol = "Q点-原位" } },
                    Addresses = new[] { new AddressRule { NewBitOffsetExcelCol = "Q点-原位偏移量" } }
                },
                ["37"] = new PartsModificationRule
                {
                    Components = new[]
                    {
                        new ComponentRule { OriginalName = "DB_ControlNodesC5", NewNameExcelCol = "手动行DB块名称" },
                        new ComponentRule { OriginalName = "em12", NewNameExcelCol = "OpMode名称" },
                        new ComponentRule { OriginalName = "NA1C101左缓存挡料气缸1", NewNameExcelCol = "name" }
                    }
                },
                ["38"] = new PartsModificationRule
                {
                    Components = new[]
                    {
                        new ComponentRule { OriginalName = "DB_ControlNodesC5", NewNameExcelCol = "手动行DB块名称" },
                        new ComponentRule { OriginalName = "em12", NewNameExcelCol = "OpMode名称" },
                        new ComponentRule { OriginalName = "NA1C101左缓存挡料气缸1", NewNameExcelCol = "name" }
                    }
                },
                ["39"] = new PartsModificationRule
                {
                    Components = new[] { new ComponentRule { OriginalName = "NA1C101左缓存挡料气缸1", NewNameExcelCol = "name" } }
                }
            };
        }

        /// <summary>
        /// 初始化双输入类型的修改规则
        /// </summary>
        private Dictionary<string, PartsModificationRule> InitializeDoubleInputRules()
        {
            return new Dictionary<string, PartsModificationRule>
            {
                ["21"] = new PartsModificationRule
                {
                    Components = new[] { new ComponentRule { OriginalName = "I133.0 左IPIN缓存料盘定位气缸 伸出到位 NA1-C501a", NewNameExcelCol = "I点-工作位1" } },
                    Addresses = new[] { new AddressRule { NewBitOffsetExcelCol = "I点-工作位1偏移量" } }
                },
                ["22"] = new PartsModificationRule
                {
                    Components = new[] { new ComponentRule { OriginalName = "I133.2 左IPIN缓存料盘定位气缸 伸出到位 NA1-C501c", NewNameExcelCol = "I点-工作位2" } },
                    Addresses = new[] { new AddressRule { NewBitOffsetExcelCol = "I点-工作位2偏移量" } }
                },
                ["23"] = new PartsModificationRule
                {
                    Components = new[] { new ComponentRule { OriginalName = "I133.1 左IPIN缓存料盘定位气缸 缩回到位 NA1-C501b", NewNameExcelCol = "I点-原位1" } },
                    Addresses = new[] { new AddressRule { NewBitOffsetExcelCol = "I点-原位1偏移量" } }
                },
                ["24"] = new PartsModificationRule
                {
                    Components = new[] { new ComponentRule { OriginalName = "I133.3 左IPIN缓存料盘定位气缸 缩回到位 NA1-C501d", NewNameExcelCol = "I点-原位2" } },
                    Addresses = new[] { new AddressRule { NewBitOffsetExcelCol = "I点-原位2偏移量" } }
                },
                ["25"] = new PartsModificationRule
                {
                    Components = new[]
                    {
                        new ComponentRule { OriginalName = "DB_ControlNodesC5", NewNameExcelCol = "手动行DB块名称" },
                        new ComponentRule { OriginalName = "em12", NewNameExcelCol = "OpMode名称" },
                        new ComponentRule { OriginalName = "NA1C501左缓存料盘定位气缸", NewNameExcelCol = "name" }
                    }
                },
                ["26"] = new PartsModificationRule
                {
                    Components = new[]
                    {
                        new ComponentRule { OriginalName = "DB_ControlNodesC5", NewNameExcelCol = "手动行DB块名称" },
                        new ComponentRule { OriginalName = "em12", NewNameExcelCol = "OpMode名称" },
                        new ComponentRule { OriginalName = "NA1C501左缓存料盘定位气缸", NewNameExcelCol = "name" }
                    }
                },
                ["31"] = new PartsModificationRule
                {
                    Constants = new[] { new ConstantRule { OriginalValue = "12", NewValueExcelCol = "OpMode编号" } }
                },
                ["37"] = new PartsModificationRule
                {
                    Components = new[] { new ComponentRule { OriginalName = "Q217.4 NA1C501料盘定位气缸伸出", NewNameExcelCol = "Q点-工作位" } },
                    Addresses = new[] { new AddressRule { NewBitOffsetExcelCol = "Q点-工作位偏移量" } }
                },
                ["38"] = new PartsModificationRule
                {
                    Components = new[] { new ComponentRule { OriginalName = "Q217.5 NA1C501料盘定位气缸缩回", NewNameExcelCol = "Q点-原位" } },
                    Addresses = new[] { new AddressRule { NewBitOffsetExcelCol = "Q点-原位偏移量" } }
                },
                ["39"] = new PartsModificationRule
                {
                    Components = new[]
                    {
                        new ComponentRule { OriginalName = "DB_ControlNodesC5", NewNameExcelCol = "手动行DB块名称" },
                        new ComponentRule { OriginalName = "em12", NewNameExcelCol = "OpMode名称" },
                        new ComponentRule { OriginalName = "NA1C501左缓存料盘定位气缸", NewNameExcelCol = "name" }
                    }
                },
                ["40"] = new PartsModificationRule
                {
                    Components = new[]
                    {
                        new ComponentRule { OriginalName = "DB_ControlNodesC5", NewNameExcelCol = "手动行DB块名称" },
                        new ComponentRule { OriginalName = "em12", NewNameExcelCol = "OpMode名称" },
                        new ComponentRule { OriginalName = "NA1C501左缓存料盘定位气缸", NewNameExcelCol = "name" }
                    }
                },
                ["45"] = new PartsModificationRule
                {
                    Components = new[] { new ComponentRule { OriginalName = "NA1C501左缓存料盘定位气缸", NewNameExcelCol = "name" } }
                }
            };
        }
    }

    /// <summary>
    /// 参数修改规则
    /// </summary>
    public class PartsModificationRule
    {
        public ComponentRule[] Components { get; set; }
        public AddressRule[] Addresses { get; set; }
        public ConstantRule[] Constants { get; set; }
    }

    /// <summary>
    /// 组件规则
    /// </summary>
    public class ComponentRule
    {
        public string OriginalName { get; set; }
        public string NewNameExcelCol { get; set; }
    }

    /// <summary>
    /// 地址规则
    /// </summary>
    public class AddressRule
    {
        public string NewBitOffsetExcelCol { get; set; }
    }

    /// <summary>
    /// 常量规则
    /// </summary>
    public class ConstantRule
    {
        public string OriginalValue { get; set; }
        public string NewValueExcelCol { get; set; }
    }
}
