<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Enums.NET</name>
    </assembly>
    <members>
        <member name="T:EnumsNET.AttributeCollection">
            <summary>
            An <see cref="T:System.Attribute"/> collection. 
            </summary>
        </member>
        <member name="P:EnumsNET.AttributeCollection.Item(System.Int32)">
            <summary>
            The <see cref="T:EnumsNET.AttributeCollection"/> indexer. 
            </summary>
            <param name="index">The index of the <see cref="T:System.Attribute"/> to retrieve.</param>
            <returns>The <see cref="T:System.Attribute"/> at the specified <paramref name="index"/></returns>
        </member>
        <member name="P:EnumsNET.AttributeCollection.Count">
            <summary>
            The number of <see cref="T:System.Attribute"/>s.
            </summary>
        </member>
        <member name="M:EnumsNET.AttributeCollection.Has``1">
            <summary>
            Indicates if the collection contains a <typeparamref name="TAttribute"/>.
            </summary>
            <typeparam name="TAttribute">The attribute type.</typeparam>
            <returns>Indication if the colleciton contains a <typeparamref name="TAttribute"/>.</returns>
        </member>
        <member name="M:EnumsNET.AttributeCollection.Has(System.Type)">
            <summary>
            Indicates if the collection contains an <see cref="T:System.Attribute"/> that is an instance of <paramref name="attributeType"/>.
            </summary>
            <param name="attributeType">The attribute type.</param>
            <returns>Indication if the colleciton contains an <see cref="T:System.Attribute"/> that is an instance of <paramref name="attributeType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="attributeType"/> is <c>null</c>.</exception>
        </member>
        <member name="M:EnumsNET.AttributeCollection.Get``1">
            <summary>
            Retrieves the first <typeparamref name="TAttribute"/> in the collection if defined otherwise <c>null</c>.
            </summary>
            <typeparam name="TAttribute">The attribute type.</typeparam>
            <returns>The first <typeparamref name="TAttribute"/> in the collection if defined otherwise <c>null</c>.</returns>
        </member>
        <member name="M:EnumsNET.AttributeCollection.Get(System.Type)">
            <summary>
            Retrieves the first <see cref="T:System.Attribute"/> that is an instance of <paramref name="attributeType"/> in the collection if defined otherwise <c>null</c>.
            </summary>
            <param name="attributeType">The attribute type.</param>
            <returns>The first <see cref="T:System.Attribute"/> that is an instance of <paramref name="attributeType"/> in the collection if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="attributeType"/> is <c>null</c>.</exception>
        </member>
        <member name="M:EnumsNET.AttributeCollection.GetAll``1">
            <summary>
            Retrieves all <typeparamref name="TAttribute"/>'s in the collection.
            </summary>
            <typeparam name="TAttribute">The attribute type.</typeparam>
            <returns>All <typeparamref name="TAttribute"/>'s in the collection.</returns>
        </member>
        <member name="M:EnumsNET.AttributeCollection.GetAll(System.Type)">
            <summary>
            Retrieves all <see cref="T:System.Attribute"/>s that are an instance of <paramref name="attributeType"/> in the collection.
            </summary>
            <param name="attributeType">The attribute type.</param>
            <returns>All <see cref="T:System.Attribute"/>s that are an instance of <paramref name="attributeType"/> in the collection.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="attributeType"/> is <c>null</c>.</exception>
        </member>
        <member name="M:EnumsNET.AttributeCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>An enumerator that iterates through the collection.</returns>
        </member>
        <member name="T:EnumsNET.EnumComparer">
            <summary>
            An efficient enum comparer.
            </summary>
        </member>
        <member name="M:EnumsNET.EnumComparer.GetInstance(System.Type)">
            <summary>
            Gets a singleton instance of <see cref="T:EnumsNET.EnumComparer"/> for the enum type provided.
            </summary>
            <param name="enumType">The enum type.</param>
            <returns>A singleton instance of <see cref="T:EnumsNET.EnumComparer"/> for the enum type provided.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.EnumComparer.Equals(System.Object,System.Object)">
            <summary>
            Indicates if <paramref name="x"/> equals <paramref name="y"/> without boxing the values.
            </summary>
            <param name="x">The first enum value.</param>
            <param name="y">The second enum value.</param>
            <returns>Indication if <paramref name="x"/> equals <paramref name="y"/> without boxing the values.</returns>
        </member>
        <member name="M:EnumsNET.EnumComparer.GetHashCode(System.Object)">
            <summary>
            Retrieves a hash code for <paramref name="obj"/> without boxing the value.
            </summary>
            <param name="obj">The enum value.</param>
            <returns>Hash code for <paramref name="obj"/> without boxing the value.</returns>
        </member>
        <member name="M:EnumsNET.EnumComparer.Compare(System.Object,System.Object)">
            <summary>
            Compares <paramref name="x"/> to <paramref name="y"/> without boxing the values.
            </summary>
            <param name="x">The first enum value.</param>
            <param name="y">The second enum value.</param>
            <returns>1 if <paramref name="x"/> is greater than <paramref name="y"/>, 0 if <paramref name="x"/> equals <paramref name="y"/>,
            and -1 if <paramref name="x"/> is less than <paramref name="y"/>.</returns>
        </member>
        <member name="T:EnumsNET.EnumComparer`1">
            <summary>
            An efficient enum comparer which doesn't box the values.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
        </member>
        <member name="P:EnumsNET.EnumComparer`1.Instance">
            <summary>
            The singleton instance of <see cref="T:EnumsNET.EnumComparer`1"/>. 
            </summary>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.EnumComparer`1.Equals(`0,`0)">
            <summary>
            Indicates if <paramref name="x"/> equals <paramref name="y"/> without boxing the values.
            </summary>
            <param name="x">The first enum value.</param>
            <param name="y">The second enum value.</param>
            <returns>Indication if <paramref name="x"/> equals <paramref name="y"/> without boxing the values.</returns>
        </member>
        <member name="M:EnumsNET.EnumComparer`1.GetHashCode(`0)">
            <summary>
            Retrieves a hash code for <paramref name="obj"/> without boxing the value.
            </summary>
            <param name="obj">The enum value.</param>
            <returns>Hash code for <paramref name="obj"/> without boxing the value.</returns>
        </member>
        <member name="M:EnumsNET.EnumComparer`1.Compare(`0,`0)">
            <summary>
            Compares <paramref name="x"/> to <paramref name="y"/> without boxing the values.
            </summary>
            <param name="x">The first enum value.</param>
            <param name="y">The second enum value.</param>
            <returns>1 if <paramref name="x"/> is greater than <paramref name="y"/>, 0 if <paramref name="x"/> equals <paramref name="y"/>,
            and -1 if <paramref name="x"/> is less than <paramref name="y"/>.</returns>
        </member>
        <member name="T:EnumsNET.EnumFormat">
            <summary>
            Specifies the enum string representation formats.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumFormat.DecimalValue">
            <summary>
            Enum is represented by its decimal value.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumFormat.HexadecimalValue">
            <summary>
            Enum is represented by its hexadecimal value.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumFormat.UnderlyingValue">
            <summary>
            Enum is represented by its underlying value.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumFormat.Name">
            <summary>
            Enum is represented by its name.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumFormat.Description">
            <summary>
            Enum is represented by its <see cref="P:System.ComponentModel.DescriptionAttribute.Description"/>.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumFormat.EnumMemberValue">
            <summary>
            Enum is represented by its <see cref="P:System.Runtime.Serialization.EnumMemberAttribute.Value"/>.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumFormat.DisplayName">
            <summary>
            Enum is represented by its <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name"/>.
            </summary>
        </member>
        <member name="T:EnumsNET.EnumMember">
            <summary>
            An enum member which is composed of its name, value, and attributes.
            </summary>
        </member>
        <member name="P:EnumsNET.EnumMember.Value">
            <summary>
            The enum member's value.
            </summary>
        </member>
        <member name="P:EnumsNET.EnumMember.Name">
            <summary>
            The enum member's name.
            </summary>
        </member>
        <member name="P:EnumsNET.EnumMember.Attributes">
            <summary>
            The enum member's attributes.
            </summary>
        </member>
        <member name="M:EnumsNET.EnumMember.ToString">
            <summary>
            Retrieves the enum member's name.
            </summary>
            <returns>The enum member's name.</returns>
        </member>
        <member name="M:EnumsNET.EnumMember.AsString">
            <summary>
            Retrieves the enum member's name.
            </summary>
            <returns>The enum member's name.</returns>
        </member>
        <member name="M:EnumsNET.EnumMember.AsString(System.String)">
            <summary>
            Converts the enum member to its string representation using the specified <paramref name="format"/>.
            </summary>
            <param name="format">The output format to use.</param>
            <returns>A string representation of the enum member.</returns>
            <exception cref="T:System.FormatException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.AsString(EnumsNET.EnumFormat)">
            <summary>
            Converts the enum member to its string representation using the specified <paramref name="format"/>.
            </summary>
            <param name="format">The output format to use.</param>
            <returns>A string representation of the enum member.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.AsString(EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the enum member to its string representation using the specified formats.
            </summary>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use if using the first resolves to <c>null</c>.</param>
            <returns>A string representation of the enum member.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.AsString(EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the enum member to its string representation using the specified formats.
            </summary>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use if using the first resolves to <c>null</c>.</param>
            <param name="format2">The third output format to use if using the first and second both resolve to <c>null</c>.</param>
            <returns>A string representation of the enum member.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.AsString(EnumsNET.EnumFormat[])">
            <summary>
            Converts the enum member to its string representation using the specified <paramref name="formats"/>.
            </summary>
            <param name="formats">The output formats to use.</param>
            <returns>A string representation of the enum member.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.GetUnderlyingValue">
            <summary>
            Retrieves the enum member's underlying integral value.
            </summary>
            <returns>The enum member's underlying integral value.</returns>
        </member>
        <member name="M:EnumsNET.EnumMember.ToSByte">
            <summary>
            Converts <see cref="P:EnumsNET.EnumMember.Value"/> to an <see cref="T:System.SByte"/>.
            </summary>
            <returns><see cref="P:EnumsNET.EnumMember.Value"/> converted to an <see cref="T:System.SByte"/>.</returns>
            <exception cref="T:System.OverflowException"><see cref="P:EnumsNET.EnumMember.Value"/> cannot fit within <see cref="T:System.SByte"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.ToByte">
            <summary>
            Converts <see cref="P:EnumsNET.EnumMember.Value"/> to a <see cref="T:System.Byte"/>.
            </summary>
            <returns><see cref="P:EnumsNET.EnumMember.Value"/> converted to a <see cref="T:System.Byte"/>.</returns>
            <exception cref="T:System.OverflowException"><see cref="P:EnumsNET.EnumMember.Value"/> cannot fit within <see cref="T:System.Byte"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.ToInt16">
            <summary>
            Converts <see cref="P:EnumsNET.EnumMember.Value"/> to an <see cref="T:System.Int16"/>.
            </summary>
            <returns><see cref="P:EnumsNET.EnumMember.Value"/> converted to an <see cref="T:System.Int16"/>.</returns>
            <exception cref="T:System.OverflowException"><see cref="P:EnumsNET.EnumMember.Value"/> cannot fit within <see cref="T:System.Int16"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.ToUInt16">
            <summary>
            Converts <see cref="P:EnumsNET.EnumMember.Value"/> to a <see cref="T:System.UInt16"/>.
            </summary>
            <returns><see cref="P:EnumsNET.EnumMember.Value"/> converted to a <see cref="T:System.UInt16"/>.</returns>
            <exception cref="T:System.OverflowException"><see cref="P:EnumsNET.EnumMember.Value"/> cannot fit within <see cref="T:System.UInt16"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.ToInt32">
            <summary>
            Converts <see cref="P:EnumsNET.EnumMember.Value"/> to an <see cref="T:System.Int32"/>.
            </summary>
            <returns><see cref="P:EnumsNET.EnumMember.Value"/> converted to an <see cref="T:System.Int32"/>.</returns>
            <exception cref="T:System.OverflowException"><see cref="P:EnumsNET.EnumMember.Value"/> cannot fit within <see cref="T:System.Int32"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.ToUInt32">
            <summary>
            Converts <see cref="P:EnumsNET.EnumMember.Value"/> to a <see cref="T:System.UInt32"/>.
            </summary>
            <returns><see cref="P:EnumsNET.EnumMember.Value"/> converted to a <see cref="T:System.UInt32"/>.</returns>
            <exception cref="T:System.OverflowException"><see cref="P:EnumsNET.EnumMember.Value"/> cannot fit within <see cref="T:System.UInt32"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.ToInt64">
            <summary>
            Converts <see cref="P:EnumsNET.EnumMember.Value"/> to an <see cref="T:System.Int64"/>.
            </summary>
            <returns><see cref="P:EnumsNET.EnumMember.Value"/> converted to an <see cref="T:System.Int64"/>.</returns>
            <exception cref="T:System.OverflowException"><see cref="P:EnumsNET.EnumMember.Value"/> cannot fit within <see cref="T:System.Int64"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.ToUInt64">
            <summary>
            Converts <see cref="P:EnumsNET.EnumMember.Value"/> to a <see cref="T:System.UInt64"/>.
            </summary>
            <returns><see cref="P:EnumsNET.EnumMember.Value"/> converted to a <see cref="T:System.UInt64"/>.</returns>
            <exception cref="T:System.OverflowException"><see cref="P:EnumsNET.EnumMember.Value"/> cannot fit within <see cref="T:System.UInt64"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.EnumMember.GetHashCode">
            <summary>
            Retrieves the hash code of <see cref="P:EnumsNET.EnumMember.Value"/>.
            </summary>
            <returns>The hash code of <see cref="P:EnumsNET.EnumMember.Value"/>.</returns>
        </member>
        <member name="M:EnumsNET.EnumMember.Equals(EnumsNET.EnumMember)">
            <summary>
            Indicates whether the specified <see cref="T:EnumsNET.EnumMember"/> is equal to the current <see cref="T:EnumsNET.EnumMember"/>.
            </summary>
            <param name="other">The other <see cref="T:EnumsNET.EnumMember"/>.</param>
            <returns>Indication whether the specified <see cref="T:EnumsNET.EnumMember"/> is equal to the current <see cref="T:EnumsNET.EnumMember"/>.</returns>
        </member>
        <member name="M:EnumsNET.EnumMember.Equals(System.Object)">
            <summary>
            Indicates whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.
            </summary>
            <param name="other">The other <see cref="T:System.Object"/>.</param>
            <returns>Indication whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.</returns>
        </member>
        <member name="T:EnumsNET.EnumMember`1">
            <summary>
            An enum member which is composed of its name, value, and attributes.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
        </member>
        <member name="P:EnumsNET.EnumMember`1.Value">
            <summary>
            The enum member's value.
            </summary>
        </member>
        <member name="M:EnumsNET.EnumMember`1.Equals(EnumsNET.EnumMember{`0})">
            <summary>
            Indicates whether the specified <see cref="T:EnumsNET.EnumMember`1"/> is equal to the current <see cref="T:EnumsNET.EnumMember`1"/>.
            </summary>
            <param name="other">The other <see cref="T:EnumsNET.EnumMember`1"/>.</param>
            <returns>Indication whether the specified <see cref="T:EnumsNET.EnumMember`1"/> is equal to the current <see cref="T:EnumsNET.EnumMember`1"/>.</returns>
        </member>
        <member name="T:EnumsNET.EnumMemberSelection">
            <summary>
            Specifies what enum members to include.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumMemberSelection.All">
            <summary>
            Include all enum members.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumMemberSelection.Distinct">
            <summary>
            Include only distinct valued enum members.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumMemberSelection.Flags">
            <summary>
            Include each flag enum member.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumMemberSelection.DisplayOrder">
            <summary>
            Include enum members in display order using <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Order"/>.
            </summary>
        </member>
        <member name="T:EnumsNET.Enums">
            <summary>
            Static class that provides efficient type-safe enum operations through the use of cached enum members.
            Many operations are exposed as C# extension methods for convenience.
            </summary>
        </member>
        <member name="M:EnumsNET.Enums.RegisterCustomEnumFormat(System.Func{EnumsNET.EnumMember,System.String})">
            <summary>
            Registers a custom <see cref="T:EnumsNET.EnumFormat"/> with the specified <see cref="T:EnumsNET.EnumMember"/> formatter.
            </summary>
            <param name="enumMemberFormatter">The <see cref="T:EnumsNET.EnumMember"/> formatter.</param>
            <returns>A custom <see cref="T:EnumsNET.EnumFormat"/> that is registered with the specified <see cref="T:EnumsNET.EnumMember"/> formatter.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumMemberFormatter"/> is <c>null</c>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetUnderlyingType``1">
            <summary>
            Retrieves the underlying type of <typeparamref name="TEnum"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <returns>The underlying type of <typeparamref name="TEnum"/>.</returns>
        </member>
        <member name="M:EnumsNET.Enums.GetTypeCode``1">
            <summary>
            Retrieves <typeparamref name="TEnum"/>'s underlying type's <see cref="T:System.TypeCode"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <returns><typeparamref name="TEnum"/>'s underlying type's <see cref="T:System.TypeCode"/>.</returns>
        </member>
        <member name="M:EnumsNET.Enums.GetMemberCount``1(EnumsNET.EnumMemberSelection)">
            <summary>
            Retrieves <typeparamref name="TEnum"/>'s member count.
            The parameter <paramref name="selection"/> indicates what members to include.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="selection">Indicates what members to include.</param>
            <returns><typeparamref name="TEnum"/>'s member count.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="selection"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMembers``1(EnumsNET.EnumMemberSelection)">
            <summary>
            Retrieves <typeparamref name="TEnum"/>'s members in increasing value order.
            The parameter <paramref name="selection"/> indicates what members to include.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="selection">Indicates what members to include.</param>
            <returns><typeparamref name="TEnum"/>'s members in increasing value order.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="selection"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetNames``1(EnumsNET.EnumMemberSelection)">
            <summary>
            Retrieves <typeparamref name="TEnum"/>'s members' names in increasing value order.
            The parameter <paramref name="selection"/> indicates what members to include.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="selection">Indicates what members to include.</param>
            <returns><typeparamref name="TEnum"/>'s members' names in increasing value order.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="selection"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetValues``1(EnumsNET.EnumMemberSelection)">
            <summary>
            Retrieves <typeparamref name="TEnum"/>'s members' values in increasing value order.
            The parameter <paramref name="selection"/> indicates what members to include.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="selection">Indicates what members to include.</param>
            <returns><typeparamref name="TEnum"/>'s members' values in increasing value order.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="selection"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject``1(System.Object,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert. Must be an <see cref="T:System.SByte"/>, <see cref="T:System.Byte"/>, <see cref="T:System.Int16"/>, <see cref="T:System.UInt16"/>,
            <see cref="T:System.Int32"/>, <see cref="T:System.UInt32"/>, <see cref="T:System.Int64"/>, <see cref="T:System.UInt64"/>, <typeparamref name="TEnum"/>, <see cref="T:System.String"/>, or Nullable of one of these.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> is not a valid type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject``1(System.SByte,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject``1(System.Byte,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject``1(System.Int16,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject``1(System.UInt16,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject``1(System.Int32,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject``1(System.UInt32,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject``1(System.Int64,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject``1(System.UInt64,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject``1(System.Object,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type.  The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert. Must be an <see cref="T:System.SByte"/>, <see cref="T:System.Byte"/>, <see cref="T:System.Int16"/>, <see cref="T:System.UInt16"/>,
            <see cref="T:System.Int32"/>, <see cref="T:System.UInt32"/>, <see cref="T:System.Int64"/>, <see cref="T:System.UInt64"/>, <typeparamref name="TEnum"/>, <see cref="T:System.String"/>, or Nullable of one of these.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject``1(System.SByte,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type.  The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject``1(System.Byte,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type.  The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject``1(System.Int16,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type.  The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject``1(System.UInt16,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type.  The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject``1(System.Int32,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type.  The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject``1(System.UInt32,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type.  The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject``1(System.Int64,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type.  The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject``1(System.UInt64,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type.  The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.IsValid``1(``0,EnumsNET.EnumValidation)">
            <summary>
            Indicates if <paramref name="value"/> is valid using the specified <paramref name="validation"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="validation">The validation to perform on the value.</param>
            <returns>Indication if <paramref name="value"/> is valid.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.IsDefined``1(``0)">
            <summary>
            Indicates if <paramref name="value"/> is defined.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns>Indication if <paramref name="value"/> is defined.</returns>
        </member>
        <member name="M:EnumsNET.Enums.Validate``1(``0,System.String,EnumsNET.EnumValidation)">
            <summary>
            Validates that <paramref name="value"/> is valid using the specified <paramref name="validation"/>.
            If it's not it throws an <see cref="T:System.ArgumentException"/> with the specified <paramref name="paramName"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="paramName">The parameter name to be used if throwing an <see cref="T:System.ArgumentException"/>.</param>
            <param name="validation">The validation to perform on the value.</param>
            <returns><paramref name="value"/> for use in fluent API's and base constructor method calls.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="validation"/> is an invalid value
            -or-
            <paramref name="value"/> is invalid.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsString``1(``0)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
        </member>
        <member name="M:EnumsNET.Enums.AsString``1(``0,System.String)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified <paramref name="format"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="format">The output format to use.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.FormatException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsString``1(``0,EnumsNET.EnumFormat)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified <paramref name="format"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="format">The output format to use.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsString``1(``0,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified formats.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use if using the first resolves to <c>null</c>.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsString``1(``0,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified formats.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use if using the first resolves to <c>null</c>.</param>
            <param name="format2">The third output format to use if using the first and second both resolve to <c>null</c>.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsString``1(``0,EnumsNET.EnumFormat[])">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified <paramref name="formats"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="formats">The output formats to use.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Format``1(``0,System.String)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified <paramref name="format"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="format">The output format to use.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="format"/> is <c>null</c>.</exception>
            <exception cref="T:System.FormatException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetUnderlyingValue``1(``0)">
            <summary>
            Returns <paramref name="value"/>'s underlying integral value.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/>'s underlying integral value.</returns>
        </member>
        <member name="M:EnumsNET.Enums.ToSByte``1(``0)">
            <summary>
            Converts <paramref name="value"/> to an <see cref="T:System.SByte"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to an <see cref="T:System.SByte"/>.</returns>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.SByte"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToByte``1(``0)">
            <summary>
            Converts <paramref name="value"/> to a <see cref="T:System.Byte"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to a <see cref="T:System.Byte"/>.</returns>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.Byte"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToInt16``1(``0)">
            <summary>
            Converts <paramref name="value"/> to an <see cref="T:System.Int16"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to an <see cref="T:System.Int16"/>.</returns>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.Int16"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToUInt16``1(``0)">
            <summary>
            Converts <paramref name="value"/> to a <see cref="T:System.UInt16"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to a <see cref="T:System.UInt16"/>.</returns>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.UInt16"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToInt32``1(``0)">
            <summary>
            Converts <paramref name="value"/> to an <see cref="T:System.Int32"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to an <see cref="T:System.Int32"/>.</returns>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.Int32"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToUInt32``1(``0)">
            <summary>
            Converts <paramref name="value"/> to a <see cref="T:System.UInt32"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to a <see cref="T:System.UInt32"/>.</returns>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.UInt32"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToInt64``1(``0)">
            <summary>
            Converts <paramref name="value"/> to an <see cref="T:System.Int64"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to an <see cref="T:System.Int64"/>.</returns>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.Int64"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToUInt64``1(``0)">
            <summary>
            Converts <paramref name="value"/> to a <see cref="T:System.UInt64"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to a <see cref="T:System.UInt64"/>.</returns>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.UInt64"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetHashCode``1(``0)">
            <summary>
            Retrieves the hash code of <paramref name="value"/>. It's more efficient as it doesn't require boxing and unboxing of <paramref name="value"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns>Hash code of <paramref name="value"/>.</returns>
        </member>
        <member name="M:EnumsNET.Enums.Equals``1(``0,``0)">
            <summary>
            Indicates if <paramref name="value"/> equals <paramref name="other"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="other">The other enum value.</param>
            <returns>Indication if <paramref name="value"/> equals <paramref name="other"/>.</returns>
        </member>
        <member name="M:EnumsNET.Enums.CompareTo``1(``0,``0)">
            <summary>
            Compares <paramref name="value"/> to <paramref name="other"/> for ordering.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="other">The other enum value.</param>
            <returns>1 if <paramref name="value"/> is greater than <paramref name="other"/>, 0 if <paramref name="value"/> equals <paramref name="other"/>,
            and -1 if <paramref name="value"/> is less than <paramref name="other"/>.</returns>
        </member>
        <member name="M:EnumsNET.Enums.GetName``1(``0)">
            <summary>
            Retrieves <paramref name="value"/>'s enum member name if defined otherwise <c>null</c>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/>'s enum member name if defined otherwise <c>null</c>.</returns>
        </member>
        <member name="M:EnumsNET.Enums.GetAttributes``1(``0)">
            <summary>
            Retrieves <paramref name="value"/>'s enum member attributes if defined otherwise <c>null</c>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/>'s enum member attributes if defined otherwise <c>null</c>.</returns>
        </member>
        <member name="M:EnumsNET.Enums.GetMember``1(``0)">
            <summary>
            Retrieves an enum member with the specified <paramref name="value"/> if defined otherwise <c>null</c>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns>Enum member with the specified <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
        </member>
        <member name="M:EnumsNET.Enums.GetMember``1(System.String)">
            <summary>
            Retrieves the enum member with the specified <paramref name="name"/> if defined otherwise <c>null</c>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="name">The enum member name.</param>
            <returns>Enum member with the specified <paramref name="name"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <c>null</c>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMember``1(System.String,System.Boolean)">
            <summary>
            Retrieves the enum member with the specified <paramref name="name"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="name">The enum member name.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <returns>Enum member with the specified <paramref name="name"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <c>null</c>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMember``1(System.String,System.Boolean,EnumsNET.EnumFormat)">
            <summary>
            Retrieves an enum member whose string representation using the specified <paramref name="format"/> is <paramref name="value"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member's string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format">The parsing enum format.</param>
            <returns>Enum member represented by <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMember``1(System.String,System.Boolean,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Retrieves an enum member whose string representation using the specified formats is <paramref name="value"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member's string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>Enum member represented by <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMember``1(System.String,System.Boolean,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Retrieves an enum member whose string representation using the specified formats is <paramref name="value"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member's string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>Enum member represented by <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMember``1(System.String,System.Boolean,EnumsNET.EnumFormat[])">
            <summary>
            Retrieves an enum member whose string representation using the specified <paramref name="formats"/> is <paramref name="value"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member's string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>Enum member represented by <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Parse``1(System.String)">
            <summary>
            Converts the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member name or value of <typeparamref name="TEnum"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Parse``1(System.String,System.Boolean)">
            <summary>
            Converts the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member name or value of <typeparamref name="TEnum"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Parse``1(System.String,System.Boolean,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum format. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format">The parsing enum format.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="format"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Parse``1(System.String,System.Boolean,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Parse``1(System.String,System.Boolean,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Parse``1(System.String,System.Boolean,EnumsNET.EnumFormat[])">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParse``1(System.String,``0@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
        </member>
        <member name="M:EnumsNET.Enums.TryParse``1(System.String,System.Boolean,``0@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive. The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
        </member>
        <member name="M:EnumsNET.Enums.TryParse``1(System.String,System.Boolean,``0@,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum format. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format">The parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParse``1(System.String,System.Boolean,``0@,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParse``1(System.String,System.Boolean,``0@,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParse``1(System.String,System.Boolean,``0@,EnumsNET.EnumFormat[])">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetUnderlyingTypeUnsafe``1">
            <summary>
            Retrieves the underlying type of <typeparamref name="TEnum"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <returns>The underlying type of <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetTypeCodeUnsafe``1">
            <summary>
            Retrieves <typeparamref name="TEnum"/>'s underlying type's <see cref="T:System.TypeCode"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <returns><typeparamref name="TEnum"/>'s underlying type's <see cref="T:System.TypeCode"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMemberCountUnsafe``1(EnumsNET.EnumMemberSelection)">
            <summary>
            Retrieves <typeparamref name="TEnum"/>'s member count.
            The parameter <paramref name="selection"/> indicates what members to include.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="selection">Indicates what members to include.</param>
            <returns><typeparamref name="TEnum"/>'s member count.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="selection"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMembersUnsafe``1(EnumsNET.EnumMemberSelection)">
            <summary>
            Retrieves <typeparamref name="TEnum"/>'s members in increasing value order.
            The parameter <paramref name="selection"/> indicates what members to include.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="selection">Indicates what members to include.</param>
            <returns><typeparamref name="TEnum"/>'s members in increasing value order.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="selection"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetNamesUnsafe``1(EnumsNET.EnumMemberSelection)">
            <summary>
            Retrieves <typeparamref name="TEnum"/>'s members' names in increasing value order.
            The parameter <paramref name="selection"/> indicates what members to include.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="selection">Indicates what members to include.</param>
            <returns><typeparamref name="TEnum"/>'s members' names in increasing value order.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="selection"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetValuesUnsafe``1(EnumsNET.EnumMemberSelection)">
            <summary>
            Retrieves <typeparamref name="TEnum"/>'s members' values in increasing value order.
            The parameter <paramref name="selection"/> indicates what members to include.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="selection">Indicates what members to include.</param>
            <returns><typeparamref name="TEnum"/>'s members' values in increasing value order.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="selection"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObjectUnsafe``1(System.Object,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert. Must be an <see cref="T:System.SByte"/>, <see cref="T:System.Byte"/>, <see cref="T:System.Int16"/>, <see cref="T:System.UInt16"/>,
            <see cref="T:System.Int32"/>, <see cref="T:System.UInt32"/>, <see cref="T:System.Int64"/>, <see cref="T:System.UInt64"/>, <typeparamref name="TEnum"/>, <see cref="T:System.String"/>, or Nullable of one of these.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> is not a valid type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObjectUnsafe``1(System.SByte,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObjectUnsafe``1(System.Byte,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObjectUnsafe``1(System.Int16,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObjectUnsafe``1(System.UInt16,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObjectUnsafe``1(System.Int32,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObjectUnsafe``1(System.UInt32,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObjectUnsafe``1(System.Int64,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObjectUnsafe``1(System.UInt64,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObjectUnsafe``1(System.Object,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert. Must be an <see cref="T:System.SByte"/>, <see cref="T:System.Byte"/>, <see cref="T:System.Int16"/>, <see cref="T:System.UInt16"/>,
            <see cref="T:System.Int32"/>, <see cref="T:System.UInt32"/>, <see cref="T:System.Int64"/>, <see cref="T:System.UInt64"/>, <typeparamref name="TEnum"/>, <see cref="T:System.String"/>, or Nullable of one of these.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObjectUnsafe``1(System.SByte,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObjectUnsafe``1(System.Byte,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObjectUnsafe``1(System.Int16,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObjectUnsafe``1(System.UInt16,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObjectUnsafe``1(System.Int32,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObjectUnsafe``1(System.UInt32,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObjectUnsafe``1(System.Int64,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObjectUnsafe``1(System.UInt64,``0@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a <typeparamref name="TEnum"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.IsValidUnsafe``1(``0,EnumsNET.EnumValidation)">
            <summary>
            Indicates if <paramref name="value"/> is valid using the specified <paramref name="validation"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="validation">The validation to perform on the value.</param>
            <returns>Indication if <paramref name="value"/> is valid.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.IsDefinedUnsafe``1(``0)">
            <summary>
            Indicates if <paramref name="value"/> is defined.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns>Indication if <paramref name="value"/> is defined.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ValidateUnsafe``1(``0,System.String,EnumsNET.EnumValidation)">
            <summary>
            Validates that <paramref name="value"/> is valid using the specified <paramref name="validation"/>.
            If it's not it throws an <see cref="T:System.ArgumentException"/> with the specified <paramref name="paramName"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="paramName">The parameter name to be used if throwing an <see cref="T:System.ArgumentException"/>.</param>
            <param name="validation">The validation to perform on the value.</param>
            <returns><paramref name="value"/> for use in fluent API's and base constructor method calls.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            <paramref name="value"/> is invalid.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsStringUnsafe``1(``0)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsStringUnsafe``1(``0,System.String)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified <paramref name="format"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="format">The output format to use.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
            <exception cref="T:System.FormatException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsStringUnsafe``1(``0,EnumsNET.EnumFormat)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified <paramref name="format"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="format">The output format to use.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsStringUnsafe``1(``0,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified formats.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use if using the first resolves to <c>null</c>.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsStringUnsafe``1(``0,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified formats.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use if using the first resolves to <c>null</c>.</param>
            <param name="format2">The third output format to use if using the first and second both resolve to <c>null</c>.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsStringUnsafe``1(``0,EnumsNET.EnumFormat[])">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified <paramref name="formats"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="formats">The output formats to use.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.FormatUnsafe``1(``0,System.String)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified <paramref name="format"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="format">The output format to use.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="format"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
            <exception cref="T:System.FormatException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetUnderlyingValueUnsafe``1(``0)">
            <summary>
            Returns <paramref name="value"/>'s underlying integral value.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/>'s underlying integral value.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToSByteUnsafe``1(``0)">
            <summary>
            Converts <paramref name="value"/> to an <see cref="T:System.SByte"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to an <see cref="T:System.SByte"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.SByte"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToByteUnsafe``1(``0)">
            <summary>
            Converts <paramref name="value"/> to a <see cref="T:System.Byte"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to a <see cref="T:System.Byte"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.Byte"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToInt16Unsafe``1(``0)">
            <summary>
            Converts <paramref name="value"/> to an <see cref="T:System.Int16"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to an <see cref="T:System.Int16"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.Int16"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToUInt16Unsafe``1(``0)">
            <summary>
            Converts <paramref name="value"/> to a <see cref="T:System.UInt16"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to a <see cref="T:System.UInt16"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.UInt16"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToInt32Unsafe``1(``0)">
            <summary>
            Converts <paramref name="value"/> to an <see cref="T:System.Int32"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to an <see cref="T:System.Int32"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.Int32"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToUInt32Unsafe``1(``0)">
            <summary>
            Converts <paramref name="value"/> to a <see cref="T:System.UInt32"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to a <see cref="T:System.UInt32"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.UInt32"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToInt64Unsafe``1(``0)">
            <summary>
            Converts <paramref name="value"/> to an <see cref="T:System.Int64"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to an <see cref="T:System.Int64"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.Int64"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToUInt64Unsafe``1(``0)">
            <summary>
            Converts <paramref name="value"/> to a <see cref="T:System.UInt64"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to a <see cref="T:System.UInt64"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.UInt64"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetHashCodeUnsafe``1(``0)">
            <summary>
            Retrieves the hash code of <paramref name="value"/>. It's more efficient as it doesn't require boxing and unboxing of <paramref name="value"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns>Hash code of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.EqualsUnsafe``1(``0,``0)">
            <summary>
            Indicates if <paramref name="value"/> equals <paramref name="other"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="other">The other enum value.</param>
            <returns>Indication if <paramref name="value"/> equals <paramref name="other"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.CompareToUnsafe``1(``0,``0)">
            <summary>
            Compares <paramref name="value"/> to <paramref name="other"/> for ordering.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <param name="other">The other enum value.</param>
            <returns>1 if <paramref name="value"/> is greater than <paramref name="other"/>, 0 if <paramref name="value"/> equals <paramref name="other"/>,
            and -1 if <paramref name="value"/> is less than <paramref name="other"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetNameUnsafe``1(``0)">
            <summary>
            Retrieves <paramref name="value"/>'s enum member name if defined otherwise <c>null</c>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/>'s enum member name if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetAttributesUnsafe``1(``0)">
            <summary>
            Retrieves <paramref name="value"/>'s enum member attributes if defined otherwise <c>null</c>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/>'s enum member attributes if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMemberUnsafe``1(``0)">
            <summary>
            Retrieves an enum member with the specified <paramref name="value"/> if defined otherwise <c>null</c>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum value.</param>
            <returns>Enum member with the specified <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMemberUnsafe``1(System.String)">
            <summary>
            Retrieves the enum member with the specified <paramref name="name"/> if defined otherwise <c>null</c>.
            Is case-sensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="name">The enum member name.</param>
            <returns>Enum member with the specified <paramref name="name"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMemberUnsafe``1(System.String,System.Boolean)">
            <summary>
            Retrieves the enum member with the specified <paramref name="name"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="name">The enum member name.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <returns>Enum member with the specified <paramref name="name"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMemberUnsafe``1(System.String,System.Boolean,EnumsNET.EnumFormat)">
            <summary>
            Retrieves an enum member whose string representation using the specified <paramref name="format"/> is <paramref name="value"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member's string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format">The parsing enum format.</param>
            <returns>Enum member represented by <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMemberUnsafe``1(System.String,System.Boolean,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Retrieves an enum member whose string representation using the specified formats is <paramref name="value"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member's string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>Enum member represented by <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMemberUnsafe``1(System.String,System.Boolean,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Retrieves an enum member whose string representation using the specified formats is <paramref name="value"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member's string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>Enum member represented by <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMemberUnsafe``1(System.String,System.Boolean,EnumsNET.EnumFormat[])">
            <summary>
            Retrieves an enum member whose string representation using the specified <paramref name="formats"/> is <paramref name="value"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member's string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>Enum member represented by <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ParseUnsafe``1(System.String)">
            <summary>
            Converts the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <returns>A <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member name or value of <typeparamref name="TEnum"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of <typeparamref name="TEnum"/>'s underlying type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ParseUnsafe``1(System.String,System.Boolean)">
            <summary>
            Converts the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member name or value of <typeparamref name="TEnum"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ParseUnsafe``1(System.String,System.Boolean,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum format. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format">The parsing enum format.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="format"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ParseUnsafe``1(System.String,System.Boolean,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ParseUnsafe``1(System.String,System.Boolean,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ParseUnsafe``1(System.String,System.Boolean,EnumsNET.EnumFormat[])">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParseUnsafe``1(System.String,``0@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParseUnsafe``1(System.String,System.Boolean,``0@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive. The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParseUnsafe``1(System.String,System.Boolean,``0@,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum format. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format">The parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParseUnsafe``1(System.String,System.Boolean,``0@,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParseUnsafe``1(System.String,System.Boolean,``0@,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParseUnsafe``1(System.String,System.Boolean,``0@,EnumsNET.EnumFormat[])">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetUnderlyingType(System.Type)">
            <summary>
            Retrieves the underlying type of <paramref name="enumType"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <returns>The underlying type of <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetTypeCode(System.Type)">
            <summary>
            Retrieves <paramref name="enumType"/>'s underlying type's <see cref="T:System.TypeCode"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <returns><paramref name="enumType"/>'s underlying type's <see cref="T:System.TypeCode"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMemberCount(System.Type,EnumsNET.EnumMemberSelection)">
            <summary>
            Retrieves <paramref name="enumType"/>'s member count.
            The parameter <paramref name="selection"/> indicates what members to include.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="selection">Indicates what members to include.</param>
            <returns><paramref name="enumType"/>'s member count.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="selection"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMembers(System.Type,EnumsNET.EnumMemberSelection)">
            <summary>
            Retrieves <paramref name="enumType"/>'s members in increasing value order.
            The parameter <paramref name="selection"/> indicates what members to include.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="selection">Indicates what members to include.</param>
            <returns><paramref name="enumType"/>'s members in increasing value order.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="selection"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetNames(System.Type,EnumsNET.EnumMemberSelection)">
            <summary>
            Retrieves <paramref name="enumType"/>'s members' names in increasing value order.
            The parameter <paramref name="selection"/> indicates what members to include.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="selection">Indicates what members to include.</param>
            <returns><paramref name="enumType"/>'s members' names in increasing value order.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="selection"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetValues(System.Type,EnumsNET.EnumMemberSelection)">
            <summary>
            Retrieves <paramref name="enumType"/>'s members' values in increasing value order.
            The parameter <paramref name="selection"/> indicates what members to include.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="selection">Indicates what members to include.</param>
            <returns><paramref name="enumType"/>'s members' values in increasing value order.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="selection"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject(System.Type,System.Object,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to convert. Must be an <see cref="T:System.SByte"/>, <see cref="T:System.Byte"/>, <see cref="T:System.Int16"/>, <see cref="T:System.UInt16"/>,
            <see cref="T:System.Int32"/>, <see cref="T:System.UInt32"/>, <see cref="T:System.Int64"/>, <see cref="T:System.UInt64"/>, <paramref name="enumType"/>, <see cref="T:System.String"/>, or Nullable of one of these.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is not a valid type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject(System.Type,System.SByte,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject(System.Type,System.Byte,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject(System.Type,System.Int16,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject(System.Type,System.UInt16,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject(System.Type,System.Int32,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject(System.Type,System.UInt32,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject(System.Type,System.Int64,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToObject(System.Type,System.UInt64,EnumsNET.EnumValidation)">
            <summary>
            Converts the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to convert.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>The specified <paramref name="value"/> converted to a <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            the result is invalid with the specified <paramref name="validation"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the underlying type's value range.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject(System.Type,System.Object,System.Object@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to try to convert. Must be an <see cref="T:System.SByte"/>, <see cref="T:System.Byte"/>, <see cref="T:System.Int16"/>, <see cref="T:System.UInt16"/>,
            <see cref="T:System.Int32"/>, <see cref="T:System.UInt32"/>, <see cref="T:System.Int64"/>, <see cref="T:System.UInt64"/>, <paramref name="enumType"/>, <see cref="T:System.String"/>, or Nullable of one of these.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject(System.Type,System.SByte,System.Object@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject(System.Type,System.Byte,System.Object@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject(System.Type,System.Int16,System.Object@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject(System.Type,System.UInt16,System.Object@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject(System.Type,System.Int32,System.Object@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject(System.Type,System.UInt32,System.Object@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject(System.Type,System.Int64,System.Object@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryToObject(System.Type,System.UInt64,System.Object@,EnumsNET.EnumValidation)">
            <summary>
            Tries to convert the specified <paramref name="value"/> to a value of type <paramref name="enumType"/> while checking that it doesn't overflow the
            underlying type. The parameter <paramref name="validation"/> specifies the validation to perform on the result.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">Value to try to convert.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> whose value is <paramref name="value"/>.</param>
            <param name="validation">The validation to perform on the result.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.IsValid(System.Type,System.Object,EnumsNET.EnumValidation)">
            <summary>
            Indicates if <paramref name="value"/> is valid. If <paramref name="enumType"/> is a standard enum it returns whether the value is defined.
            If <paramref name="enumType"/> is marked with <see cref="T:System.FlagsAttribute"/> it returns whether it's a valid flag combination of <paramref name="enumType"/>'s defined values
            or is defined. Or if <paramref name="enumType"/> has an attribute that implements <see cref="T:EnumsNET.IEnumValidatorAttribute`1"/>
            then that attribute's <see cref="M:EnumsNET.IEnumValidatorAttribute`1.IsValid(`0)"/> method is used.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <param name="validation">The validation to perform on the value.</param>
            <returns>Indication if <paramref name="value"/> is valid.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type
            -or-
            <paramref name="validation"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.IsDefined(System.Type,System.Object)">
            <summary>
            Indicates if <paramref name="value"/> is defined.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns>Indication if <paramref name="value"/> is defined.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Validate(System.Type,System.Object,System.String,EnumsNET.EnumValidation)">
            <summary>
            Validates that <paramref name="value"/> is valid. If it's not it throws an <see cref="T:System.ArgumentException"/> with the specified <paramref name="paramName"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <param name="paramName">The parameter name to be used if throwing an <see cref="T:System.ArgumentException"/>.</param>
            <param name="validation">The validation to perform on the value.</param>
            <returns><paramref name="value"/> for use in fluent API's and base constructor method calls.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type
            -or-
            <paramref name="validation"/> is an invalid value
            -or-
            <paramref name="value"/> is invalid.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsString(System.Type,System.Object)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsString(System.Type,System.Object,System.String)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified <paramref name="format"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <param name="format">The output format to use.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
            <exception cref="T:System.FormatException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsString(System.Type,System.Object,EnumsNET.EnumFormat)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified <paramref name="format"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <param name="format">The output format to use.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsString(System.Type,System.Object,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified formats.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use if using the first resolves to <c>null</c>.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsString(System.Type,System.Object,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified formats.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use if using the first resolves to <c>null</c>.</param>
            <param name="format2">The third output format to use if using the first and second both resolve to <c>null</c>.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.AsString(System.Type,System.Object,EnumsNET.EnumFormat[])">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified <paramref name="formats"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <param name="formats">The output formats to use.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Format(System.Type,System.Object,System.String)">
            <summary>
            Converts the specified <paramref name="value"/> to its string representation using the specified <paramref name="format"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <param name="format">The output format to use.</param>
            <returns>A string representation of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/>, <paramref name="value"/>, or <paramref name="format"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
            <exception cref="T:System.FormatException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetUnderlyingValue(System.Type,System.Object)">
            <summary>
            Returns <paramref name="value"/>'s underlying integral value.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/>'s underlying integral value.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToSByte(System.Type,System.Object)">
            <summary>
            Converts <paramref name="value"/> to an <see cref="T:System.SByte"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to an <see cref="T:System.SByte"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.SByte"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToByte(System.Type,System.Object)">
            <summary>
            Converts <paramref name="value"/> to a <see cref="T:System.Byte"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to a <see cref="T:System.Byte"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.Byte"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToInt16(System.Type,System.Object)">
            <summary>
            Converts <paramref name="value"/> to an <see cref="T:System.Int16"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to an <see cref="T:System.Int16"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.Int16"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToUInt16(System.Type,System.Object)">
            <summary>
            Converts <paramref name="value"/> to a <see cref="T:System.UInt16"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to a <see cref="T:System.UInt16"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.UInt16"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToInt32(System.Type,System.Object)">
            <summary>
            Converts <paramref name="value"/> to an <see cref="T:System.Int32"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to an <see cref="T:System.Int32"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.Int32"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToUInt32(System.Type,System.Object)">
            <summary>
            Converts <paramref name="value"/> to a <see cref="T:System.UInt32"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to a <see cref="T:System.UInt32"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.UInt32"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToInt64(System.Type,System.Object)">
            <summary>
            Converts <paramref name="value"/> to an <see cref="T:System.Int64"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to an <see cref="T:System.Int64"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.Int64"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.ToUInt64(System.Type,System.Object)">
            <summary>
            Converts <paramref name="value"/> to a <see cref="T:System.UInt64"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/> converted to a <see cref="T:System.UInt64"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> cannot fit within <see cref="T:System.UInt64"/>'s value range without overflowing.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Equals(System.Type,System.Object,System.Object)">
            <summary>
            Indicates if <paramref name="value"/> equals <paramref name="other"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <param name="other">The other enum value.</param>
            <returns>Indication if <paramref name="value"/> equals <paramref name="other"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/>, <paramref name="value"/>, or <paramref name="other"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> or <paramref name="other"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.CompareTo(System.Type,System.Object,System.Object)">
            <summary>
            Compares <paramref name="value"/> to <paramref name="other"/> for ordering.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <param name="other">The other enum value.</param>
            <returns>1 if <paramref name="value"/> is greater than <paramref name="other"/>, 0 if <paramref name="value"/> equals <paramref name="other"/>,
            and -1 if <paramref name="value"/> is less than <paramref name="other"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/>, <paramref name="value"/>, or <paramref name="other"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> or <paramref name="other"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetName(System.Type,System.Object)">
            <summary>
            Retrieves <paramref name="value"/>'s enum member name if defined otherwise <c>null</c>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/>'s enum member name if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetAttributes(System.Type,System.Object)">
            <summary>
            Retrieves <paramref name="value"/>'s enum member attributes if defined otherwise <c>null</c>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns><paramref name="value"/>'s enum member attributes if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMember(System.Type,System.Object)">
            <summary>
            Retrieves an enum member with the specified <paramref name="value"/> if defined otherwise <c>null</c>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum value.</param>
            <returns>Enum member with the specified <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMember(System.Type,System.String)">
            <summary>
            Retrieves the enum member with the specified <paramref name="name"/> if defined otherwise <c>null</c>.
            Is case-sensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="name">The enum member name.</param>
            <returns>Enum member with the specified <paramref name="name"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="name"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMember(System.Type,System.String,System.Boolean)">
            <summary>
            Retrieves the enum member with the specified <paramref name="name"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="name">The enum member name.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <returns>Enum member with the specified <paramref name="name"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="name"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMember(System.Type,System.String,System.Boolean,EnumsNET.EnumFormat)">
            <summary>
            Retrieves an enum member whose string representation using the specified <paramref name="format"/> is <paramref name="value"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member's string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format">The parsing enum format.</param>
            <returns>Enum member represented by <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMember(System.Type,System.String,System.Boolean,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Retrieves an enum member whose string representation using the specified formats is <paramref name="value"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member's string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>Enum member represented by <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMember(System.Type,System.String,System.Boolean,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Retrieves an enum member whose string representation using the specified formats is <paramref name="value"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member's string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>Enum member represented by <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.GetMember(System.Type,System.String,System.Boolean,EnumsNET.EnumFormat[])">
            <summary>
            Retrieves an enum member whose string representation using the specified <paramref name="formats"/> is <paramref name="value"/> if defined otherwise <c>null</c>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member's string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>Enum member represented by <paramref name="value"/> if defined otherwise <c>null</c>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Parse(System.Type,System.String)">
            <summary>
            Converts the string representation of one or more member names or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member names or values' string representation.</param>
            <returns>A <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member name or value of <paramref name="enumType"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of <paramref name="enumType"/>'s underlying type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Parse(System.Type,System.String,System.Boolean)">
            <summary>
            Converts the string representation of one or more member names or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>.
            The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <returns>The <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member name or value of <paramref name="enumType"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <paramref name="enumType"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Parse(System.Type,System.String,System.Boolean,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum format. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format">The parsing enum format.</param>
            <returns>The <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <paramref name="enumType"/>
            -or-
            <paramref name="format"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <paramref name="enumType"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Parse(System.Type,System.String,System.Boolean,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>The <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <paramref name="enumType"/>
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <paramref name="enumType"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Parse(System.Type,System.String,System.Boolean,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>The <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <paramref name="enumType"/>
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <paramref name="enumType"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.Parse(System.Type,System.String,System.Boolean,EnumsNET.EnumFormat[])">
            <summary>
            Converts the string representation of one or more members or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>The <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <paramref name="enumType"/>
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <paramref name="enumType"/>.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParse(System.Type,System.String,System.Object@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="result">If the conversion succeeds this contains a <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParse(System.Type,System.String,System.Boolean,System.Object@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive. The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParse(System.Type,System.String,System.Boolean,System.Object@,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum format. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format">The parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParse(System.Type,System.String,System.Boolean,System.Object@,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParse(System.Type,System.String,System.Boolean,System.Object@,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.Enums.TryParse(System.Type,System.String,System.Boolean,System.Object@,EnumsNET.EnumFormat[])">
            <summary>
            Tries to convert the string representation of one or more members or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="T:EnumsNET.EnumValidation">
            <summary>
            Specifies the enum validation to perform.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumValidation.None">
            <summary>
            No validation.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumValidation.Default">
            <summary>
            If the enum is a standard enum returns whether the value is defined.
            If the enum is marked with the <see cref="T:System.FlagsAttribute"/> it returns whether it's a valid flag combination of the enum's defined values
            or is defined. Or if the enum has an attribute that implements <see cref="T:EnumsNET.IEnumValidatorAttribute`1"/>
            then that attribute's <see cref="M:EnumsNET.IEnumValidatorAttribute`1.IsValid(`0)"/> method is used.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumValidation.IsDefined">
            <summary>
            Returns if the value is defined.
            </summary>
        </member>
        <member name="F:EnumsNET.EnumValidation.IsValidFlagCombination">
            <summary>
            Returns if the value is a valid flag combination of the enum's defined values.
            </summary>
        </member>
        <member name="T:EnumsNET.FlagEnums">
            <summary>
            Static class that provides efficient type-safe flag enum operations through the use of cached enum names, values, and attributes.
            Many operations are exposed as C# extension methods for convenience.
            </summary>
        </member>
        <member name="M:EnumsNET.FlagEnums.IsFlagEnum``1">
            <summary>
            Indicates if <typeparamref name="TEnum"/> is marked with the <see cref="T:System.FlagsAttribute"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <returns>Indication if <typeparamref name="TEnum"/> is marked with the <see cref="T:System.FlagsAttribute"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetAllFlags``1">
            <summary>
            Retrieves all the flags defined by <typeparamref name="TEnum"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <returns>All the flags defined by <typeparamref name="TEnum"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.IsValidFlagCombination``1(``0)">
            <summary>
            Indicates whether <paramref name="value"/> is a valid flag combination of <typeparamref name="TEnum"/>'s defined flags.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>Indication of whether <paramref name="value"/> is a valid flag combination of <typeparamref name="TEnum"/>'s defined flags.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlags``1(``0)">
            <summary>
            Retrieves the names of <paramref name="value"/>'s flags delimited with commas or if empty returns the name of the zero flag if defined otherwise "0".
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>The names of <paramref name="value"/>'s flags delimited with commas or if empty returns the name of the zero flag if defined otherwise "0".
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlags``1(``0,System.String)">
            <summary>
            Retrieves the names of <paramref name="value"/>'s flags delimited with <paramref name="delimiter"/> or if empty returns the name of the zero flag if defined otherwise "0".
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <returns>The names of <paramref name="value"/>'s flags delimited with <paramref name="delimiter"/> or if empty returns the name of the zero flag if defined otherwise "0".
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlags``1(``0,System.String,EnumsNET.EnumFormat)">
            <summary>
            Retrieves <paramref name="value"/>'s flags formatted with <paramref name="format"/> and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with <paramref name="format"/>.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <param name="format">The output format to use.</param>
            <returns><paramref name="value"/>'s flags formatted with <paramref name="format"/> and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with <paramref name="format"/>.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlags``1(``0,System.String,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Retrieves <paramref name="value"/>'s flags formatted with formats and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with formats.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use.</param>
            <returns><paramref name="value"/>'s flags formatted with formats and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with formats.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlags``1(``0,System.String,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Retrieves <paramref name="value"/>'s flags formatted with formats and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with formats.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use.</param>
            <param name="format2">The third output format to use.</param>
            <returns><paramref name="value"/>'s flags formatted with formats and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with formats.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlags``1(``0,System.String,EnumsNET.EnumFormat[])">
            <summary>
            Retrieves <paramref name="value"/>'s flags formatted with <paramref name="formats"/> and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with <paramref name="formats"/>.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <param name="formats">The output formats to use.</param>
            <returns><paramref name="value"/>'s flags formatted with <paramref name="formats"/> and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with <paramref name="formats"/>.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlags``1(``0)">
            <summary>
            Retrieves the flags that compose <paramref name="value"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>The flags that compose <paramref name="value"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagMembers``1(``0)">
            <summary>
            Retrieves the <see cref="T:EnumsNET.EnumMember`1"/>s of the flags that compose <paramref name="value"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>The <see cref="T:EnumsNET.EnumMember`1"/>s of the flags that compose <paramref name="value"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagCount``1">
            <summary>
            Retrieves the flag count of <typeparamref name="TEnum"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <returns>The flag count of <typeparamref name="TEnum"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagCount``1(``0)">
            <summary>
            Retrieves the flag count of <paramref name="value"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>The flag count of <paramref name="value"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagCount``1(``0,``0)">
            <summary>
            Retrieves the flag count of <paramref name="otherFlags"/> that <paramref name="value"/> has.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns>The flag count of <paramref name="otherFlags"/> that <paramref name="value"/> has.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAnyFlags``1(``0)">
            <summary>
            Indicates if <paramref name="value"/> has any flags.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>Indication if <paramref name="value"/> has any flags.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAnyFlags``1(``0,``0)">
            <summary>
            Indicates if <paramref name="value"/> has any flags that are in <paramref name="otherFlags"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns>Indication if <paramref name="value"/> has any flags that are in <paramref name="otherFlags"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAllFlags``1(``0)">
            <summary>
            Indicates if <paramref name="value"/> has all of the flags that are defined in <typeparamref name="TEnum"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>Indication if <paramref name="value"/> has all of the flags that are defined in <typeparamref name="TEnum"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAllFlags``1(``0,``0)">
            <summary>
            Indicates if <paramref name="value"/> has all of the flags that are in <paramref name="otherFlags"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns>Indication if <paramref name="value"/> has all of the flags that are in <paramref name="otherFlags"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.ToggleFlags``1(``0)">
            <summary>
            Returns <paramref name="value"/> with all of it's flags toggled. Equivalent to the bitwise "xor" operator with <see cref="M:EnumsNET.FlagEnums.GetAllFlags``1"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns><paramref name="value"/> with all of it's flags toggled.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.ToggleFlags``1(``0,``0)">
            <summary>
            Returns <paramref name="value"/> while toggling the flags that are in <paramref name="otherFlags"/>. Equivalent to the bitwise "xor" operator.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns><paramref name="value"/> while toggling the flags that are in <paramref name="otherFlags"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.CommonFlags``1(``0,``0)">
            <summary>
            Returns <paramref name="value"/> with only the flags that are also in <paramref name="otherFlags"/>. Equivalent to the bitwise "and" operation.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns><paramref name="value"/> with only the flags that are also in <paramref name="otherFlags"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlags``1(``0,``0)">
            <summary>
            Combines the flags of <paramref name="value"/> and <paramref name="otherFlags"/>. Equivalent to the bitwise "or" operation.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns>Combination of <paramref name="value"/> with the flags in <paramref name="otherFlags"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlags``1(``0,``0,``0)">
            <summary>
            Combines the flags of <paramref name="flag0"/>, <paramref name="flag1"/>, and <paramref name="flag2"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="flag0">The first flags enum value.</param>
            <param name="flag1">The second flags enum value.</param>
            <param name="flag2">The third flags enum value.</param>
            <returns>Combination of the flags of <paramref name="flag0"/>, <paramref name="flag1"/>, and <paramref name="flag2"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlags``1(``0,``0,``0,``0)">
            <summary>
            Combines the flags of <paramref name="flag0"/>, <paramref name="flag1"/>, <paramref name="flag2"/>, and <paramref name="flag3"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="flag0">The first flags enum value.</param>
            <param name="flag1">The second flags enum value.</param>
            <param name="flag2">The third flags enum value.</param>
            <param name="flag3">The fourth flags enum value.</param>
            <returns>Combination of the flags of <paramref name="flag0"/>, <paramref name="flag1"/>, <paramref name="flag2"/>, and <paramref name="flag3"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlags``1(``0,``0,``0,``0,``0)">
            <summary>
            Combines the flags of <paramref name="flag0"/>, <paramref name="flag1"/>, <paramref name="flag2"/>, <paramref name="flag3"/>, and <paramref name="flag4"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="flag0">The first flags enum value.</param>
            <param name="flag1">The second flags enum value.</param>
            <param name="flag2">The third flags enum value.</param>
            <param name="flag3">The fourth flags enum value.</param>
            <param name="flag4">The fifth flags enum value.</param>
            <returns>Combination of the flags of <paramref name="flag0"/>, <paramref name="flag1"/>, <paramref name="flag2"/>, <paramref name="flag3"/>, and <paramref name="flag4"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlags``1(``0[])">
            <summary>
            Combines all of the flags of <paramref name="flags"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="flags">The flags enum values.</param>
            <returns>Combination of all of the flags of <paramref name="flags"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlags``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Combines all of the flags of <paramref name="flags"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="flags">The flags enum values.</param>
            <returns>Combination of all of the flags of <paramref name="flags"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.RemoveFlags``1(``0,``0)">
            <summary>
            Returns <paramref name="value"/> without the flags specified in <paramref name="otherFlags"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns><paramref name="value"/> without the flags specified in <paramref name="otherFlags"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags``1(System.String)">
            <summary>
            Converts the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <returns>A <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member name or value of <typeparamref name="TEnum"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of <typeparamref name="TEnum"/>'s underlying type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags``1(System.String,System.Boolean)">
            <summary>
            Converts the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member name or value of <typeparamref name="TEnum"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags``1(System.String,System.Boolean,System.String)">
            <summary>
            Converts the string representation of one or more member names or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value.
            The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member name or value of <typeparamref name="TEnum"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags``1(System.String,System.Boolean,System.String,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum format. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="format">The parsing enum format.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="format"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags``1(System.String,System.Boolean,System.String,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags``1(System.String,System.Boolean,System.String,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags``1(System.String,System.Boolean,System.String,EnumsNET.EnumFormat[])">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags``1(System.String,``0@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags``1(System.String,System.Boolean,``0@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive. The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags``1(System.String,System.Boolean,System.String,``0@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive. The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags``1(System.String,System.Boolean,System.String,``0@,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum format. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format">The parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags``1(System.String,System.Boolean,System.String,``0@,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The first parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags``1(System.String,System.Boolean,System.String,``0@,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags``1(System.String,System.Boolean,System.String,``0@,EnumsNET.EnumFormat[])">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.IsFlagEnumUnsafe``1">
            <summary>
            Indicates if <typeparamref name="TEnum"/> is marked with the <see cref="T:System.FlagsAttribute"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <returns>Indication if <typeparamref name="TEnum"/> is marked with the <see cref="T:System.FlagsAttribute"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetAllFlagsUnsafe``1">
            <summary>
            Retrieves all the flags defined by <typeparamref name="TEnum"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <returns>All the flags defined by <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.IsValidFlagCombinationUnsafe``1(``0)">
            <summary>
            Indicates whether <paramref name="value"/> is a valid flag combination of <typeparamref name="TEnum"/>'s defined flags.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>Indication of whether <paramref name="value"/> is a valid flag combination of <typeparamref name="TEnum"/>'s defined flags.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlagsUnsafe``1(``0)">
            <summary>
            Retrieves the names of <paramref name="value"/>'s flags delimited with commas or if empty returns the name of the zero flag if defined otherwise "0".
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>The names of <paramref name="value"/>'s flags delimited with commas or if empty returns the name of the zero flag if defined otherwise "0".
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlagsUnsafe``1(``0,System.String)">
            <summary>
            Retrieves the names of <paramref name="value"/>'s flags delimited with <paramref name="delimiter"/> or if empty returns the name of the zero flag if defined otherwise "0".
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <returns>The names of <paramref name="value"/>'s flags delimited with <paramref name="delimiter"/> or if empty returns the name of the zero flag if defined otherwise "0".
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlagsUnsafe``1(``0,System.String,EnumsNET.EnumFormat)">
            <summary>
            Retrieves <paramref name="value"/>'s flags formatted with <paramref name="format"/> and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with <paramref name="format"/>.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <param name="format">The output format to use.</param>
            <returns><paramref name="value"/>'s flags formatted with <paramref name="format"/> and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with <paramref name="format"/>.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlagsUnsafe``1(``0,System.String,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Retrieves <paramref name="value"/>'s flags formatted with formats and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with formats.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use.</param>
            <returns><paramref name="value"/>'s flags formatted with formats and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with formats.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlagsUnsafe``1(``0,System.String,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Retrieves <paramref name="value"/>'s flags formatted with formats and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with formats.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use.</param>
            <param name="format2">The third output format to use.</param>
            <returns><paramref name="value"/>'s flags formatted with formats and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with formats.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlagsUnsafe``1(``0,System.String,EnumsNET.EnumFormat[])">
            <summary>
            Retrieves <paramref name="value"/>'s flags formatted with <paramref name="formats"/> and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with <paramref name="formats"/>.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <param name="formats">The output formats to use.</param>
            <returns><paramref name="value"/>'s flags formatted with <paramref name="formats"/> and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with <paramref name="formats"/>.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagsUnsafe``1(``0)">
            <summary>
            Retrieves the flags that compose <paramref name="value"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>The flags that compose <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagMembersUnsafe``1(``0)">
            <summary>
            Retrieves the <see cref="T:EnumsNET.EnumMember`1"/>s of the flags that compose <paramref name="value"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>The <see cref="T:EnumsNET.EnumMember`1"/>s of the flags that compose <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagCountUnsafe``1">
            <summary>
            Retrieves the flag count of <typeparamref name="TEnum"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <returns>The flag count of <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagCountUnsafe``1(``0)">
            <summary>
            Retrieves the flag count of <paramref name="value"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>The flag count of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagCountUnsafe``1(``0,``0)">
            <summary>
            Retrieves the flag count of <paramref name="otherFlags"/> that <paramref name="value"/> has.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns>The flag count of <paramref name="otherFlags"/> that <paramref name="value"/> has.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAnyFlagsUnsafe``1(``0)">
            <summary>
            Indicates if <paramref name="value"/> has any flags.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>Indication if <paramref name="value"/> has any flags.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAnyFlagsUnsafe``1(``0,``0)">
            <summary>
            Indicates if <paramref name="value"/> has any flags that are in <paramref name="otherFlags"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns>Indication if <paramref name="value"/> has any flags that are in <paramref name="otherFlags"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAllFlagsUnsafe``1(``0)">
            <summary>
            Indicates if <paramref name="value"/> has all of the flags that are defined in <typeparamref name="TEnum"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns>Indication if <paramref name="value"/> has all of the flags that are defined in <typeparamref name="TEnum"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAllFlagsUnsafe``1(``0,``0)">
            <summary>
            Indicates if <paramref name="value"/> has all of the flags that are in <paramref name="otherFlags"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns>Indication if <paramref name="value"/> has all of the flags that are in <paramref name="otherFlags"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ToggleFlagsUnsafe``1(``0)">
            <summary>
            Returns <paramref name="value"/> with all of it's flags toggled. Equivalent to the bitwise "xor" operator with <see cref="M:EnumsNET.FlagEnums.GetAllFlags``1"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <returns><paramref name="value"/> with all of it's flags toggled.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ToggleFlagsUnsafe``1(``0,``0)">
            <summary>
            Returns <paramref name="value"/> while toggling the flags that are in <paramref name="otherFlags"/>. Equivalent to the bitwise "xor" operator.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns><paramref name="value"/> while toggling the flags that are in <paramref name="otherFlags"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.CommonFlagsUnsafe``1(``0,``0)">
            <summary>
            Returns <paramref name="value"/> with only the flags that are also in <paramref name="otherFlags"/>. Equivalent to the bitwise "and" operation.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns><paramref name="value"/> with only the flags that are also in <paramref name="otherFlags"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlagsUnsafe``1(``0,``0)">
            <summary>
            Combines the flags of <paramref name="value"/> and <paramref name="otherFlags"/>. Equivalent to the bitwise "or" operation.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns>Combination of <paramref name="value"/> with the flags in <paramref name="otherFlags"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlagsUnsafe``1(``0,``0,``0)">
            <summary>
            Combines the flags of <paramref name="flag0"/>, <paramref name="flag1"/>, and <paramref name="flag2"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="flag0">The first flags enum value.</param>
            <param name="flag1">The second flags enum value.</param>
            <param name="flag2">The third flags enum value.</param>
            <returns>Combination of the flags of <paramref name="flag0"/>, <paramref name="flag1"/>, and <paramref name="flag2"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlagsUnsafe``1(``0,``0,``0,``0)">
            <summary>
            Combines the flags of <paramref name="flag0"/>, <paramref name="flag1"/>, <paramref name="flag2"/>, and <paramref name="flag3"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="flag0">The first flags enum value.</param>
            <param name="flag1">The second flags enum value.</param>
            <param name="flag2">The third flags enum value.</param>
            <param name="flag3">The fourth flags enum value.</param>
            <returns>Combination of the flags of <paramref name="flag0"/>, <paramref name="flag1"/>, <paramref name="flag2"/>, and <paramref name="flag3"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlagsUnsafe``1(``0,``0,``0,``0,``0)">
            <summary>
            Combines the flags of <paramref name="flag0"/>, <paramref name="flag1"/>, <paramref name="flag2"/>, <paramref name="flag3"/>, and <paramref name="flag4"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="flag0">The first flags enum value.</param>
            <param name="flag1">The second flags enum value.</param>
            <param name="flag2">The third flags enum value.</param>
            <param name="flag3">The fourth flags enum value.</param>
            <param name="flag4">The fifth flags enum value.</param>
            <returns>Combination of the flags of <paramref name="flag0"/>, <paramref name="flag1"/>, <paramref name="flag2"/>, <paramref name="flag3"/>, and <paramref name="flag4"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlagsUnsafe``1(``0[])">
            <summary>
            Combines all of the flags of <paramref name="flags"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="flags">The flags enum values.</param>
            <returns>Combination of all of the flags of <paramref name="flags"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlagsUnsafe``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Combines all of the flags of <paramref name="flags"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="flags">The flags enum values.</param>
            <returns>Combination of all of the flags of <paramref name="flags"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.RemoveFlagsUnsafe``1(``0,``0)">
            <summary>
            Returns <paramref name="value"/> without the flags specified in <paramref name="otherFlags"/>.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns><paramref name="value"/> without the flags specified in <paramref name="otherFlags"/>.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlagsUnsafe``1(System.String)">
            <summary>
            Converts the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <returns>A <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member name or value of <typeparamref name="TEnum"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of <typeparamref name="TEnum"/>'s underlying type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlagsUnsafe``1(System.String,System.Boolean)">
            <summary>
            Converts the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member name or value of <typeparamref name="TEnum"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlagsUnsafe``1(System.String,System.Boolean,System.String)">
            <summary>
            Converts the string representation of one or more member names or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value.
            The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member name or value of <typeparamref name="TEnum"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlagsUnsafe``1(System.String,System.Boolean,System.String,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum format. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="format">The parsing enum format.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="format"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlagsUnsafe``1(System.String,System.Boolean,System.String,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlagsUnsafe``1(System.String,System.Boolean,System.String,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlagsUnsafe``1(System.String,System.Boolean,System.String,EnumsNET.EnumFormat[])">
            <summary>
            Converts the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>The <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <typeparamref name="TEnum"/>
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <typeparamref name="TEnum"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlagsUnsafe``1(System.String,``0@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlagsUnsafe``1(System.String,System.Boolean,``0@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <typeparamref name="TEnum"/> to its respective <typeparamref name="TEnum"/> value.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive. The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlagsUnsafe``1(System.String,System.Boolean,System.String,``0@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive. The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlagsUnsafe``1(System.String,System.Boolean,System.String,``0@,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum format. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format">The parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlagsUnsafe``1(System.String,System.Boolean,System.String,``0@,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlagsUnsafe``1(System.String,System.Boolean,System.String,``0@,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlagsUnsafe``1(System.String,System.Boolean,System.String,``0@,EnumsNET.EnumFormat[])">
            <summary>
            Tries to convert the string representation of one or more members or values of <typeparamref name="TEnum"/> delimited with <paramref name="delimiter"/> to its respective <typeparamref name="TEnum"/> value
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a <typeparamref name="TEnum"/> value that is represented by <paramref name="value"/>.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentException"><typeparamref name="TEnum"/> is not an enum type
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.IsFlagEnum(System.Type)">
            <summary>
            Indicates if <paramref name="enumType"/> is marked with the <see cref="T:System.FlagsAttribute"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <returns>Indication if <paramref name="enumType"/> is marked with the <see cref="T:System.FlagsAttribute"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetAllFlags(System.Type)">
            <summary>
            Retrieves all the flags defined by <paramref name="enumType"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <returns>All the flags defined by <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.IsValidFlagCombination(System.Type,System.Object)">
            <summary>
            Indicates whether <paramref name="value"/> is a valid flag combination of <paramref name="enumType"/>'s defined flags.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <returns>Indication of whether <paramref name="value"/> is a valid flag combination of <paramref name="enumType"/>'s defined flags.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlags(System.Type,System.Object)">
            <summary>
            Retrieves the names of <paramref name="value"/>'s flags delimited with commas or if empty returns the name of the zero flag if defined otherwise "0".
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <returns>The names of <paramref name="value"/>'s flags delimited with commas or if empty returns the name of the zero flag if defined otherwise "0".
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlags(System.Type,System.Object,System.String)">
            <summary>
            Retrieves the names of <paramref name="value"/>'s flags delimited with <paramref name="delimiter"/> or if empty returns the name of the zero flag if defined otherwise "0".
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <returns>The names of <paramref name="value"/>'s flags delimited with <paramref name="delimiter"/> or if empty returns the name of the zero flag if defined otherwise "0".
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlags(System.Type,System.Object,System.String,EnumsNET.EnumFormat)">
            <summary>
            Retrieves <paramref name="value"/>'s flags formatted with <paramref name="format"/> and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with <paramref name="format"/>.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <param name="format">The output format to use.</param>
            <returns><paramref name="value"/>'s flags formatted with <paramref name="format"/> and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with <paramref name="format"/>.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type
            -or-
            <paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlags(System.Type,System.Object,System.String,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Retrieves <paramref name="value"/>'s flags formatted with formats and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with formats.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use.</param>
            <returns><paramref name="value"/>'s flags formatted with formats and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with formats.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlags(System.Type,System.Object,System.String,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Retrieves <paramref name="value"/>'s flags formatted with formats and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with formats.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <param name="format0">The first output format to use.</param>
            <param name="format1">The second output format to use.</param>
            <param name="format2">The third output format to use.</param>
            <returns><paramref name="value"/>'s flags formatted with formats and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with formats.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.FormatFlags(System.Type,System.Object,System.String,EnumsNET.EnumFormat[])">
            <summary>
            Retrieves <paramref name="value"/>'s flags formatted with <paramref name="formats"/> and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with <paramref name="formats"/>.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <param name="delimiter">The delimiter to use to separate individual flags.</param>
            <param name="formats">The output formats to use.</param>
            <returns><paramref name="value"/>'s flags formatted with <paramref name="formats"/> and delimited with <paramref name="delimiter"/>
            or if empty returns the zero flag formatted with <paramref name="formats"/>.
            If <paramref name="value"/> is not a valid flag combination <c>null</c> is returned.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlags(System.Type,System.Object)">
            <summary>
            Retrieves the flags that compose <paramref name="value"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <returns>The flags that compose <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagMembers(System.Type,System.Object)">
            <summary>
            Retrieves the <see cref="T:EnumsNET.EnumMember"/>s of the flags that compose <paramref name="value"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <returns>The <see cref="T:EnumsNET.EnumMember"/>s of the flags that compose <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagCount(System.Type)">
            <summary>
            Retrieves the flag count of <paramref name="enumType"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <returns>The flag count of <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagCount(System.Type,System.Object)">
            <summary>
            Retrieves the flag count of <paramref name="value"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <returns>The flag count of <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagCount(System.Type,System.Object,System.Object)">
            <summary>
            Retrieves the flag count of <paramref name="otherFlags"/> that <paramref name="value"/> has.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns>The flag count of <paramref name="otherFlags"/> that <paramref name="value"/> has.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/>, <paramref name="value"/>, or <paramref name="otherFlags"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> or <paramref name="otherFlags"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAnyFlags(System.Type,System.Object)">
            <summary>
            Indicates if <paramref name="value"/> has any flags.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <returns>Indication if <paramref name="value"/> has any flags.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAnyFlags(System.Type,System.Object,System.Object)">
            <summary>
            Indicates if <paramref name="value"/> has any flags that are in <paramref name="otherFlags"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns>Indication if <paramref name="value"/> has any flags that are in <paramref name="otherFlags"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/>, <paramref name="value"/>, or <paramref name="otherFlags"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> or <paramref name="otherFlags"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAllFlags(System.Type,System.Object)">
            <summary>
            Indicates if <paramref name="value"/> has all of the flags that are defined in <paramref name="enumType"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <returns>Indication if <paramref name="value"/> has all of the flags that are defined in <paramref name="enumType"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAllFlags(System.Type,System.Object,System.Object)">
            <summary>
            Indicates if <paramref name="value"/> has all of the flags that are in <paramref name="otherFlags"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns>Indication if <paramref name="value"/> has all of the flags that are in <paramref name="otherFlags"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/>, <paramref name="value"/>, or <paramref name="otherFlags"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> or <paramref name="otherFlags"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ToggleFlags(System.Type,System.Object)">
            <summary>
            Returns <paramref name="value"/> with all of it's flags toggled. Equivalent to the bitwise "xor" operator with <see cref="M:EnumsNET.FlagEnums.GetAllFlags(System.Type)"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <returns><paramref name="value"/> with all of it's flags toggled.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ToggleFlags(System.Type,System.Object,System.Object)">
            <summary>
            Returns <paramref name="value"/> while toggling the flags that are in <paramref name="otherFlags"/>. Equivalent to the bitwise "xor" operator.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns><paramref name="value"/> while toggling the flags that are in <paramref name="otherFlags"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/>, <paramref name="value"/>, or <paramref name="otherFlags"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> or <paramref name="otherFlags"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.CommonFlags(System.Type,System.Object,System.Object)">
            <summary>
            Returns <paramref name="value"/> with only the flags that are also in <paramref name="otherFlags"/>. Equivalent to the bitwise "and" operation.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns><paramref name="value"/> with only the flags that are also in <paramref name="otherFlags"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/>, <paramref name="value"/>, or <paramref name="otherFlags"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> or <paramref name="otherFlags"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlags(System.Type,System.Object,System.Object)">
            <summary>
            Combines the flags of <paramref name="value"/> and <paramref name="otherFlags"/>. Equivalent to the bitwise "or" operation.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns>Combination of <paramref name="value"/> with the flags in <paramref name="otherFlags"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/>, <paramref name="value"/>, or <paramref name="otherFlags"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> or <paramref name="otherFlags"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlags(System.Type,System.Object[])">
            <summary>
            Combines all of the flags of <paramref name="flags"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="flags">The flags enum values.</param>
            <returns>Combination of all of the flags of <paramref name="flags"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or one of the <paramref name="flags"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="flags"/> contains a value that is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.CombineFlags(System.Type,System.Collections.Generic.IEnumerable{System.Object})">
            <summary>
            Combines all of the flags of <paramref name="flags"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="flags">The flags enum values.</param>
            <returns>Combination of all of the flags of <paramref name="flags"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or one of the <paramref name="flags"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="flags"/> contains a value that is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.RemoveFlags(System.Type,System.Object,System.Object)">
            <summary>
            Returns <paramref name="value"/> without the flags specified in <paramref name="otherFlags"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The flags enum value.</param>
            <param name="otherFlags">The other flags enum value.</param>
            <returns><paramref name="value"/> without the flags specified in <paramref name="otherFlags"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/>, <paramref name="value"/>, or <paramref name="otherFlags"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> or <paramref name="otherFlags"/> is of an invalid type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags(System.Type,System.String)">
            <summary>
            Converts the string representation of one or more member names or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member names or values' string representation.</param>
            <returns>A <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member name or value of <paramref name="enumType"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of <paramref name="enumType"/>'s underlying type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags(System.Type,System.String,System.Boolean)">
            <summary>
            Converts the string representation of one or more member names or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>.
            The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <returns>The <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member name or value of <paramref name="enumType"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <paramref name="enumType"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags(System.Type,System.String,System.Boolean,System.String)">
            <summary>
            Converts the string representation of one or more member names or values of <paramref name="enumType"/> delimited with <paramref name="delimiter"/> to its respective value of type <paramref name="enumType"/>.
            The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <returns>The <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member name or value of <paramref name="enumType"/>.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <paramref name="enumType"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags(System.Type,System.String,System.Boolean,System.String,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <paramref name="enumType"/> delimited with <paramref name="delimiter"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum format. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="format">The parsing enum format.</param>
            <returns>The <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <paramref name="enumType"/>
            -or-
            <paramref name="format"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <paramref name="enumType"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags(System.Type,System.String,System.Boolean,System.String,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <paramref name="enumType"/> delimited with <paramref name="delimiter"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>The <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <paramref name="enumType"/>
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <paramref name="enumType"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags(System.Type,System.String,System.Boolean,System.String,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Converts the string representation of one or more members or values of <paramref name="enumType"/> delimited with <paramref name="delimiter"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>The <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <paramref name="enumType"/>
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <paramref name="enumType"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.ParseFlags(System.Type,System.String,System.Boolean,System.String,EnumsNET.EnumFormat[])">
            <summary>
            Converts the string representation of one or more members or values of <paramref name="enumType"/> delimited with <paramref name="delimiter"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies if the operation is case-insensitive.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>The <paramref name="enumType"/> value that is represented by <paramref name="value"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> or <paramref name="value"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="value"/> doesn't represent a member or value of <paramref name="enumType"/>
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
            <exception cref="T:System.OverflowException"><paramref name="value"/> is outside the range of the underlying type of <paramref name="enumType"/>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags(System.Type,System.String,System.Object@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags(System.Type,System.String,System.Boolean,System.Object@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <paramref name="enumType"/> to its respective value of type <paramref name="enumType"/>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive. The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags(System.Type,System.String,System.Boolean,System.String,System.Object@)">
            <summary>
            Tries to convert the string representation of one or more member names or values of <paramref name="enumType"/> delimited with <paramref name="delimiter"/> to its respective value of type <paramref name="enumType"/>.
            The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive. The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum member names or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> that is represented by <paramref name="value"/>.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags(System.Type,System.String,System.Boolean,System.String,System.Object@,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <paramref name="enumType"/> delimited with <paramref name="delimiter"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum format. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> that is represented by <paramref name="value"/>.</param>
            <param name="format">The parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="format"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags(System.Type,System.String,System.Boolean,System.String,System.Object@,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <paramref name="enumType"/> delimited with <paramref name="delimiter"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> that is represented by <paramref name="value"/>.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="format0"/> or <paramref name="format1"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags(System.Type,System.String,System.Boolean,System.String,System.Object@,EnumsNET.EnumFormat,EnumsNET.EnumFormat,EnumsNET.EnumFormat)">
            <summary>
            Tries to convert the string representation of one or more members or values of <paramref name="enumType"/> delimited with <paramref name="delimiter"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> that is represented by <paramref name="value"/>.</param>
            <param name="format0">The first parsing enum format.</param>
            <param name="format1">The second parsing enum format.</param>
            <param name="format2">The third parsing enum format.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="format0"/>, <paramref name="format1"/>, or <paramref name="format2"/> is an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.TryParseFlags(System.Type,System.String,System.Boolean,System.String,System.Object@,EnumsNET.EnumFormat[])">
            <summary>
            Tries to convert the string representation of one or more members or values of <paramref name="enumType"/> delimited with <paramref name="delimiter"/> to its respective value of type <paramref name="enumType"/>
            using the specified parsing enum formats. The parameter <paramref name="ignoreCase"/> specifies whether the operation is case-insensitive.
            The return value indicates whether the conversion succeeded.
            </summary>
            <param name="enumType">The enum type.</param>
            <param name="value">The enum members or values' string representation.</param>
            <param name="ignoreCase">Indicates if the operation is case-insensitive.</param>
            <param name="delimiter">The delimiter used to separate individual flags.</param>
            <param name="result">If the conversion succeeds this contains a value of type <paramref name="enumType"/> that is represented by <paramref name="value"/>.</param>
            <param name="formats">The parsing enum formats.</param>
            <returns>Indication whether the conversion succeeded.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="enumType"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="enumType"/> is not an enum type
            -or-
            <paramref name="formats"/> contains an invalid value.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.IsValidFlagCombination(EnumsNET.EnumMember)">
            <summary>
            Indicates whether <paramref name="member"/>'s value is a valid flag combination of its enum's defined values.
            </summary>
            <param name="member">The enum member.</param>
            <returns>Indication of whether <paramref name="member"/>'s value is a valid flag combination of its enum's defined values.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="member"/> is <c>null</c>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlags``1(EnumsNET.EnumMember{``0})">
            <summary>
            Retrieves the flags that compose <paramref name="member"/>'s value.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="member">The enum member.</param>
            <returns>The flags that compose <paramref name="member"/>'s value.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="member"/> is <c>null</c>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagMembers``1(EnumsNET.EnumMember{``0})">
            <summary>
            Retrieves the <see cref="T:EnumsNET.EnumMember`1"/>s of the flags that compose <paramref name="member"/>'s value.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
            <param name="member">The enum member.</param>
            <returns>The <see cref="T:EnumsNET.EnumMember`1"/>s of the flags that compose <paramref name="member"/>'s value.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="member"/> is <c>null</c>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAnyFlags(EnumsNET.EnumMember)">
            <summary>
            Indicates if <paramref name="member"/>'s value has any flags.
            </summary>
            <param name="member">The enum member.</param>
            <returns>Indication if <paramref name="member"/>'s has any flags.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="member"/> is <c>null</c>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.HasAllFlags(EnumsNET.EnumMember)">
            <summary>
            Indicates if <paramref name="member"/>'s value has all of the flags that are defined in its enum type.
            </summary>
            <param name="member">The enum member.</param>
            <returns>Indication if <paramref name="member"/> has all of the flags that are defined in its enum type.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="member"/> is <c>null</c>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagCount(EnumsNET.EnumMember)">
            <summary>
            Retrieves the flag count of <paramref name="member"/>.
            </summary>
            <param name="member">The flags enum value.</param>
            <returns>The flag count of <paramref name="member"/>.</returns>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlags(EnumsNET.EnumMember)">
            <summary>
            Retrieves the flags that compose <paramref name="member"/>'s value.
            </summary>
            <param name="member">The enum member.</param>
            <returns>The flags that compose <paramref name="member"/>'s value.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="member"/> is <c>null</c>.</exception>
        </member>
        <member name="M:EnumsNET.FlagEnums.GetFlagMembers(EnumsNET.EnumMember)">
            <summary>
            Retrieves the <see cref="T:EnumsNET.EnumMember"/>s of the flags that compose <paramref name="member"/>'s value.
            </summary>
            <param name="member">The enum member.</param>
            <returns>The <see cref="T:EnumsNET.EnumMember"/>s of the flags that compose <paramref name="member"/>'s value.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="member"/> is <c>null</c>.</exception>
        </member>
        <member name="T:EnumsNET.IEnumValidatorAttribute`1">
            <summary>
            Interface to be implemented on an enum validator attribute class to allow custom validation logic.
            </summary>
            <typeparam name="TEnum">The enum type.</typeparam>
        </member>
        <member name="M:EnumsNET.IEnumValidatorAttribute`1.IsValid(`0)">
            <summary>
            Indicates if <paramref name="value"/> is valid.
            </summary>
            <param name="value">The enum value.</param>
            <returns>Indication if <paramref name="value"/> is valid.</returns>
        </member>
        <member name="T:EnumsNET.PrimaryEnumMemberAttribute">
            <summary>
            Indicates if the enum member should be the primary enum member when there are duplicate values.
            In the case of duplicate values, extension methods will use the enum member marked with this attribute.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
    </members>
</doc>
