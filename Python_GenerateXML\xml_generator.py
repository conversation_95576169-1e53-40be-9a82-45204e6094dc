import os
import sys
import logging
import openpyxl
from lxml import etree
import re
import uuid
import copy
import shutil # Added for backup
from datetime import datetime
from network_generator import NetworkGenerator
from typing import List, Dict, Tuple, Optional, Any, Union
from collections import defaultdict
import importlib.util

# 默认文件路径
# Determine base path for resources (robust for PyInstaller)
if getattr(sys, 'frozen', False):
    # Running in a bundle
    BASE_PATH = sys._MEIPASS
else:
    # Running in a normal Python environment
    BASE_PATH = os.path.dirname(os.path.abspath(__file__))

DEFAULT_INPUT_EXCEL_FILE = os.path.join(BASE_PATH, "manualRow_2.xlsx")
DEFAULT_TEMPLATE_XML_FILE = os.path.join(BASE_PATH, "template2.xml")
DEFAULT_OUTPUT_XML_FILE = "output_new.xml" # Output typically in CWD
DEFAULT_FB_INSTANCE_SIZE = 256

# Configure basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Helper function to safely get cell value as string
def get_cell_value_as_str(cell_value: Any) -> str:
    if cell_value is None:
        return ""
    return str(cell_value).strip()

# Helper function to clean potential leading/trailing quotes from a string
def _clean_string_quotes(value_str: Any) -> str:
    if isinstance(value_str, str):
        return value_str.strip('"')
    if value_str is None:
        return ""
    return str(value_str)

class IdGenerator:
    def __init__(self, start_id=0x13): # Start from 19 (0x13)
        self.current_id = start_id
        self.logger = logging.getLogger('IdGenerator') # Optional: for logging ID generation if needed

    def get_next_id(self) -> str:
        next_id_val = self.current_id
        self.current_id += 1
        hex_id = hex(next_id_val)[2:].upper() # Get hex string, remove "0x", and uppercase
        # self.logger.debug(f"Generated ID: {hex_id} (Decimal: {next_id_val})")
        return hex_id

    def reset(self, start_id=0x13): # Allows resetting for new generation cycles if needed (e.g., multiple files)
        self.current_id = start_id
        self.logger.info(f"IdGenerator reset. Next ID will start from {hex(start_id)[2:].upper()}.")

class XMLGenerator:
    INSTANCE_PARAM_MAPPING = {
        'forward_sensor':  {'section': 'Input', 'member': 'xSnsFwd', 'is_bool': False},
        'backward_sensor': {'section': 'Input', 'member': 'xSnsBwd', 'is_bool': False},
        'forward_output':  {'section': 'Output', 'member': 'xCmdFwd', 'is_bool': False},
        'backward_output': {'section': 'Output', 'member': 'xCmdBwd', 'is_bool': False},
        'auto_forward':    {'section': 'Input', 'member': 'bAutoFwd', 'is_bool': True},
        'auto_backward':   {'section': 'Input', 'member': 'bAutoBwd', 'is_bool': True},
        'forward_permit':  {'section': 'Input', 'member': 'bPermitFwd', 'is_bool': True},
        'backward_permit': {'section': 'Input', 'member': 'bPermitBwd', 'is_bool': True},
    }
    
    PARTS_MODIFICATIONS_SINGLE_INPUT = {
        "21": { # UID
            "components": [{"original_name": "I132.0 左UPIN缓存挡料1气缸 伸出到位 NA1-C101a", "new_name_excel_col": "I点-工作位1"}],
            "addresses": [{"new_bit_offset_excel_col": "I点-工作位1偏移量"}]
        },
        "22": {
            "components": [{"original_name": "I132.1 左UPIN缓存挡料1气缸 缩回到位 NA1-C101b", "new_name_excel_col": "I点-原位1"}],
            "addresses": [{"new_bit_offset_excel_col": "I点-原位1偏移量"}]
        },
        "23": {
            "components": [
                {"original_name": "DB_ControlNodesC5", "new_name_excel_col": "手动行DB块名称"},
                {"original_name": "em12", "new_name_excel_col": "OpMode名称"},
                {"original_name": "NA1C101左缓存挡料气缸1", "new_name_excel_col": "name"}
            ]
        },
        "24": { # 与UID 23 规则相同
            "components": [
                {"original_name": "DB_ControlNodesC5", "new_name_excel_col": "手动行DB块名称"},
                {"original_name": "em12", "new_name_excel_col": "OpMode名称"},
                {"original_name": "NA1C101左缓存挡料气缸1", "new_name_excel_col": "name"}
            ]
        },
        "29": {
            "constants": [{"original_value": "12", "new_value_excel_col": "OpMode编号"}]
        },
        "35": {
            "components": [{"original_name": "Q216.4 NA1C101挡料气缸1伸出", "new_name_excel_col": "Q点-工作位"}],
            "addresses": [{"new_bit_offset_excel_col": "Q点-工作位偏移量"}]
        },
        "36": {
            "components": [{"original_name": "Q216.5 NA1C101挡料气缸1缩回", "new_name_excel_col": "Q点-原位"}],
            "addresses": [{"new_bit_offset_excel_col": "Q点-原位偏移量"}]
        },
        "37": {
            "components": [
                {"original_name": "DB_ControlNodesC5", "new_name_excel_col": "手动行DB块名称"},
                {"original_name": "em12", "new_name_excel_col": "OpMode名称"},
                {"original_name": "NA1C101左缓存挡料气缸1", "new_name_excel_col": "name"}
            ]
        },
        "38": { # 与UID 37 规则相同
            "components": [
                {"original_name": "DB_ControlNodesC5", "new_name_excel_col": "手动行DB块名称"},
                {"original_name": "em12", "new_name_excel_col": "OpMode名称"},
                {"original_name": "NA1C101左缓存挡料气缸1", "new_name_excel_col": "name"}
            ]
        },
        "39": {
            "components": [ # 只需要改这一个Component
                {"original_name": "NA1C101左缓存挡料气缸1", "new_name_excel_col": "name"}
            ]
        }
    }

    PARTS_MODIFICATIONS_DOUBLE_INPUT = {
        "21": {
            "components": [{"original_name": "I133.0 左IPIN缓存料盘定位气缸 伸出到位 NA1-C501a", "new_name_excel_col": "I点-工作位1"}],
            "addresses": [{"new_bit_offset_excel_col": "I点-工作位1偏移量"}]
        },
        "22": {
            "components": [{"original_name": "I133.2 左IPIN缓存料盘定位气缸 伸出到位 NA1-C501c", "new_name_excel_col": "I点-工作位2"}],
            "addresses": [{"new_bit_offset_excel_col": "I点-工作位2偏移量"}]
        },
        "23": {
            "components": [{"original_name": "I133.1 左IPIN缓存料盘定位气缸 缩回到位 NA1-C501b", "new_name_excel_col": "I点-原位1"}],
            "addresses": [{"new_bit_offset_excel_col": "I点-原位1偏移量"}]
        },
        "24": {
            "components": [{"original_name": "I133.3 左IPIN缓存料盘定位气缸 缩回到位 NA1-C501d", "new_name_excel_col": "I点-原位2"}],
            "addresses": [{"new_bit_offset_excel_col": "I点-原位2偏移量"}]
        },
        "25": {
            "components": [
                {"original_name": "DB_ControlNodesC5", "new_name_excel_col": "手动行DB块名称"},
                {"original_name": "em12", "new_name_excel_col": "OpMode名称"},
                {"original_name": "NA1C501左缓存料盘定位气缸", "new_name_excel_col": "name"}
            ]
        },
        "26": { # 与UID 25 规则相同
            "components": [
                {"original_name": "DB_ControlNodesC5", "new_name_excel_col": "手动行DB块名称"},
                {"original_name": "em12", "new_name_excel_col": "OpMode名称"},
                {"original_name": "NA1C501左缓存料盘定位气缸", "new_name_excel_col": "name"}
            ]
        },
        "31": {
            "constants": [{"original_value": "12", "new_value_excel_col": "OpMode编号"}]
        },
        "37": {
            "components": [{"original_name": "Q217.4 NA1C501料盘定位气缸伸出", "new_name_excel_col": "Q点-工作位"}],
            "addresses": [{"new_bit_offset_excel_col": "Q点-工作位偏移量"}]
        },
        "38": {
            "components": [{"original_name": "Q217.5 NA1C501料盘定位气缸缩回", "new_name_excel_col": "Q点-原位"}],
            "addresses": [{"new_bit_offset_excel_col": "Q点-原位偏移量"}]
        },
        "39": {
            "components": [
                {"original_name": "DB_ControlNodesC5", "new_name_excel_col": "手动行DB块名称"},
                {"original_name": "em12", "new_name_excel_col": "OpMode名称"},
                {"original_name": "NA1C501左缓存料盘定位气缸", "new_name_excel_col": "name"}
            ]
        },
        "40": { # 与UID 39 规则相同
            "components": [
                {"original_name": "DB_ControlNodesC5", "new_name_excel_col": "手动行DB块名称"},
                {"original_name": "em12", "new_name_excel_col": "OpMode名称"},
                {"original_name": "NA1C501左缓存料盘定位气缸", "new_name_excel_col": "name"}
            ]
        },
        "45": {
            "components": [
                {"original_name": "NA1C501左缓存料盘定位气缸", "new_name_excel_col": "name"}
            ]
        }
    }
    
    def __init__(self, template_filepath=DEFAULT_TEMPLATE_XML_FILE, 
                 output_filepath=DEFAULT_OUTPUT_XML_FILE,
                 excel_filepath="manualRow_2.xlsx",
                 logger=None):
        self.logger = logger or logging.getLogger('xml_generator')
        if not self.logger.handlers:
            self.setup_logging()
        self.template_filepath = template_filepath
        self.output_filepath = output_filepath
        self.excel_filepath = excel_filepath
        self.id_generator = IdGenerator()
        self.tree = None
        self.root = None
        self.ns = None  # XML namespace
        self.network_generator = NetworkGenerator(self.logger, self.template_filepath, self.id_generator)
        
    def setup_logging(self):
        self.logger.setLevel(logging.DEBUG)
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO) # Console shows INFO and above
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def read_excel_data(self, excel_file_path: str) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        instances = []
        uid_param_map = {}  # Stores UID to parameter mapping
        op_mode_comment_for_footer = "" # Initialize to empty string

        try:
            workbook = openpyxl.load_workbook(excel_file_path, data_only=True)
            sheet = workbook.active
            if sheet is None:
                self.logger.error("No active sheet found in the Excel file.")
                return [], {}
            
            header = [get_cell_value_as_str(sheet.cell(row=1, column=i).value) for i in range(1, sheet.max_column + 1)]
            self.logger.info(f"Excel Header: {header}")

            # 检查Excel表头中是否存在预期的列
            expected_columns = [
                '实例名称', '实例注释',
                'I点-工作位1', 'I点-原位1', 
                'Q点-工作位', 'Q点-原位'
            ]
            
            # 验证所需列是否存在
            missing_columns = []
            for col in expected_columns:
                if col not in header:
                    missing_columns.append(col)
            
            if missing_columns:
                self.logger.error(f"Missing one or more required columns in Excel header: {', '.join(missing_columns)}")
                return [], {}
            
            # 获取列索引
            name_col = header.index("实例名称") + 1
            comment_col = header.index("实例注释") + 1
            
            # I/O 地址列
            fwd_sensor_addr_col = header.index("I点-工作位1") + 1
            bwd_sensor_addr_col = header.index("I点-原位1") + 1
            fwd_output_addr_col = header.index("Q点-工作位") + 1
            bwd_output_addr_col = header.index("Q点-原位") + 1
            
            # 可选列偏移量
            fwd_sensor_offset_col = header.index("I点-工作位1偏移量") + 1 if "I点-工作位1偏移量" in header else -1
            bwd_sensor_offset_col = header.index("I点-原位1偏移量") + 1 if "I点-原位1偏移量" in header else -1
            fwd_output_offset_col = header.index("Q点-工作位偏移量") + 1 if "Q点-工作位偏移量" in header else -1
            bwd_output_offset_col = header.index("Q点-原位偏移量") + 1 if "Q点-原位偏移量" in header else -1
            
            # 双输入点的可选列
            fwd_sensor2_col = header.index("I点-工作位2") + 1 if "I点-工作位2" in header else -1
            bwd_sensor2_col = header.index("I点-原位2") + 1 if "I点-原位2" in header else -1
            fwd_sensor2_offset_col = header.index("I点-工作位2偏移量") + 1 if "I点-工作位2偏移量" in header else -1
            bwd_sensor2_offset_col = header.index("I点-原位2偏移量") + 1 if "I点-原位2偏移量" in header else -1
            
            # 其他可能的列
            db_name_col = header.index("手动行DB块名称") + 1 if "手动行DB块名称" in header else -1
            op_mode_name_col = header.index("OpMode名称") + 1 if "OpMode名称" in header else -1
            op_mode_number_col = header.index("OpMode编号") + 1 if "OpMode编号" in header else -1
            module_name_col_c_header = "OpMode注释" # CORRECTED based on user screenshot
            module_name_col_c = header.index(module_name_col_c_header) + 1 if module_name_col_c_header in header else -1
            
            # FB模板编号
            fb_template_col = header.index("手动FB 块编号") + 1 if "手动FB 块编号" in header else -1

            # Read the OpMode注释 from the first data row (e.g., row 2) for the footer
            if module_name_col_c != -1 and sheet.max_row >= 2:
                op_mode_comment_for_footer = get_cell_value_as_str(sheet.cell(row=2, column=module_name_col_c).value)
                self.logger.info(f"Read '{module_name_col_c_header}' for footer from Excel row 2: '{op_mode_comment_for_footer}'")
            elif module_name_col_c == -1:
                self.logger.warning(f"Excel column '{module_name_col_c_header}' (expected for Column C) not found. Footer text replacement will use an empty string.")
            else: # Column exists but sheet has less than 2 rows
                self.logger.warning(f"Excel column '{module_name_col_c_header}' found, but sheet has less than 2 rows. Cannot read footer comment.")

            first_processed_row_db_name = ""
            first_processed_row_op_mode_name = ""
            is_first_processed_row = True

            for current_row_idx in range(2, sheet.max_row + 1):
                instance_name = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=name_col).value)
                instance_comment = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=comment_col).value)

                # 如果实例名称为空则跳过
                if not instance_name.strip():
                    continue
                
                # 清洁字符串值
                instance_name_cleaned = _clean_string_quotes(instance_name)
                instance_comment_cleaned = _clean_string_quotes(instance_comment)
                
                # 读取基本地址数据
                fwd_sensor_addr = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=fwd_sensor_addr_col).value)
                bwd_sensor_addr = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=bwd_sensor_addr_col).value)
                fwd_output_addr = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=fwd_output_addr_col).value)
                bwd_output_addr = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=bwd_output_addr_col).value)
                
                # 创建基本实例数据
                instance_data = {
                    'name': instance_name_cleaned,
                    'comment': instance_comment_cleaned,
                    'excel_row': current_row_idx,
                    
                    # 存储Excel表中的原始列数据
                    'I点-工作位1': fwd_sensor_addr,
                    'I点-原位1': bwd_sensor_addr,
                    'Q点-工作位': fwd_output_addr,
                    'Q点-原位': bwd_output_addr,
                    
                    # 兼容旧代码的属性
                    'forward_sensor_addr': fwd_sensor_addr,
                    'backward_sensor_addr': bwd_sensor_addr,
                    'forward_output_addr': fwd_output_addr,
                    'backward_output_addr': bwd_output_addr,
                    'forward_sensor_desc': f"{fwd_sensor_addr} {instance_name_cleaned}工作位",
                    'backward_sensor_desc': f"{bwd_sensor_addr} {instance_name_cleaned}原位",
                    'forward_output_desc': f"{fwd_output_addr} {instance_name_cleaned}工作位输出",
                    'backward_output_desc': f"{bwd_output_addr} {instance_name_cleaned}原位输出",
                }
                
                # 添加偏移量数据（如果存在）
                if fwd_sensor_offset_col != -1:
                    instance_data['I点-工作位1偏移量'] = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=fwd_sensor_offset_col).value)
                if bwd_sensor_offset_col != -1:
                    instance_data['I点-原位1偏移量'] = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=bwd_sensor_offset_col).value)
                if fwd_output_offset_col != -1:
                    instance_data['Q点-工作位偏移量'] = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=fwd_output_offset_col).value)
                if bwd_output_offset_col != -1:
                    instance_data['Q点-原位偏移量'] = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=bwd_output_offset_col).value)
                
                # 添加双输入点数据（如果存在）
                is_double_input = False
                if fwd_sensor2_col != -1:
                    fwd_sensor2 = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=fwd_sensor2_col).value)
                    if fwd_sensor2.strip():
                        instance_data['I点-工作位2'] = fwd_sensor2
                        instance_data['forward_sensor2_addr'] = fwd_sensor2
                        instance_data['forward_sensor2_desc'] = f"{fwd_sensor2} {instance_name_cleaned}工作位2"
                        is_double_input = True
                
                if bwd_sensor2_col != -1:
                    bwd_sensor2 = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=bwd_sensor2_col).value)
                    if bwd_sensor2.strip():
                        instance_data['I点-原位2'] = bwd_sensor2
                        instance_data['backward_sensor2_addr'] = bwd_sensor2
                        instance_data['backward_sensor2_desc'] = f"{bwd_sensor2} {instance_name_cleaned}原位2"
                        is_double_input = True
                
                # 添加双输入点偏移量（如果存在）
                if fwd_sensor2_offset_col != -1:
                    instance_data['I点-工作位2偏移量'] = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=fwd_sensor2_offset_col).value)
                if bwd_sensor2_offset_col != -1:
                    instance_data['I点-原位2偏移量'] = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=bwd_sensor2_offset_col).value)
                
                # 设置实例类型
                instance_data['is_double_input_type'] = is_double_input
                instance_data['type'] = "DoubleInput" if is_double_input else "SingleInput"
                
                # 添加其他可能的数据
                if db_name_col != -1:
                    current_db_val = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=db_name_col).value)
                    if is_first_processed_row:
                        first_processed_row_db_name = current_db_val
                    instance_data['手动行DB块名称'] = current_db_val if current_db_val else first_processed_row_db_name
                    instance_data['manual_db_name'] = instance_data['手动行DB块名称']
                
                if op_mode_name_col != -1:
                    current_op_mode_val = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=op_mode_name_col).value)
                    if is_first_processed_row:
                        first_processed_row_op_mode_name = current_op_mode_val
                    instance_data['OpMode名称'] = current_op_mode_val if current_op_mode_val else first_processed_row_op_mode_name
                    instance_data['op_mode_name'] = instance_data['OpMode名称']
                
                if op_mode_number_col != -1:
                    instance_data['OpMode编号'] = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=op_mode_number_col).value)
                    instance_data['op_mode_number'] = instance_data['OpMode编号']
                
                # Removed per-instance reading of module_name_col_c as it's a merged cell, read once above for footer.
                # if module_name_col_c != -1:
                #     instance_data['module_name_col_c'] = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=module_name_col_c).value)
                # else:
                #     instance_data['module_name_col_c'] = "" # Default to empty string if column C is not found
                #     self.logger.warning(f"Excel column '{module_name_col_c_header}' (expected for Column C) not found. Footer text replacement will use an empty string.")

                # 获取FB模板编号（如果存在）
                if fb_template_col != -1:
                    fb_template_name = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=fb_template_col).value)
                    instance_data['fb_template'] = fb_template_name
                
                instances.append(instance_data)
                
                # 使用实例名称作为 uid_param_map 的键
                uid_param_map[instance_name_cleaned] = instance_data
                
                is_first_processed_row = False # Mark that we've processed at least one valid row

        except FileNotFoundError:
            self.logger.error(f"Excel file not found at path: {excel_file_path}")
        except Exception as e:
            self.logger.error(f"Error reading Excel data: {e}", exc_info=True)
            
        return instances, uid_param_map, op_mode_comment_for_footer

    def update_xml_basic_info(self):
        """更新XML文件的基本信息，如FB的名称、编号等"""
        # 先读取Excel数据
        data = self.read_excel_data(self.excel_filepath)
        op_mode_data = data['op_mode']
        instances = data['instances']
        
        # 更新FB名称、编号
        self.update_fb_attributes(self.root, op_mode_data)

    def update_fb_attributes(self, root, op_mode_data):
        """FB属性更新 (Name, Number, Comment)"""
        # 设置命名空间
        ns_prefix = "ns"
        ns_uri = self.ns
        ns_dict = {ns_prefix: ns_uri} if ns_uri else {}
        xpath_prefix = f"{ns_prefix}:" if ns_uri else ""
        
        # 假设这是FB元素的XPath
        fb_elem = root.find(f".//{xpath_prefix}SW.Blocks.FB", namespaces=ns_dict)
        if fb_elem is None:
            self.logger.error("未在XML中找到FB元素")
            return False
        
        # 更新FB名称
        if op_mode_data.get('fb_name'):
            fb_name_elem = fb_elem.find(f".//{xpath_prefix}Name", namespaces=ns_dict)
            if fb_name_elem is not None:
                fb_name_elem.text = op_mode_data['fb_name']
                self.logger.info(f"已更新FB名称为: {op_mode_data['fb_name']}")
            else:
                self.logger.warning("未找到FB名称元素，无法更新")
        
        # 更新FB编号
        if op_mode_data.get('fb_number'):
            fb_number_attr = fb_elem.find(f".//{xpath_prefix}ObjectList/{xpath_prefix}IntegerAttribute[@Name='Number']", namespaces=ns_dict)
            if fb_number_attr is not None:
                fb_number_attr.text = str(op_mode_data['fb_number'])
                self.logger.info(f"已更新FB编号为: {op_mode_data['fb_number']}")
            else:
                self.logger.warning("未找到FB编号属性，无法更新")
        
        # 更新FB注释
        if op_mode_data.get('op_mode_comment'):
            # 假设注释在MultiLanguageText元素内
            comment_text = fb_elem.find(f".//{xpath_prefix}MultiLanguageText[@Lang='zh-CN']", namespaces=ns_dict)
            if comment_text is not None:
                comment_text.text = op_mode_data['op_mode_comment']
                self.logger.info(f"已更新FB注释为: {op_mode_data['op_mode_comment']}")
            else:
                self.logger.warning("未找到FB注释元素，无法更新")
        
        return True

    def generate_static_members(self):
        """
        生成静态成员变量：
        1. 首先生成实例声明内容（从模板66-436行）
        2. 然后生成固定变量的声明（从模板3028-3076行）
        """
        self.logger.info("生成静态成员变量")
        
        # 设置命名空间
        ns_prefix = "ns"
        ns_uri = self.ns
        ns_dict = {ns_prefix: ns_uri} if ns_uri else {}
        xpath_prefix = f"{ns_prefix}:" if ns_uri else ""
        
        # 读取Excel数据
        data = self.read_excel_data(self.excel_filepath)
        instances = data[0]
        
        # 找到Interface->Sections->Static元素
        interface_elem = self.root.find(f".//{xpath_prefix}Interface", namespaces=ns_dict)
        if interface_elem is None:
            self.logger.error("未在XML中找到Interface元素")
            return False
        
        sections_elem = interface_elem.find(f".//{xpath_prefix}Sections", namespaces=ns_dict)
        if sections_elem is None:
            self.logger.error("未在XML中找到Sections元素")
            return False
        
        static_elem = sections_elem.find(f".//{xpath_prefix}Section[@Name='Static']", namespaces=ns_dict)
        if static_elem is None:
            self.logger.error("未在XML中找到Static段")
            return False
        
        # 清空Static元素的内容 (如果有的话)
        for child in list(static_elem):
            static_elem.remove(child)
        
        # 步骤1: 生成实例成员
        self._generate_instance_declarations(static_elem, instances)
        
        # 步骤2: 生成固定成员
        self._generate_fixed_members(static_elem)
        
        self.logger.info(f"成功生成静态成员变量 ({len(instances)}个实例 + 固定成员)")
        return True

    def _generate_instance_declarations(self, static_elem, instances):
        """
        生成实例声明内容
        - 每个实例基于template2.xml的66-436行
        - 根据实例数据修改参数
        """
        self.logger.info("生成实例声明内容")
        
        # 读取模板中的实例声明内容 (66-436行)
        instance_template_lines = []
        try:
            with open(self.template_filepath, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                instance_template_lines = all_lines[65:436]  # 66-436行 (按0-索引)
        except Exception as e:
            self.logger.error(f"读取实例模板内容失败: {e}")
            return
        
        instance_template_xml = ''.join(instance_template_lines)
        
        # 为每个实例生成声明
        for i, instance in enumerate(instances):
            # 解析模板XML片段
            try:
                instance_template_root = etree.fromstring(f"<root>{instance_template_xml}</root>")
                # 获取第一个Member元素 (即模板中的实例元素)
                member_elem = instance_template_root.find(".//Member")
                if member_elem is None:
                    self.logger.error(f"在实例模板中未找到Member元素")
                    continue
                    
                # 复制Member元素
                copied_member = copy.deepcopy(member_elem)
                
                # 修改实例名称 - 使用实例数据中的name值 (G列内容)
                copied_member.set("Name", instance['name'])
                
                # 修改偏移量 - 只修改实例声明的整体偏移量，不修改实例内部的偏移量
                offset_elem = copied_member.find(".//IntegerAttribute[@Name='Offset']")
                if offset_elem is not None:
                    # 确保只修改实例声明的第一个偏移量，不修改实例内部的偏移量
                    parent = offset_elem.getparent()
                    if parent is not None and parent.tag.endswith("AttributeList") and parent.getparent() is copied_member:
                        offset_elem.text = str(i * 256)  # 每个实例偏移量递增256，符合prompt.md要求
                        self.logger.debug(f"已将实例 '{instance['name']}' 的整体偏移量设置为 {i * 256}")
                
                # 修改注释 - 使用实例数据中的comment值 (H列内容)，而不是name值 (G列内容)
                comment_elem = copied_member.find(".//Comment")
                if comment_elem is not None:
                    # 查找中文注释元素
                    multi_lang_text = comment_elem.find(".//MultiLanguageText[@Lang='zh-CN']")
                    if multi_lang_text is not None:
                        # 设置为实例注释 (H列内容)
                        multi_lang_text.text = instance['comment']
                        self.logger.debug(f"已将实例 '{instance['name']}' 的注释设置为 '{instance['comment']}'")
                
                # 将修改后的Member元素添加到Static元素
                static_elem.append(copied_member)
                
                self.logger.debug(f"已生成实例 '{instance['name']}' 的声明")
            except Exception as e:
                self.logger.error(f"生成实例 '{instance.get('name', f'实例{i}')}' 的声明失败: {e}")

    def _generate_fixed_members(self, static_elem):
        """
        生成固定变量的声明
        - 基于template2.xml的3028-3076行
        """
        self.logger.info("生成固定变量的声明")
        
        # 读取模板中的固定成员声明内容 (3028-3076行)
        fixed_members_lines = []
        try:
            with open(self.template_filepath, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                fixed_members_lines = all_lines[3027:3076]  # 3028-3076行 (按0-索引)
        except Exception as e:
            self.logger.error(f"读取固定成员模板内容失败: {e}")
            return
        
        fixed_members_xml = ''.join(fixed_members_lines)
        
        # 解析固定成员XML片段
        try:
            fixed_members_root = etree.fromstring(f"<root>{fixed_members_xml}</root>")
            # 获取所有Member元素
            member_elems = fixed_members_root.findall(".//Member")
            
            for member_elem in member_elems:
                # 复制Member元素
                copied_member = copy.deepcopy(member_elem)
                # 将复制的元素添加到Static元素
                static_elem.append(copied_member)
            
            self.logger.debug(f"已添加 {len(member_elems)} 个固定成员变量")
        except Exception as e:
            self.logger.error(f"生成固定成员变量失败: {e}")

    def generate_instance_members(self):
        """
        这个函数已不再需要，功能已合并到generate_static_members中
        保留此函数以保持兼容性
        """
        self.logger.info("实例成员变量生成已合并到generate_static_members函数中")
        return True

    def generate_networks(self):
        """
        生成网络代码:
        1. 复制模板xml中3077-3459行的HMI display、mode固定内容
        2. 根据每个实例的类型(单输入/双输入)复制相应的网络代码并修改参数
        3. 对最后一个实例程序进行特殊处理
        """
        self.logger.info("生成网络代码")
        
        # 设置命名空间
        ns_prefix = "ns"
        ns_uri = self.ns
        ns_dict = {ns_prefix: ns_uri} if ns_uri else {}
        xpath_prefix = f"{ns_prefix}:" if ns_uri else ""
        
        # 读取Excel数据
        data = self.read_excel_data(self.excel_filepath)
        op_mode_data = data['op_mode']
        instances = data['instances']
        
        # 使用NetworkGenerator生成网络代码
        # 这已经包含了按照prompt.md要求的逻辑
        network_lines = self.network_generator.generate_all_networks(instances, op_mode_data)
        
        # 找到NetworkSource元素
        networks_elem = self.root.find(f".//{xpath_prefix}SW.Blocks.FB/{xpath_prefix}ObjectList/{xpath_prefix}SW.Blocks.CompileUnit/{xpath_prefix}AttributeList/{xpath_prefix}NetworkSource", namespaces=ns_dict)
        if networks_elem is None:
            self.logger.error("未在XML中找到NetworkSource元素")
            return False
        
        # 更新网络代码
        networks_text = ''.join(network_lines)
        networks_elem.text = networks_text
        
        self.logger.info(f"成功生成网络代码，共 {len(network_lines)} 行")
        return True

    def find_access_for_param(self, param_name, uid_map):
        """
        查找参数在网络中的访问点
        param_name: 参数名称，如 'forward_sensor', 'backward_sensor' 等
        uid_map: 映射参数到UID的字典
        
        Returns: 对应的 Access XML 节点，如果未找到则返回 None
        """
        try:
            mapping_info = self.INSTANCE_PARAM_MAPPING.get(param_name)
            if not mapping_info:
                self.logger.warning(f"未找到参数 '{param_name}' 的映射信息")
                return None
            
            self.logger.debug(f"查找参数 '{param_name}' 的访问点，映射信息：{mapping_info}")
            section_name = mapping_info.get('section')
            member_name = mapping_info.get('member')
            
            if not section_name or not member_name:
                self.logger.warning(f"参数 '{param_name}' 的映射信息不完整：section={section_name}, member={member_name}")
                return None
            
            # 更准确地查找包含Section和Member指定属性的元素
            section_uid = uid_map.get(f"SECTION:{section_name}")
            member_uid = uid_map.get(f"MEMBER:{member_name}")
            
            self.logger.debug(f"参数 '{param_name}' 对应的Section UID: {section_uid}, Member UID: {member_uid}")
            
            if not section_uid or not member_uid:
                self.logger.warning(f"参数 '{param_name}' 无法找到对应的Section UID或Member UID: section_uid={section_uid}, member_uid={member_uid}")
                
                # 详细记录所有可用的映射，帮助调试
                self.logger.debug("所有可用的UID映射:")
                for key, value in uid_map.items():
                    self.logger.debug(f"  {key} -> {value}")
                
                return None
            
            # 构建XPath查询
            xpath_query = f"//Interface[@UId='{section_uid}']//Section[@Name='{section_name}']//Member[@Name='{member_name}']"
            
            self.logger.debug(f"使用XPath查询: {xpath_query}")
            member_elem = self.root.xpath(xpath_query, namespaces=self.ns)
            
            if not member_elem:
                self.logger.warning(f"未找到参数 '{param_name}' 的Member元素，使用XPath: {xpath_query}")
                
                # 尝试更简单的XPath查询
                simpler_xpath = f"//Section[@Name='{section_name}']//Member[@Name='{member_name}']"
                self.logger.debug(f"尝试更简单的XPath查询: {simpler_xpath}")
                member_elem = self.root.xpath(simpler_xpath, namespaces=self.ns)
                
                if not member_elem:
                    self.logger.warning(f"使用简化XPath也未找到参数 '{param_name}' 的Member元素")
                    
                    # 检查Section元素是否存在
                    section_xpath = f"//Section[@Name='{section_name}']"
                    sections = self.root.xpath(section_xpath, namespaces=self.ns)
                    self.logger.debug(f"找到 {len(sections)} 个Section元素，查询: {section_xpath}")
                    
                    # 列出所有Member
                    if sections:
                        for i, section in enumerate(sections):
                            members = section.xpath(".//Member", namespaces=self.ns)
                            self.logger.debug(f"Section[{i}]中包含 {len(members)} 个Member元素")
                            for j, member in enumerate(members[:5]):  # 只显示前5个，避免日志过大
                                name = member.get("Name", "无名称")
                                self.logger.debug(f"  Member[{j}]: Name='{name}'")
                    
                    return None
            
            # 取第一个匹配的成员
            member = member_elem[0]
            self.logger.debug(f"找到参数 '{param_name}' 的Member元素: {etree.tostring(member, encoding='unicode')}")
            
            # 构建Access节点
            access_elem = etree.Element('Access')
            access_elem.set('Scope', 'LocalVariable')
            access_elem.set('UId', member.get('UId', '0'))
            
            self.logger.debug(f"创建Access节点: {etree.tostring(access_elem, encoding='unicode')}")
            return access_elem
        
        except Exception as e:
            self.logger.error(f"查找参数 '{param_name}' 访问点时发生错误: {str(e)}", exc_info=True)
            return None

    def remap_uids(self, flgnet_element):
        """
        重新映射FlgNet元素中的UID，确保不同实例的UID唯一。
        
        Args:
            flgnet_element: FlgNet XML元素
            
        Returns:
            无，直接修改输入的元素。
        """
        self.logger.info("[ID_REGEN] 开始为该实例重新生成唯一ID。")
        
        # 收集当前元素中的所有UID
        uid_map = {}  # 原始UID -> 新UID的映射
        uid_elements = []  # 所有带UID的元素
        
        # 使用XPath查找所有带UID属性的元素
        uid_pattern = ".//*[@UId]"
        try:
            for elem in flgnet_element.xpath(uid_pattern):
                uid = elem.get('UId')
                if uid:
                    uid_elements.append((elem, uid))
        except Exception as e:
            self.logger.error(f"[ID_REGEN] 查找UID元素时出错: {e}", exc_info=True)
        
        self.logger.debug(f"[ID_REGEN] 找到 {len(uid_elements)} 个带UID的元素")
        
        # 首先收集所有唯一UID，并创建映射关系
        unique_uids = set()
        for elem, uid in uid_elements:
            if uid not in uid_map:
                new_uid = str(uuid.uuid4())[:8]  # 生成新的唯一UID
                uid_map[uid] = new_uid
                unique_uids.add(uid)
                tag_name = etree.QName(elem).localname
                self.logger.debug(f"[ID_REGEN] 找到标签 <{tag_name}> 带UID='{uid}', 将替换为 '{new_uid}'")
        
        self.logger.info(f"[ID_REGEN] 找到 {len(unique_uids)} 个唯一UID需要替换。")
        
        # 然后应用映射，更新所有元素的UID
        replaced_count = 0
        for elem, uid in uid_elements:
            if uid in uid_map:
                tag_name = etree.QName(elem).localname
                new_uid = uid_map[uid]
                elem.set('UId', new_uid)
                replaced_count += 1
                # 记录行号和元素名称，方便调试
                line_number = elem.sourceline if hasattr(elem, 'sourceline') else '未知'
                self.logger.info(f"[ID_REGEN] 行{line_number}: 将 <{tag_name}> 的UID从 '{uid}' 改为 '{new_uid}'")
        
        # 更新元素间的引用关系
        reference_count = 0
        # 处理Source、Target、NameSource属性等，它们都可能引用UID
        reference_attrs = ['Source', 'Target', 'NameSource']
        for attr_name in reference_attrs:
            attr_pattern = f".//*[@{attr_name}]"
            for elem in flgnet_element.xpath(attr_pattern):
                ref_value = elem.get(attr_name)
                if ref_value in uid_map:
                    new_ref = uid_map[ref_value]
                    elem.set(attr_name, new_ref)
                    reference_count += 1
                    tag_name = etree.QName(elem).localname
                    self.logger.debug(f"[ID_REGEN] 更新元素 <{tag_name}> 的 {attr_name} 引用: '{ref_value}' -> '{new_ref}'")
        
        self.logger.info(f"[ID_REGEN] UID重生成完成，共执行 {replaced_count} 个替换和 {reference_count} 个引用更新。")
        return flgnet_element

    def fix_multilingual_text_ids(self, preserve_hmi_mode_ids=False):
        """
        修复XML中的MultilingualText和相关元素的ID冲突
        此函数遍历XML中的所有带ID属性的元素，收集它们，并重新分配唯一ID
        
        Args:
            preserve_hmi_mode_ids: 是否保留HMI/mode部分的原始ID（3077-3459行）
        """
        self.logger.info("开始修复MultilingualText和CompileUnit元素ID冲突")
        
        # 用于识别和重新映射ID的字典 
        # {元素标签: {原ID: 新ID}}
        id_maps = {
            'MultilingualText': {},
            'MultilingualTextItem': {},
            'SW.Blocks.CompileUnit': {},
            # 添加其他可能有ID冲突的元素类型
        }
        
        # 第一步：收集所有ID并检测冲突
        id_conflicts = {}  # {元素标签: {ID: [出现次数]}}
        all_elements = []  # 存储所有带ID的元素以便后续处理
        
        # 设置命名空间
        ns_prefix = "ns"
        ns_uri = self.ns  
        ns_dict = {ns_prefix: ns_uri} if ns_uri else {}
        
        # 收集所有带ID属性的元素
        xpath_query = "//*[@ID]"
        elements_with_id = self.root.xpath(xpath_query, namespaces=ns_dict)
        
        self.logger.info(f"找到 {len(elements_with_id)} 个带ID属性的元素")
        
        # 分析元素并识别冲突
        for elem in elements_with_id:
            tag = etree.QName(elem).localname  # 获取不带命名空间的标签名
            id_val = elem.get('ID')
            
            # 检查是否是HMI/mode部分的元素（ID范围为1-12,对应模板中3077-3459行）
            is_hmi_mode_element = False
            if preserve_hmi_mode_ids:
                if (tag == 'MultilingualText' and id_val in ['1', '5', '8', 'C', 'F']) or \
                   (tag == 'MultilingualTextItem' and id_val in ['2', '3', '6', '7', '9', 'A', 'D', 'E', '10', '11']) or \
                   (tag == 'SW.Blocks.CompileUnit' and id_val in ['4', 'B', '12']):
                    is_hmi_mode_element = True
            
            # 如果是HMI/mode部分且需要保留，则跳过
            if is_hmi_mode_element:
                continue
                
            if tag not in id_conflicts:
                id_conflicts[tag] = {}
            
            if id_val not in id_conflicts[tag]:
                id_conflicts[tag][id_val] = 0
            
            id_conflicts[tag][id_val] += 1
            all_elements.append((elem, tag, id_val))
        
        # 第二步：找出有冲突的ID
        for tag, ids in id_conflicts.items():
            conflict_ids = {id_val: count for id_val, count in ids.items() if count > 1}
            
            if conflict_ids:
                self.logger.warning(f"元素类型 '{tag}' 中发现 {len(conflict_ids)} 个ID冲突")
                for id_val, count in conflict_ids.items():
                    self.logger.warning(f"  ID '{id_val}' 出现了 {count} 次")
            else:
                self.logger.info(f"元素类型 '{tag}' 没有ID冲突")
        
        # 第三步：对每种元素类型使用唯一的ID起始值
        # 修改：使用单一计数器，所有元素类型共享同一个ID序列
        single_id_counter = 0x13  # 从十六进制13开始
        
        # 第四步：重新映射冲突的ID
        for elem, tag, old_id in all_elements:
            # 检查是否是HMI/mode部分的元素 - 再次检查，确保不修改这些ID
            is_hmi_mode_element = False
            if preserve_hmi_mode_ids:
                if (tag == 'MultilingualText' and old_id in ['1', '5', '8', 'C', 'F']) or \
                   (tag == 'MultilingualTextItem' and old_id in ['2', '3', '6', '7', '9', 'A', 'D', 'E', '10', '11']) or \
                   (tag == 'SW.Blocks.CompileUnit' and old_id in ['4', 'B', '12']):
                    is_hmi_mode_element = True
            
            # 如果是HMI/mode部分且需要保留，则跳过
            if is_hmi_mode_element:
                continue
                
            # 修改：改为使用统一的ID计数器，确保ID序列的连续性
            if tag in ['MultilingualText', 'MultilingualTextItem', 'SW.Blocks.CompileUnit']:
                if tag not in id_maps:
                    id_maps[tag] = {}
                
                if old_id not in id_maps[tag]:
                    # 为这个ID分配一个新的唯一ID
                    new_id = hex(single_id_counter)[2:].upper()
                    id_maps[tag][old_id] = new_id
                    single_id_counter += 1  # 增加共享计数器
                    self.logger.debug(f"元素 '{tag}' 的ID '{old_id}' 映射为新ID '{new_id}'")
                
                # 应用新ID
                elem.set('ID', id_maps[tag][old_id])
        
        # 第五步：统计修复结果
        total_remapped = sum(len(id_map) for id_map in id_maps.values())
        self.logger.info(f"成功重映射了 {total_remapped} 个ID")
        
        # 详细报告每种元素类型的重映射情况
        for tag, id_map in id_maps.items():
            if id_map:
                self.logger.info(f"元素类型 '{tag}' 重映射了 {len(id_map)} 个ID")
        
        return total_remapped > 0  # 如果有重映射，则返回True

    def _apply_part_component_name_change(self, parent_element_with_uid, comp_rule, instance, uid_for_logging):
        original_name = comp_rule['original_name']
        new_name_col = comp_rule['new_name_excel_col']
        
        # XPath to find ALL components with the original_name *within* the parent_element_with_uid.
        # parent_element_with_uid is the element that has the UId (e.g. <Access UId="39">)
        components_to_change = parent_element_with_uid.findall(f".//Component[@Name='{original_name}']")
        
        if components_to_change: # If the list is not empty
            new_name = instance.get(new_name_col)
            if new_name is not None and str(new_name).strip() != "":
                changed_count = 0
                for comp_to_change in components_to_change:
                    comp_to_change.set("Name", str(new_name).strip())
                    changed_count +=1
                self.logger.debug(f"UID {uid_for_logging}: Changed {changed_count} Component(s) with original name '{original_name}' to '{str(new_name).strip()}' (from Excel col '{new_name_col}')")
            else:
                self.logger.warning(f"UID {uid_for_logging}: No value or empty value for new Component Name in Excel col '{new_name_col}' for original_name '{original_name}'. Original Name(s) preserved for {len(components_to_change)} component(s).")
        else:
            self.logger.warning(f"UID {uid_for_logging}: No Component(s) with original_name='{original_name}' found within the element <{parent_element_with_uid.tag} UId=\"{uid_for_logging}\">.")
            # Further logging for debugging if needed:
            # all_components_in_parent = parent_element_with_uid.findall(".//Component")
            # if all_components_in_parent:
            #    self.logger.debug(f"UID {uid_for_logging}: Found {len(all_components_in_parent)} components in parent, but none matched '{original_name}'. Names found: {[c.get('Name') for c in all_components_in_parent]}")
            # else:
            #    self.logger.debug(f"UID {uid_for_logging}: No components at all found within the parent element.")
            pass

    def _apply_part_address_bitoffset_change(self, symbol_element, addr_rule, instance, uid_for_logging):
        new_bit_offset_col = addr_rule['new_bit_offset_excel_col']
        
        # Similar to component name, find the (usually first/only) Address tag.
        address_tag = symbol_element.find(".//Address")

        if address_tag is not None:
            new_offset_str = instance.get(new_bit_offset_col)
            if new_offset_str is not None and str(new_offset_str).strip() != "":
                try:
                    _ = int(str(new_offset_str).strip()) 
                    old_offset_for_log = address_tag.get("BitOffset", "N/A")
                    address_tag.set("BitOffset", str(new_offset_str).strip())
                    self.logger.debug(f"UID {uid_for_logging}: Address BitOffset (originally '{old_offset_for_log}') -> '{str(new_offset_str).strip()}' (from Excel col '{new_bit_offset_col}')")
                except ValueError:
                    self.logger.error(f"UID {uid_for_logging}: Invalid BitOffset value '{str(new_offset_str).strip()}' from Excel col '{new_bit_offset_col}'. Not a valid integer.")
            else:
                self.logger.warning(f"UID {uid_for_logging}: No value or empty value for BitOffset in Excel col '{new_bit_offset_col}'. Original BitOffset '{address_tag.get('BitOffset')}' preserved.")
        else:
             self.logger.warning(f"UID {uid_for_logging}: Address tag not found for BitOffset change in Symbol based on rule: {addr_rule}")

    def _apply_part_constant_value_change(self, access_element, const_rule, instance, uid_for_logging):
        original_value_for_doc = const_rule['original_value'] # Keep for logging/documentation
        new_value_col = const_rule['new_value_excel_col']
        
        # For ConstantValue, we assume there's one target <ConstantValue> tag within the <Access Scope="LiteralConstant">
        constant_value_tag = access_element.find(".//ConstantValue")
        
        if constant_value_tag is not None:
            new_value = instance.get(new_value_col)
            if new_value is not None and str(new_value).strip() != "":
                old_value_for_log = constant_value_tag.text
                constant_value_tag.text = str(new_value).strip()
                self.logger.debug(f"UID {uid_for_logging}: ConstantValue (originally '{old_value_for_log}', rule expected '{original_value_for_doc}') -> '{str(new_value).strip()}' (from Excel col '{new_value_col}')")
            else:
                self.logger.warning(f"UID {uid_for_logging}: No value for ConstantValue in Excel col '{new_value_col}' (rule expected original '{original_value_for_doc}'). Original value '{constant_value_tag.text}' preserved.")
        else:
            self.logger.warning(f"UID {uid_for_logging}: ConstantValue tag not found for rule: {const_rule}")

    def _customize_parts_content(self, parts_xml, instance, index):
        self.logger.info(f"Customizing <Parts> content for instance '{instance['name']}' (Excel row {instance['excel_row']}) using structured rules.")
        
        try:
            parts_root = etree.fromstring(f"<root>{parts_xml}</root>") # Wrap to make it valid XML
            
            rules_map = self.PARTS_MODIFICATIONS_SINGLE_INPUT if not instance.get('is_double_input_type') else self.PARTS_MODIFICATIONS_DOUBLE_INPUT
            
            for uid_str, uid_rules in rules_map.items():
                # Find all <Access UId="X"> or <Part UId="X"> etc. elements with this UID directly under <root>
                # (since parts_xml is a fragment, elements with UID are direct children of parts_root)
                elements_with_uid = parts_root.xpath(f"./*[@UId='{uid_str}']")

                if not elements_with_uid:
                    # self.logger.debug(f"UID {uid_str}: No element found in <Parts> fragment.")
                    continue
                
                for access_like_element in elements_with_uid: # Typically only one, but loop just in case
                    self.logger.debug(f"UID {uid_str}: Processing element <{access_like_element.tag}>")
                    # symbol_element = access_like_element.find(".//Symbol") # Changes usually happen within a Symbol # OLD LOGIC
                    
                    if "components" in uid_rules:
                        # if not symbol_element: # OLD LOGIC
                        #     self.logger.warning(f"UID {uid_str}: <Symbol> tag not found, cannot apply component rules.") # OLD LOGIC
                        # else: # OLD LOGIC
                        for comp_rule in uid_rules["components"]:
                            # Pass the access_like_element itself, so the helper can search thoroughly within it
                            self._apply_part_component_name_change(access_like_element, comp_rule, instance, uid_str)
                    
                    if "addresses" in uid_rules:
                        # Address changes are typically more specific, often to the first Address tag found.
                        # If UIDs 39/45 needed specific address changes for multiple addresses, this might need adjustment.
                        # For now, assuming current logic (find first symbol, then first address) is OK as Parts_modify.md doesn't target address for these UIDs.
                        symbol_element_for_address = access_like_element.find(".//Symbol") 
                        if not symbol_element_for_address:
                            self.logger.warning(f"UID {uid_str}: <Symbol> tag not found, cannot apply address rules.")
                        else:
                            for addr_rule in uid_rules["addresses"]:
                                self._apply_part_address_bitoffset_change(symbol_element_for_address, addr_rule, instance, uid_str)

                    if "constants" in uid_rules:
                        # ConstantValue can be directly under the access_like_element if it's <Access Scope="LiteralConstant">
                        # This seems fine as is.
                        for const_rule in uid_rules["constants"]:
                             self._apply_part_constant_value_change(access_like_element, const_rule, instance, uid_str)
            
            # Convert modified parts_root back to string (excluding the dummy <root> tag)
            modified_parts_str = ""
            for child in parts_root:
                modified_parts_str += etree.tostring(child, encoding='unicode')
            
            return modified_parts_str
        
        except Exception as e:
            self.logger.error(f"Error processing <Parts> content for instance '{instance['name']}': {str(e)}", exc_info=True)
            return parts_xml # Return original on error
    
    # Remove or comment out old helper methods:
    # _modify_input_element, _modify_output_element, _modify_db_reference, 
    # _modify_constant_value, _modify_instance_reference

    # ... (rest of the class, including generate_xml, _customize_network_content etc.)
    # _customize_network_content will need to be simplified next.

    def generate_xml(self):
        """
        根据Excel数据生成XML文件，严格按照prompt.md中的要求
        """
        self.logger.info(f"开始生成XML文件: {self.output_filepath}")
        
        try:
            # 创建备份目录（如果不存在）
            backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backups")
            os.makedirs(backup_dir, exist_ok=True)
            
            # 如果输出文件已存在，创建备份
            if os.path.exists(self.output_filepath):
                backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = os.path.join(backup_dir, f"backup_{backup_time}_{os.path.basename(self.output_filepath)}")
                shutil.copy2(self.output_filepath, backup_file)
                self.logger.info(f"已创建备份: {backup_file}")
            
            # 读取Excel数据
            instances, uid_param_map, op_mode_comment_for_footer = self.read_excel_data(self.excel_filepath)
            if not instances:
                self.logger.error("未读取到Excel数据或实例列表为空。请检查Excel文件。")
                return False
            
            # 读取模板文件
            self.logger.info(f"读取模板文件: {self.template_filepath}")
            with open(self.template_filepath, 'r', encoding='utf-8') as f:
                template_lines = f.readlines()
            
            output_lines = []
            
            # 1. 复制XML头部（1-65行）
            self.logger.info("复制XML头部（1-65行）")
            header_lines = template_lines[:65]
            output_lines.extend(header_lines)
            
            # 2. 生成实例声明
            self.logger.info("生成实例声明内容")
            instance_template_lines = template_lines[65:436]  # 模板中的实例声明部分
            
            for i, instance in enumerate(instances):
                # 复制模板的实例声明部分
                instance_lines = copy.deepcopy(instance_template_lines)
                
                # 处理第66行 Member Name
                if len(instance_lines) > 0:
                    line = instance_lines[0]
                    if 'Member Name="' in line:
                        # 提取Member Name后的原始名称
                        start_idx = line.find('Member Name="') + len('Member Name="')
                        end_idx = line.find('"', start_idx)
                        original_name = line[start_idx:end_idx]
                        # 替换为实例名称 (G列内容)
                        instance_lines[0] = line.replace(
                            f'Member Name="{original_name}"', 
                            f'Member Name="{instance["name"]}"'
                        )
                        self.logger.debug(f"已将第66行的Member Name从'{original_name}'修改为'{instance['name']}'")
                
                # 处理第68行 IntegerAttribute Name="Offset" 的值
                for j, line in enumerate(instance_lines):
                    # 只修改实例声明的整体偏移量（第68行），不修改实例内部的偏移量
                    # 通过检查行内容是否包含特定标记来确保只修改实例声明的第一个偏移量
                    if '<IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">' in line and j < 5:  # 只处理前几行中的偏移量，确保是实例声明的偏移量
                        # 提取原始偏移量
                        start_idx = line.find('<IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">') + len('<IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">')
                        end_idx = line.find('</IntegerAttribute>')
                        if end_idx > start_idx:
                            # 替换为递增的偏移量，每个实例递增256
                            instance_lines[j] = line.replace(
                                f'<IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>',
                                f'<IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">{i * 256}</IntegerAttribute>'
                            )
                            self.logger.debug(f"已将实例 '{instance['name']}' 的整体偏移量设置为 {i * 256}")
                        # 只处理第一个匹配的偏移量，然后退出循环
                        break
                
                # 处理第78行 MultiLanguageText
                for j, line in enumerate(instance_lines):
                    if '<MultiLanguageText Lang="zh-CN">' in line:
                        # 提取原始文本
                        start_idx = line.find('<MultiLanguageText Lang="zh-CN">') + len('<MultiLanguageText Lang="zh-CN">')
                        end_idx = line.find('</MultiLanguageText>')
                        if end_idx > start_idx:
                            original_text = line[start_idx:end_idx]
                            # 替换为实例注释 (H列内容)，而不是实例名称
                            instance_lines[j] = line.replace(
                                f'<MultiLanguageText Lang="zh-CN">{original_text}</MultiLanguageText>',
                                f'<MultiLanguageText Lang="zh-CN">{instance["comment"]}</MultiLanguageText>'
                            )
                            self.logger.debug(f"已将MultiLanguageText从'{original_text}'修改为'{instance['comment']}'")
                
                # 将处理后的行添加到输出
                output_lines.extend(instance_lines)
            
            # 3. 添加固定变量声明
            self.logger.info("添加固定变量声明（3028-3076行）")
            fixed_vars_lines = template_lines[3027:3076]  # 从3028到3076行的内容
            
            # 计算BOOL变量的偏移地址
            # 计算最后一个实例的偏移地址
            last_instance_offset = (len(instances) - 1) * 256 if instances else 0
            
            # staManual的偏移地址是最后一个实例声明的偏移地址+256
            staManual_offset = last_instance_offset + 256
            # staAuto的偏移地址是staManual的偏移地址+1
            staAuto_offset = staManual_offset + 1
            # statReset的偏移地址是staAuto的偏移地址+1
            statReset_offset = staAuto_offset + 1
            
            self.logger.info(f"设置固定变量偏移地址：staManual={staManual_offset}, staAuto={staAuto_offset}, statReset={statReset_offset}")
            
            # 处理fixed_vars_lines，修改三个变量的偏移地址
            for i, line in enumerate(fixed_vars_lines):
                # 修改staManual的偏移地址
                if '<Member Name="staManual"' in line:
                    for j in range(i+1, len(fixed_vars_lines)):
                        if '<IntegerAttribute Name="Offset"' in fixed_vars_lines[j]:
                            start_idx = fixed_vars_lines[j].find('>') + 1
                            end_idx = fixed_vars_lines[j].find('</')
                            if end_idx > start_idx:
                                fixed_vars_lines[j] = fixed_vars_lines[j][:start_idx] + str(staManual_offset) + fixed_vars_lines[j][end_idx:]
                                self.logger.debug(f"已将staManual的偏移地址设置为{staManual_offset}")
                            break
                
                # 修改staAuto的偏移地址
                elif '<Member Name="staAuto"' in line:
                    for j in range(i+1, len(fixed_vars_lines)):
                        if '<IntegerAttribute Name="Offset"' in fixed_vars_lines[j]:
                            start_idx = fixed_vars_lines[j].find('>') + 1
                            end_idx = fixed_vars_lines[j].find('</')
                            if end_idx > start_idx:
                                fixed_vars_lines[j] = fixed_vars_lines[j][:start_idx] + str(staAuto_offset) + fixed_vars_lines[j][end_idx:]
                                self.logger.debug(f"已将staAuto的偏移地址设置为{staAuto_offset}")
                            break
                
                # 修改statReset的偏移地址
                elif '<Member Name="statReset"' in line:
                    for j in range(i+1, len(fixed_vars_lines)):
                        if '<IntegerAttribute Name="Offset"' in fixed_vars_lines[j]:
                            start_idx = fixed_vars_lines[j].find('>') + 1
                            end_idx = fixed_vars_lines[j].find('</')
                            if end_idx > start_idx:
                                fixed_vars_lines[j] = fixed_vars_lines[j][:start_idx] + str(statReset_offset) + fixed_vars_lines[j][end_idx:]
                                self.logger.debug(f"已将statReset的偏移地址设置为{statReset_offset}")
                            break
            
            output_lines.extend(fixed_vars_lines)
            
            # 4. 添加HMI/mode固定内容 - 直接复制，完全不修改任何内容，包括ID和缩进
            self.logger.info("添加HMI/mode固定内容（3077-3459行）- 直接复制，不做任何修改")
            hmi_mode_lines = template_lines[3076:3459]  # 从3077到3459行的内容
            output_lines.extend(hmi_mode_lines)  # 直接添加，不做任何修改
            
            # 5. 生成各实例的网络代码
            self.logger.info("生成实例网络代码")
            for i, instance in enumerate(instances):
                self.logger.info(f"生成实例 {i+1}/{len(instances)} 的网络代码: {instance['name']}")
                is_last = (i == len(instances) - 1)
                
                # 根据实例类型选择不同的网络模板
                if instance.get('is_double_input_type'):
                    # 双输入类型，使用5625-6099行的模板
                    self.logger.debug(f"实例 {instance['name']} 是双输入类型，使用模板行5625-6099")
                    network_template_lines = template_lines[5624:6099]  # 从5625到6099行的内容
                else:
                    # 单输入类型，使用3460-3892行的模板
                    self.logger.debug(f"实例 {instance['name']} 是单输入类型，使用模板行3460-3892")
                    network_template_lines = template_lines[3459:3892]  # 从3460到3892行的内容
                
                # 网络代码内容处理
                network_content = ''.join(network_template_lines)
                
                # 修改：确保<Parts>标签单独一行
                # 先替换所有的<Parts>标签，使其格式正确
                network_content = network_content.replace("<Parts>", "<Parts>\n    ")
                
                # 提取并处理Parts部分 - 修改正则表达式以适应新格式
                parts_match = re.search(r'<Parts>\s*(.*?)</Parts>', network_content, re.DOTALL)
                if parts_match:
                    parts_content = parts_match.group(1)
                    # 使用新方法处理Parts内容
                    modified_parts = self._customize_parts_content(parts_content, instance, i)
                    # 替换原Parts内容，保持<Parts>标签单独一行
                    network_content = re.sub(r'<Parts>\s*(.*?)</Parts>', f"<Parts>\n    {modified_parts}</Parts>", network_content, flags=re.DOTALL)
                
                # 处理其他网络内容（标题等）
                network_content = self._customize_network_content(network_content, instance, i)
                network_lines = network_content.splitlines(True)  # 带换行符的列表
                
                # 处理最后一个实例的特殊情况
                if is_last:
                    self.logger.info("这是最后一个实例，替换其末尾部分（6967-7023行）")
                    
                    # 改进：使用正则表达式更精确地匹配结束标记
                    network_content = ''.join(network_lines)
                    match = re.search(r'</FlgNet>\s*</NetworkSource>', network_content)
                    
                    if match:
                        # 找到匹配，截取到匹配位置结束
                        footer_start = match.start()
                        network_content = network_content[:footer_start]
                        # 添加结束标记，确保XML结构完整
                        network_content += '</FlgNet></NetworkSource>'
                        
                        # 处理尾部内容 (模板 6967-7023行)
                        footer_lines = template_lines[6966:7023]
                        processed_footer_lines = []
                        # Get the value from Excel Column C of the *last* instance
                        text_from_col_c = op_mode_comment_for_footer

                        for line_idx, footer_line in enumerate(footer_lines):
                            stripped_line = footer_line.strip()
                            # Target specific line from template2.xml (around line 7010)
                            if stripped_line == "<Text>去漆皮部套</Text>":
                                # Replace the content with value from Excel column C (OpMode注释) read from row 2
                                original_indent = footer_line[:footer_line.find("<")]
                                new_line = f'{original_indent}<Text>{text_from_col_c}</Text>\n'
                                processed_footer_lines.append(new_line)
                                self.logger.info(f"Footer: Replaced '<Text>去漆皮部套</Text>' with '<Text>{text_from_col_c}</Text>' from Excel Col C (OpMode注释).")
                            elif 'NetworkSource' in footer_line and 'FlgNet' in footer_line: # Avoid duplicating these closing tags
                                pass # Do not add if it's the closing tag line that was already handled
                            else:
                                processed_footer_lines.append(footer_line)
                        
                        footer_content = ''.join(processed_footer_lines)
                        
                        # 确保不重复添加已有的结束标记 - This logic might be redundant now
                        # if '</FlgNet></NetworkSource>' in footer_content:
                        #     footer_content = footer_content.replace('</FlgNet></NetworkSource>', '')
                        
                        # 合并内容
                        # Ensure network_content ends with a newline before appending footer
                        if not network_content.endswith('\n'):
                            network_content += '\n'
                        network_content += footer_content
                    else:
                        self.logger.warning("未找到网络结束标记'</FlgNet></NetworkSource>'，无法精确替换末尾部分")
                        # 保持原有内容，避免破坏XML结构
                    
                    # 更新回网络行列表
                    network_lines = network_content.splitlines(True)
                
                # 更新ID以避免冲突 - 确保只更新网络代码部分的ID，完全不修改HMI/mode部分的ID
                network_content = ''.join(network_lines)  # 再合并为字符串
                network_content = self._update_network_ids(network_content, i)  # 更新ID
                output_lines.append(network_content)  # 添加到输出
            
            # 将所有内容合并为单一字符串
            output_content = ''.join(output_lines)
            
            # 【新增】修复Component标签格式
            output_content = self._fix_component_tags(output_content)
            
            # 【新增】确保I点描述完整
            output_content = self._ensure_i_point_descriptions(output_content)
            
            # 【新增】确保Q点描述完整
            output_content = self._ensure_q_point_descriptions(output_content)
            
            # 【新增】特别处理带AccessModifier属性的Component标签
            output_content = self._preserve_access_modifier_components(output_content)
            
            # 写入输出文件
            with open(self.output_filepath, 'w', encoding='utf-8') as f:
                f.write(output_content)
            
            # 使用fix_xml_component模块进行额外的XML格式修复
            try:
                # 尝试导入fix_xml_component模块
                try:
                    # 先检查模块是否存在
                    import importlib.util
                    spec = importlib.util.find_spec('fix_xml_component')
                    if spec is not None:
                        # 使用importlib动态导入模块，避免静态导入报错
                        fix_xml_component = importlib.import_module('fix_xml_component')
                        # 通过getattr获取模块中的函数
                        fix_component_tags = getattr(fix_xml_component, 'fix_component_tags')
                        process_file = getattr(fix_xml_component, 'process_file')
                        
                        self.logger.info("使用fix_xml_component模块进行额外的XML格式修复")
                        
                        # 直接使用process_file函数处理整个文件
                        temp_fixed_file = f"{self.output_filepath}.fixed"
                        if process_file(self.output_filepath, temp_fixed_file):
                            self.logger.info(f"XML格式修复成功，已保存到临时文件: {temp_fixed_file}")
                            
                            # 验证修复后的文件
                            try:
                                # 尝试解析修复后的XML以验证格式
                                from lxml import etree
                                etree.parse(temp_fixed_file)
                                self.logger.info("修复后的XML解析成功，格式正确")
                                
                                # 将修复后的文件替换原文件
                                shutil.move(temp_fixed_file, self.output_filepath)
                                self.logger.info(f"已将修复后的文件替换原文件: {self.output_filepath}")
                            except Exception as parse_error:
                                self.logger.warning(f"修复后的XML解析警告: {str(parse_error)}")
                                self.logger.warning(f"保留原文件: {self.output_filepath}")
                        else:
                            self.logger.warning("XML格式修复失败，保留原文件")
                    else:
                        self.logger.warning("找不到fix_xml_component模块，跳过额外的XML格式修复")
                except ImportError:
                    self.logger.warning("无法导入fix_xml_component模块，跳过额外的XML格式修复")
            except Exception as fix_error:
                self.logger.warning(f"XML格式修复过程中出错: {str(fix_error)}")
            
            # 尝试解析最终的XML文件以验证格式
            try:
                from lxml import etree
                tree = etree.parse(self.output_filepath)
                self.root = tree.getroot()
                self.logger.info(f"最终XML解析成功，格式正确，文件已保存: {self.output_filepath}")
                
                # 计算行数
                with open(self.output_filepath, 'r', encoding='utf-8') as f:
                    line_count = len(f.readlines())
                self.logger.info(f"生成的XML文件共有 {line_count} 行")
                
                # 计算文件大小
                file_size = os.path.getsize(self.output_filepath) / 1024  # KB
                self.logger.info(f"生成的XML文件大小: {file_size:.2f} KB")
            except Exception as final_error:
                self.logger.warning(f"最终XML验证时出错: {str(final_error)}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"生成XML时发生错误: {str(e)}", exc_info=True)
            return False
        
    def _customize_instance_declaration(self, template_lines, instance):
        """自定义实例声明部分的内容"""
        result_lines = []
        for line in template_lines:
            # 修改Member元素的Name属性 (第66行)
            #if 'Member Name="NA1C101左缓存拦料气缸1"' in line:
                #line = line.replace('NA1C101左缓存拦料气缸1', instance['name'])
            if 'Member Name="NA1C101左缓存挡料气缸1"' in line:
                line = line.replace('NA1C101左缓存挡料气缸1', instance['name'])
            
            # 修改MultiLanguageText元素内容 (第78行)
            #elif '<MultiLanguageText Lang="zh-CN">左缓存拦料气缸1</MultiLanguageText>' in line:
                #line = line.replace('左缓存拦料气缸1', instance['comment'])
            elif '<MultiLanguageText Lang="zh-CN">左缓存挡料气缸1</MultiLanguageText>' in line:
                line = line.replace('左缓存挡料气缸1', instance['comment'])
            
            # 替换I/O地址
            if 'I132.0' in line and 'forward_sensor_addr' in instance:
                line = line.replace('I132.0', instance['forward_sensor_addr'])
            if 'I132.1' in line and 'backward_sensor_addr' in instance:
                line = line.replace('I132.1', instance['backward_sensor_addr'])
            if 'Q216.4' in line and 'forward_output_addr' in instance:
                line = line.replace('Q216.4', instance['forward_output_addr'])
            if 'Q216.5' in line and 'backward_output_addr' in instance:
                line = line.replace('Q216.5', instance['backward_output_addr'])
                
            result_lines.append(line)
        return result_lines

    def _customize_network_content(self, network_content, instance, index):
        """
        自定义网络模板中的参数。
        主要职责:
        1. 替换网络标题中的实例注释 (原为实例名称)
        2. 将 "staManual" 替换为实际的实例名称。
        其他 Component Name 和 BitOffset 的修改现在由 _customize_parts_content 和 _fix_component_tags 处理。
        """
        self.logger.debug(f"Customizing network content for instance '{instance['name']}' (Excel row {instance['excel_row']}) - simplified.")

        # 1. 替换网络标题中的实例注释 (原为实例名称)
        network_content = re.sub(r'<Text>([^<]+)</Text>', 
                                 f'<Text>{instance["comment"]}</Text>', # Changed from instance["name"] to instance["comment"]
                                 network_content)
        
        # 2. "staManual" 相关的全局替换已被移除，以确保未在Parts_modify.md中指定的组件保持模板原样。
        
        # BitOffset 修改 和 Component Name 修改（除了标题）
        # 现在都由 _customize_parts_content (基于 Parts_modify.md 规则)
        # 和后续的 _fix_component_tags (通用格式修复) 处理。
        # 因此，之前在此函数中进行的复杂 Component Name 构造和 BitOffset re.sub 调用
        # 都被移除了。

        self.logger.debug(f"Network content customization complete for '{instance['name']}'.")
        return network_content

    # 类静态变量，用于跟踪当前ID值
    _current_global_id = 0x13  # 从十六进制13开始

    def _update_network_ids(self, network_content, index):
        """
        更新网络中的ID属性，为每个实例分配唯一ID
        避免不同实例间的ID冲突
        特别处理各种ID字段，如 <MultilingualText ID="13" ...> 等
        
        注意：此函数只处理网络代码部分的ID，不处理HMI/mode固定内容部分的ID（1-12）
        """
        # 在首次运行时重置静态ID计数器
        if index == 0:
            XMLGenerator._current_global_id = 0x13  # 从十六进制13开始
            
        self.logger.info(f"实例 {index+1} 的ID基础值: 0x{XMLGenerator._current_global_id:X}")
        
        # 首先检查是否包含HMI/mode固定内容部分（ID值为1-12）
        # 如果包含，直接返回原内容，不做任何修改
        hmi_mode_id_pattern = r'ID="([1-9]|1[0-2]|[A-F])"'
        if re.search(hmi_mode_id_pattern, network_content):
            self.logger.info("检测到HMI/mode固定内容部分，保持原样不修改")
            return network_content
        
        # 创建ID映射字典，用于跟踪已分配的ID
        id_map = {}
        
        # 提取网络结束和开始标记之间的内容
        network_end_pattern = r'</FlgNet></NetworkSource>'
        network_start_pattern = r'<NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">'
        
        # 分割网络内容为三部分：网络代码、网络结束到下一个网络开始之间的部分、其他部分
        parts = re.split(f'({network_end_pattern}.*?{network_start_pattern})', network_content, flags=re.DOTALL)
        
        result = []
        for i, part in enumerate(parts):
            # 如果是网络结束到下一个网络开始之间的部分，需要特殊处理ID
            if re.search(f'{network_end_pattern}.*?{network_start_pattern}', part, flags=re.DOTALL):
                # 处理网络结束到下一个网络开始之间的部分
                # 这部分包含MultilingualText和CompileUnit元素，需要确保ID连续性
                
                # 创建所有需要处理的ID列表，确保按顺序处理
                id_elements = []
                all_id_matches = re.finditer(r'ID="([^"]+)"', part)
                for match in all_id_matches:
                    old_id = match.group(1)
                    # 跳过HMI/mode固定内容部分的ID（1-12）
                    if old_id in ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', '10', '11', '12']:
                        continue
                    id_elements.append((match.start(), old_id))
                
                # 按照在文本中的位置排序
                id_elements.sort()
                
                # 先创建映射关系
                for _, old_id in id_elements:
                    if old_id not in id_map:
                        new_id = f"{XMLGenerator._current_global_id:X}".upper()  # 直接使用十六进制格式
                        id_map[old_id] = new_id
                        self.logger.debug(f"[ID_REMAP] 将ID='{old_id}'映射为'{new_id}'")
                        XMLGenerator._current_global_id += 1  # 递增全局ID计数器
                
                # 替换所有ID属性
                modified_part = part
                for _, old_id in reversed(id_elements):  # 从后向前替换，避免位置变化
                    new_id = id_map[old_id]
                    modified_part = modified_part.replace(f'ID="{old_id}"', f'ID="{new_id}"')
                
                result.append(modified_part)
            else:
                # 对于网络代码部分，使用原有的ID替换逻辑
                lines = part.splitlines(True)
                for line in lines:
                    # 跳过包含HMI/mode固定内容部分的ID（1-12）
                    if any(f'ID="{id_val}"' in line for id_val in ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', '10', '11', '12']):
                        result.append(line)
                        continue
                    
                    # 替换MultilingualText的ID
                    if '<MultilingualText ID="' in line:
                        old_id = re.search(r'<MultilingualText ID="([^"]+)"', line).group(1)
                        if old_id in id_map:
                            new_id = id_map[old_id]
                        else:
                            new_id = f"{XMLGenerator._current_global_id:X}".upper()  # 直接使用十六进制格式
                            id_map[old_id] = new_id
                            XMLGenerator._current_global_id += 1  # 递增全局ID计数器
                        line = re.sub(r'<MultilingualText ID="[^"]+"', f'<MultilingualText ID="{new_id}"', line)
                    
                    # 替换MultilingualTextItem的ID
                    elif '<MultilingualTextItem ID="' in line:
                        old_id = re.search(r'<MultilingualTextItem ID="([^"]+)"', line).group(1)
                        if old_id in id_map:
                            new_id = id_map[old_id]
                        else:
                            new_id = f"{XMLGenerator._current_global_id:X}".upper()  # 直接使用十六进制格式
                            id_map[old_id] = new_id
                            XMLGenerator._current_global_id += 1  # 递增全局ID计数器
                        line = re.sub(r'<MultilingualTextItem ID="[^"]+"', f'<MultilingualTextItem ID="{new_id}"', line)
                    
                    # 替换CompileUnit的ID
                    elif '<SW.Blocks.CompileUnit ID="' in line:
                        old_id = re.search(r'<SW.Blocks.CompileUnit ID="([^"]+)"', line).group(1)
                        if old_id in id_map:
                            new_id = id_map[old_id]
                        else:
                            new_id = f"{XMLGenerator._current_global_id:X}".upper()  # 直接使用十六进制格式
                            id_map[old_id] = new_id
                            XMLGenerator._current_global_id += 1  # 递增全局ID计数器
                        line = re.sub(r'<SW.Blocks.CompileUnit ID="[^"]+"', f'<SW.Blocks.CompileUnit ID="{new_id}"', line)
                    
                    result.append(line)
        
        self.logger.info(f"实例 {index+1} 的ID映射完成，最终ID为: 0x{(XMLGenerator._current_global_id-1):X}")
        return ''.join(result)

    def _get_bit_offset(self, address: str) -> int:
        """
        将地址转换为位偏移量
        例如: I0.0 -> 0, I0.1 -> 1, Q0.0 -> 800, Q0.1 -> 801, M0.0 -> 1600
        
        Args:
            address (str): 地址字符串，如'I0.0', 'Q0.1', 'M0.0'
            
        Returns:
            int: 位偏移量，如果无法解析则返回0
        """
        try:
            # 检查地址格式是否为 I/Q/M + 数字 + . + 数字
            match = re.match(r'([IQM])(\d+)\.(\d+)', address)
            if not match:
                self.logger.warning(f"无法解析地址格式: {address}，使用默认偏移量0")
                return 0
            
            area, byte_num, bit_num = match.groups()
            byte_num, bit_num = int(byte_num), int(bit_num)
            
            # 计算位偏移量
            if area == 'I':
                # I区域: 偏移量 = 字节号 * 8 + 位号
                return byte_num * 8 + bit_num
            elif area == 'Q':
                # Q区域: 偏移量 = 字节号 * 8 + 位号 + 800
                return byte_num * 8 + bit_num + 800
            elif area == 'M':
                # M区域: 偏移量 = 字节号 * 8 + 位号 + 1600
                return byte_num * 8 + bit_num + 1600
            
            # 默认返回
            return 0
        except Exception as e:
            self.logger.error(f"转换地址 {address} 到位偏移量时出错: {str(e)}")
            return 0

    def update_xml_basic_info(self):
        """更新XML文件的基本信息，如FB的名称、编号等"""
        # 先读取Excel数据
        data = self.read_excel_data(self.excel_filepath)
        op_mode_data = data['op_mode']
        instances = data['instances']
        
        # 更新FB名称、编号
        self.update_fb_attributes(self.root, op_mode_data)

    def update_fb_attributes(self, root, op_mode_data):
        """FB属性更新 (Name, Number, Comment)"""
        # 设置命名空间
        ns_prefix = "ns"
        ns_uri = self.ns
        ns_dict = {ns_prefix: ns_uri} if ns_uri else {}
        xpath_prefix = f"{ns_prefix}:" if ns_uri else ""
        
        # 假设这是FB元素的XPath
        fb_elem = root.find(f".//{xpath_prefix}SW.Blocks.FB", namespaces=ns_dict)
        if fb_elem is None:
            self.logger.error("未在XML中找到FB元素")
            return False
        
        # 更新FB名称
        if op_mode_data.get('fb_name'):
            fb_name_elem = fb_elem.find(f".//{xpath_prefix}Name", namespaces=ns_dict)
            if fb_name_elem is not None:
                fb_name_elem.text = op_mode_data['fb_name']
                self.logger.info(f"已更新FB名称为: {op_mode_data['fb_name']}")
            else:
                self.logger.warning("未找到FB名称元素，无法更新")
        
        # 更新FB编号
        if op_mode_data.get('fb_number'):
            fb_number_attr = fb_elem.find(f".//{xpath_prefix}ObjectList/{xpath_prefix}IntegerAttribute[@Name='Number']", namespaces=ns_dict)
            if fb_number_attr is not None:
                fb_number_attr.text = str(op_mode_data['fb_number'])
                self.logger.info(f"已更新FB编号为: {op_mode_data['fb_number']}")
            else:
                self.logger.warning("未找到FB编号属性，无法更新")
        
        # 更新FB注释
        if op_mode_data.get('op_mode_comment'):
            # 假设注释在MultiLanguageText元素内
            comment_text = fb_elem.find(f".//{xpath_prefix}MultiLanguageText[@Lang='zh-CN']", namespaces=ns_dict)
            if comment_text is not None:
                comment_text.text = op_mode_data['op_mode_comment']
                self.logger.info(f"已更新FB注释为: {op_mode_data['op_mode_comment']}")
            else:
                self.logger.warning("未找到FB注释元素，无法更新")
        
        return True 

    def _ensure_i_point_descriptions(self, xml_content):
        self.logger.info("开始确保I点描述完整 (v2 - surgical replace)")
        
        i_descriptions = {
            "I132.0": "I132.0 左UPIN缓存挡料1气缸 伸出到位 NA1-C101a",
            "I132.1": "I132.1 左UPIN缓存挡料1气缸 缩回到位 NA1-C101b", 
            "I132.2": "I132.2 左UPIN缓存挡料2气缸 伸出到位 NA1-C201a",
            "I132.3": "I132.3 左UPIN缓存挡料2气缸 缩回到位 NA1-C201b",
            "I132.4": "I132.4 左UPIN缓存挡料3气缸 伸出到位 NA1-C301a",
            "I132.5": "I132.5 左UPIN缓存挡料3气缸 缩回到位 NA1-C301b",
            "I132.6": "I132.6 左UPIN缓存料盘抽出气缸 伸出到位 NA1-C401a",
            "I132.7": "I132.7 左UPIN缓存料盘抽出气缸 缩回到位 NA1-C401b",
            "I133.0": "I133.0 左IPIN缓存料盘定位气缸 伸出到位 NA1-C501a",
            "I133.1": "I133.1 左IPIN缓存料盘定位气缸 缩回到位 NA1-C501b",
            "I133.2": "I133.2 左IPIN缓存料盘定位气缸 伸出到位 NA1-C501c",
            "I133.3": "I133.3 左IPIN缓存料盘定位气缸 缩回到位 NA1-C501d"
        }
        
        def replacer_i(match):
            # match.group(1) is '<Component Name="'
            # match.group(2) is the current name value (e.g., "I132.0")
            # match.group(3) is '"' plus rest of attributes and tag end ' ... />'
            current_name_val = match.group(2)
            if current_name_val in i_descriptions:
                self.logger.debug(f"I-Point Desc: Replacing Name \'{current_name_val}\' with \'{i_descriptions[current_name_val]}\'")
                return f"{match.group(1)}{i_descriptions[current_name_val]}{match.group(3)}"
            return match.group(0) # No change

        # Pattern to find <Component Name="VALUE" ... />
        # It captures:
        # 1: '<Component Name="'
        # 2: The current value of the Name attribute (must not contain quotes)
        # 3: The closing quote of Name attribute and the rest of the self-closing tag.
        # This pattern assumes Name value itself doesn't contain "
        i_pattern_surgical = r'(<Component Name=")([^"]+)("(?:\\s+[^>]*)?/>)'
        xml_content = re.sub(i_pattern_surgical, replacer_i, xml_content)
        
        self.logger.info("I点描述完整性检查完成 (v2)")
        return xml_content
        
    def _ensure_q_point_descriptions(self, xml_content):
        self.logger.info("开始确保Q点描述完整 (v2 - surgical replace)")
        
        q_descriptions = {
            "Q216.4": "Q216.4 NA1C101挡料气缸1伸出",
            "Q216.5": "Q216.5 NA1C101挡料气缸1缩回",
            "Q216.6": "Q216.6 NA1C201挡料气缸2伸出",
            "Q216.7": "Q216.7 NA1C201挡料气缸2缩回",
            "Q217.0": "Q217.0 NA1C301挡料气缸3伸出",
            "Q217.1": "Q217.1 NA1C301挡料气缸3缩回",
            "Q217.2": "Q217.2 NA1C401料盘抽出气缸伸出",
            "Q217.3": "Q217.3 NA1C401料盘抽出气缸缩回",
            "Q217.4": "Q217.4 NA1C501料盘定位气缸伸出",
            "Q217.5": "Q217.5 NA1C501料盘定位气缸缩回",
            "Q216.0": "Q216.0 NA1C601料盒搬运升降气缸伸出",
            "Q216.1": "Q216.1 NA1C601料盒搬运升降气缸缩回",
            "Q216.2": "Q216.2 NA1C701料盒搬运夹爪气缸伸出",
            "Q216.3": "Q216.3 NA1C701料盒搬运夹爪气缸缩回",
            "Q10.5": "Q10.5 NA1C401左缓存挡料气缸4工作位输出", # Note: This seems like an instance-specific name
            "Q10.6": "Q10.6 NA1C401左缓存挡料气缸4原位输出"  # Note: This seems like an instance-specific name
        }
        
        def replacer_q(match):
            current_name_val = match.group(2)
            if current_name_val in q_descriptions:
                self.logger.debug(f"Q-Point Desc: Replacing Name \'{current_name_val}\' with \'{q_descriptions[current_name_val]}\'")
                return f"{match.group(1)}{q_descriptions[current_name_val]}{match.group(3)}"
            return match.group(0)

        q_pattern_surgical = r'(<Component Name=")([^"]+)("(?:\\s+[^>]*)?/>)'
        xml_content = re.sub(q_pattern_surgical, replacer_q, xml_content)
        
        self.logger.info("Q点描述完整性检查完成 (v2)")
        return xml_content

    def _preserve_access_modifier_components(self, xml_content):
        """
        专门处理带有AccessModifier属性的Component标签，确保在整个XML内容中保留它们，
        特别是修复 <Component Name="unit"/> 后跟其游离的 <Access> 子内容的常见问题。
        
        Args:
            xml_content (str): 原始XML内容
            
        Returns:
            str: 处理后的XML内容
        """
        self.logger.info("开始精确修复和保留 'unit' Component标签的结构和属性")

        # 模式: 匹配 <Component Name="unit" /> (捕获可选的其他属性)
        #       后面紧跟着 <Access Scope="LiteralConstant">...</Access> (捕获Access块)
        # 捕获组1: (<Component Name="unit") - 组件开始
        # 捕获组2: (\s*[^A/]*?)           - unit 上可能存在的其他非AccessModifier属性
        # 捕获组3: (\s*/>)                - 自闭合部分
        # 捕获组4: (\s*<Access Scope="LiteralConstant">.*?<\/Access>) - 完整的Access子内容块
        
        pattern_broken_unit = r'(<Component Name="unit")(\s*[^A/]*?)(\s*/>)(\s*<Access Scope="LiteralConstant">.*?<\/Access>)'

        def fix_unit_tag_with_following_access(match):
            component_start_raw = match.group(1)  # e.g., "<Component Name="unit"
            other_attrs_on_unit = match.group(2) # e.g., " Version=\"1\"" or ""
            # self_closing_part_raw = match.group(3) # e.g., " />"
            access_block_raw = match.group(4)    # e.g., "  <Access Scope=...></Access>"

            # 安全检查：如果原始unit标签上意外地已经有了AccessModifier，则不处理，防止重复添加或逻辑错误
            if "AccessModifier" in other_attrs_on_unit:
                self.logger.warning(f"检测到'unit'标签已存在AccessModifier属性，跳过修复: {match.group(0)[:150]}")
                return match.group(0) # 返回原始匹配，不做修改

            # 1. 构建新的unit开始标签，并强制添加AccessModifier="Array"
            #    保留原始unit标签上可能存在的其他属性 (other_attrs_on_unit)
            new_unit_start_tag = f'{component_start_raw}{other_attrs_on_unit} AccessModifier="Array">'
            
            # 2. 保持捕获到的Access块不变
            # 3. 在Access块之后添加 </Component> 来正确闭合unit组件
            corrected_structure = f'{new_unit_start_tag}{access_block_raw}</Component>'
            
            self.logger.info(f"修正了'unit'组件。原始: '{match.group(0)[:150]}...' -> 修正为: '{corrected_structure[:150]}...'")
            return corrected_structure

        # 使用循环来处理所有非重叠匹配项，直到没有更多更改发生
        previous_content = ""
        modified_content = xml_content
        max_iterations = 10 # 防止意外的无限循环
        iteration_count = 0
        while iteration_count < max_iterations:
            iteration_count += 1
            new_content = re.sub(pattern_broken_unit, fix_unit_tag_with_following_access, modified_content, flags=re.DOTALL)
            if new_content == modified_content:
                self.logger.info(f"在第 {iteration_count} 次迭代后，'unit'标签修复没有进一步更改。")
                break
            modified_content = new_content
            if iteration_count == 1:
                 self.logger.info("首次迭代完成 'unit' 标签修复。")
        else:
            self.logger.warning("'unit'标签修复达到了最大迭代次数，可能仍有未修复的实例或模式复杂性问题。")

        self.logger.info("完成对 'unit' Component标签的精确修复尝试。")
        return modified_content

    def _fix_component_tags(self, xml_content):
        self.logger.info("开始修复Component标签格式 (v3 - more conservative)")

        # Pattern1: Handles specific quoted format: <Component Name="&quot;CORE_NAME&quot; JUNK_TEXT" ATTR="VAL"/>
        # This pattern is quite specific and aims to extract CORE_NAME.
        def fix_problematic_quoted_component(match):
            name_content = match.group(1)  # CORE_NAME
            other_attrs_str = match.group(2) # ATTR="VAL" (captured from original pattern)
            # Ensure other_attrs_str starts with a space if not empty, or is empty
            attrs_part = f" {other_attrs_str.strip()}" if other_attrs_str.strip() else ""
            return f'<Component Name="{name_content}"{attrs_part} />'
        
        pattern1 = r'<Component Name="&quot;([^&"]+)&quot;\\s+[^"]*?"\\s*([^>]*)/>'
        xml_content = re.sub(pattern1, fix_problematic_quoted_component, xml_content)

        # Pattern3: Handles non-self-closing tags: <Component Name="X" Attributes>Children</Component>
        # This should be relatively safe as it preserves children and attributes.
        def fix_component_with_children(match):
            name = match.group(1).replace('&quot;', '')
            attrs_str_captured = match.group(2) 
            children = match.group(3)
            attrs_str_processed = attrs_str_captured.strip()
            attrs_part = f" {attrs_str_processed}" if attrs_str_processed else ""
            return f'<Component Name="{name}"{attrs_part}>{children}</Component>'

        pattern3 = r'<Component Name="([^"]+)"([^>]*)>(.*?)</Component>'
        xml_content = re.sub(pattern3, fix_component_with_children, xml_content, flags=re.DOTALL)

        # Pattern2: Handles genuinely self-closing tags: <Component Name="X" ATTR="VAL" />
        # Make this more conservative: if the name looks complex (e.g., contains spaces, multiple parts),
        # be very careful or even skip, as it might have been set correctly by _ensure_..._descriptions.
        def fix_genuinely_self_closing_component_conservative(match):
            full_tag = match.group(0)
            
            name_match = re.search(r'Name="([^"]+)"', full_tag)
            if not name_match:
                return full_tag # Should not happen if pattern2 matched

            name_value = name_match.group(1)

            # If name_value already contains spaces, it's likely a description.
            # Avoid re-processing it aggressively if it doesn't have &quot; issues.
            # The main goal here is to clean &quot; if present, or ensure simple names are well-formed.
            if ' ' in name_value and '&quot;' not in name_value:
                # It's a descriptive name without &quot;, likely already correct.
                # Or, if it's the ""A" B" error, this won't fix it but also won't worsen it.
                # Let's check if it has the specific error structure: ""... " ..."
                if name_value.startswith('"') and name_value.count('"') >= 3: # Heuristic for ""A" B"
                     self.logger.debug(f"Self-closing fix: Name \'{name_value}\' looks like the error format, returning full_tag: {full_tag}")
                     return full_tag # Don't touch if it's already the error pattern
                
                self.logger.debug(f"Self-closing fix: Name \'{name_value}\' contains spaces and no &quot;, considered as is: {full_tag}")
                # Return as is, assuming it's a correctly set description.
                # Or, if it's some other complex name we don't want to mess up.
                return full_tag 

            # If we are here, name_value is simple (no spaces) or contains &quot;
            cleaned_name = name_value.replace('&quot;', '')
            
            attrs_list = re.findall(r'(\\w+)="([^"]*)"', full_tag)
            attrs_dict = {k: v for k, v in attrs_list if k.lower() != 'name'} 
            
            if "AccessModifier" in attrs_dict or "accessmodifier" in attrs_dict:
                 self.logger.debug(f"Self-closing fix: Skipping tag with AccessModifier: {full_tag}")
                 return full_tag

            attrs_str = ' '.join(f'{k}="{v}"' for k, v in attrs_dict.items())
            attrs_part = f" {attrs_str}" if attrs_str else ""
            
            reconstructed_tag = f'<Component Name="{cleaned_name}"{attrs_part} />'
            if full_tag != reconstructed_tag:
                 self.logger.debug(f"Self-closing fix: Reconstructed \'{full_tag}\' -> \'{reconstructed_tag}\'")
            return reconstructed_tag

        pattern2 = r'<Component Name="[^"]+"[^>]*?/>' # Basic self-closing pattern
        xml_content = re.sub(pattern2, fix_genuinely_self_closing_component_conservative, xml_content)
        
        self.logger.info("Component标签格式修复完成 (v3)")
        return xml_content