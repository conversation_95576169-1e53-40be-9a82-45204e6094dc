using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using OpennessTool.Models;

namespace OpennessTool.Core
{
    /// <summary>
    /// Excel数据读取器，对应Python版本的read_excel_data方法
    /// </summary>
    public class ExcelDataReader
    {
        /// <summary>
        /// 从Excel文件读取实例数据
        /// </summary>
        /// <param name="excelFilePath">Excel文件路径</param>
        /// <returns>实例数据列表和OpMode注释</returns>
        public (List<InstanceData> Instances, string OpModeComment) ReadExcelData(string excelFilePath)
        {
            var instances = new List<InstanceData>();
            string opModeComment = string.Empty;

            try
            {
                if (!File.Exists(excelFilePath))
                {
                    throw new FileNotFoundException($"Excel文件不存在: {excelFilePath}");
                }

                IWorkbook workbook;
                using (var fs = new FileStream(excelFilePath, FileMode.Open, FileAccess.Read))
                {
                    if (Path.GetExtension(excelFilePath).ToLower() == ".xlsx")
                    {
                        workbook = new XSSFWorkbook(fs);
                    }
                    else
                    {
                        workbook = new HSSFWorkbook(fs);
                    }
                }

                ISheet sheet = workbook.GetSheetAt(0);
                if (sheet == null)
                {
                    throw new InvalidOperationException("Excel文件中没有找到工作表");
                }

                // 读取表头
                var header = new List<string>();
                IRow headerRow = sheet.GetRow(0);
                if (headerRow == null)
                {
                    throw new InvalidOperationException("Excel文件中没有找到表头行");
                }

                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    var cell = headerRow.GetCell(i);
                    header.Add(GetCellValueAsString(cell));
                }

                // 验证必需的列
                var requiredColumns = new[]
                {
                    "实例名称", "实例注释", "I点-工作位1", "I点-原位1", "Q点-工作位", "Q点-原位"
                };

                var missingColumns = requiredColumns.Where(col => !header.Contains(col)).ToList();
                if (missingColumns.Any())
                {
                    throw new InvalidOperationException($"Excel文件缺少必需的列: {string.Join(", ", missingColumns)}");
                }

                // 获取列索引
                var columnIndexes = GetColumnIndexes(header);

                // 读取OpMode注释（从第一个数据行）
                if (sheet.LastRowNum >= 1 && columnIndexes.OpModeCommentCol != -1)
                {
                    IRow firstDataRow = sheet.GetRow(1);
                    if (firstDataRow != null)
                    {
                        var cell = firstDataRow.GetCell(columnIndexes.OpModeCommentCol);
                        opModeComment = GetCellValueAsString(cell);
                    }
                }

                // 读取数据行
                string firstProcessedRowDbName = string.Empty;
                string firstProcessedRowOpModeName = string.Empty;
                bool isFirstProcessedRow = true;

                for (int rowIndex = 1; rowIndex <= sheet.LastRowNum; rowIndex++)
                {
                    IRow row = sheet.GetRow(rowIndex);
                    if (row == null) continue;

                    var instanceData = ReadInstanceFromRow(row, columnIndexes, rowIndex + 1);
                    if (instanceData == null) continue; // 跳过空行

                    // 处理首行数据的默认值
                    if (isFirstProcessedRow)
                    {
                        firstProcessedRowDbName = instanceData.ManualDbName;
                        firstProcessedRowOpModeName = instanceData.OpModeName;
                        isFirstProcessedRow = false;
                    }
                    else
                    {
                        // 如果当前行的DB名称或OpMode名称为空，使用首行的值
                        if (string.IsNullOrWhiteSpace(instanceData.ManualDbName))
                            instanceData.ManualDbName = firstProcessedRowDbName;
                        if (string.IsNullOrWhiteSpace(instanceData.OpModeName))
                            instanceData.OpModeName = firstProcessedRowOpModeName;
                    }

                    instances.Add(instanceData);
                }

                workbook.Close();
            }
            catch (Exception ex)
            {
                throw new Exception($"读取Excel文件时发生错误: {ex.Message}", ex);
            }

            return (instances, opModeComment);
        }

        /// <summary>
        /// 获取列索引映射
        /// </summary>
        private ColumnIndexes GetColumnIndexes(List<string> header)
        {
            return new ColumnIndexes
            {
                NameCol = GetColumnIndex(header, "实例名称"),
                CommentCol = GetColumnIndex(header, "实例注释"),
                ForwardSensorCol = GetColumnIndex(header, "I点-工作位1"),
                BackwardSensorCol = GetColumnIndex(header, "I点-原位1"),
                ForwardOutputCol = GetColumnIndex(header, "Q点-工作位"),
                BackwardOutputCol = GetColumnIndex(header, "Q点-原位"),

                // 可选列
                ForwardSensorOffsetCol = GetColumnIndex(header, "I点-工作位1偏移量"),
                BackwardSensorOffsetCol = GetColumnIndex(header, "I点-原位1偏移量"),
                ForwardOutputOffsetCol = GetColumnIndex(header, "Q点-工作位偏移量"),
                BackwardOutputOffsetCol = GetColumnIndex(header, "Q点-原位偏移量"),

                // 双输入点列
                ForwardSensor2Col = GetColumnIndex(header, "I点-工作位2"),
                BackwardSensor2Col = GetColumnIndex(header, "I点-原位2"),
                ForwardSensor2OffsetCol = GetColumnIndex(header, "I点-工作位2偏移量"),
                BackwardSensor2OffsetCol = GetColumnIndex(header, "I点-原位2偏移量"),

                // 其他列
                DbNameCol = GetColumnIndex(header, "手动行DB块名称"),
                OpModeNameCol = GetColumnIndex(header, "OpMode名称"),
                OpModeNumberCol = GetColumnIndex(header, "OpMode编号"),
                OpModeCommentCol = GetColumnIndex(header, "OpMode注释"),
                FbTemplateCol = GetColumnIndex(header, "手动FB 块编号")
            };
        }

        /// <summary>
        /// 获取指定列名的索引
        /// </summary>
        private int GetColumnIndex(List<string> header, string columnName)
        {
            int index = header.IndexOf(columnName);
            return index >= 0 ? index : -1;
        }

        /// <summary>
        /// 从行中读取实例数据
        /// </summary>
        private InstanceData ReadInstanceFromRow(IRow row, ColumnIndexes indexes, int excelRowNumber)
        {
            string instanceName = GetCellValueAsString(row.GetCell(indexes.NameCol));

            // 如果实例名称为空，跳过这一行
            if (string.IsNullOrWhiteSpace(instanceName))
                return null;

            var instanceData = new InstanceData
            {
                Name = CleanStringQuotes(instanceName),
                Comment = CleanStringQuotes(GetCellValueAsString(row.GetCell(indexes.CommentCol))),
                ExcelRow = excelRowNumber,

                // 基本I/O地址
                ForwardSensorAddr = GetCellValueAsString(row.GetCell(indexes.ForwardSensorCol)),
                BackwardSensorAddr = GetCellValueAsString(row.GetCell(indexes.BackwardSensorCol)),
                ForwardOutputAddr = GetCellValueAsString(row.GetCell(indexes.ForwardOutputCol)),
                BackwardOutputAddr = GetCellValueAsString(row.GetCell(indexes.BackwardOutputCol)),

                // 偏移量
                ForwardSensorOffset = GetCellValueAsString(row.GetCell(indexes.ForwardSensorOffsetCol)),
                BackwardSensorOffset = GetCellValueAsString(row.GetCell(indexes.BackwardSensorOffsetCol)),
                ForwardOutputOffset = GetCellValueAsString(row.GetCell(indexes.ForwardOutputOffsetCol)),
                BackwardOutputOffset = GetCellValueAsString(row.GetCell(indexes.BackwardOutputOffsetCol)),

                // 其他属性
                ManualDbName = GetCellValueAsString(row.GetCell(indexes.DbNameCol)),
                OpModeName = GetCellValueAsString(row.GetCell(indexes.OpModeNameCol)),
                OpModeNumber = GetCellValueAsString(row.GetCell(indexes.OpModeNumberCol)),
                FbTemplate = GetCellValueAsString(row.GetCell(indexes.FbTemplateCol))
            };

            // 检查是否为双输入类型
            if (indexes.ForwardSensor2Col != -1 && indexes.BackwardSensor2Col != -1)
            {
                string forwardSensor2 = GetCellValueAsString(row.GetCell(indexes.ForwardSensor2Col));
                string backwardSensor2 = GetCellValueAsString(row.GetCell(indexes.BackwardSensor2Col));

                if (!string.IsNullOrWhiteSpace(forwardSensor2) || !string.IsNullOrWhiteSpace(backwardSensor2))
                {
                    instanceData.IsDoubleInputType = true;
                    instanceData.ForwardSensor2Addr = forwardSensor2;
                    instanceData.BackwardSensor2Addr = backwardSensor2;
                    instanceData.ForwardSensor2Offset = GetCellValueAsString(row.GetCell(indexes.ForwardSensor2OffsetCol));
                    instanceData.BackwardSensor2Offset = GetCellValueAsString(row.GetCell(indexes.BackwardSensor2OffsetCol));
                }
            }

            return instanceData;
        }

        /// <summary>
        /// 获取单元格值作为字符串
        /// </summary>
        private string GetCellValueAsString(ICell cell)
        {
            if (cell == null) return string.Empty;

            switch (cell.CellType)
            {
                case CellType.String:
                    return cell.StringCellValue?.Trim() ?? string.Empty;
                case CellType.Numeric:
                    if (DateUtil.IsCellDateFormatted(cell))
                        return cell.DateCellValue.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
                    return cell.NumericCellValue.ToString(CultureInfo.InvariantCulture);
                case CellType.Boolean:
                    return cell.BooleanCellValue.ToString();
                case CellType.Formula:
                    return cell.StringCellValue?.Trim() ?? string.Empty;
                default:
                    return string.Empty;
            }
        }

        /// <summary>
        /// 清理字符串中的引号
        /// </summary>
        private string CleanStringQuotes(string value)
        {
            if (string.IsNullOrEmpty(value)) return string.Empty;
            return value.Trim('"');
        }

        /// <summary>
        /// 列索引结构
        /// </summary>
        private class ColumnIndexes
        {
            public int NameCol { get; set; }
            public int CommentCol { get; set; }
            public int ForwardSensorCol { get; set; }
            public int BackwardSensorCol { get; set; }
            public int ForwardOutputCol { get; set; }
            public int BackwardOutputCol { get; set; }
            public int ForwardSensorOffsetCol { get; set; } = -1;
            public int BackwardSensorOffsetCol { get; set; } = -1;
            public int ForwardOutputOffsetCol { get; set; } = -1;
            public int BackwardOutputOffsetCol { get; set; } = -1;
            public int ForwardSensor2Col { get; set; } = -1;
            public int BackwardSensor2Col { get; set; } = -1;
            public int ForwardSensor2OffsetCol { get; set; } = -1;
            public int BackwardSensor2OffsetCol { get; set; } = -1;
            public int DbNameCol { get; set; } = -1;
            public int OpModeNameCol { get; set; } = -1;
            public int OpModeNumberCol { get; set; } = -1;
            public int OpModeCommentCol { get; set; } = -1;
            public int FbTemplateCol { get; set; } = -1;
        }
    }
}
