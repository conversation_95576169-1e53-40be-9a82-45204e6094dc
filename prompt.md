###xml头文件：
1-65行，完全复制模板xml中的1-65行代码，不作任何修改，包括每一行的缩进也要保持与模板xml中保持一致。


###变量声明区：实例声明内容生成：
从66行开始，请按以下思路改写相关代码：
1.直接复制@template2.xml 的66-436行，作为一个实例的声明内容，也就是对应输入表格的一行内容；
2.复制66-436行之后，需要改写以下内容：
  2.1 需要将模板xml中66行的内容：<Member Name="NA1C101左缓存挡料气缸1" Datatype="&quot;FB4000_Valve&quot;" Accessibility="Public">，其中的Member Name的值，改写成表格中G列的内容，也就是“实例名称”；
  2.2 需要将模板xml中78行的内容：<MultiLanguageText Lang="zh-CN">左缓存挡料气缸1</MultiLanguageText>，其中的“左缓存挡料气缸1”，改写成表格中H列的内容，也就是“实例注释”；
  2.3 模板xml中68行内容："<IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>"，其中的数值"0"，需要每次递增256，比如第一个实例是0，第二个就是256，第二个是512，第三个是768，以此类推；注意只改写每个实例的这一行的偏移地址，实例内部其它的内容不要修改；
3.完成以上2步，就完成了一个实例内容生成；接下来，按照以上两步的方法，根据输入表格的行内容，继续进行剩余实例声明内容的生成，直到把输入表格中所有行内容的实例声明内容都生成并改写完成为止。

###变量声明区：几个固定变量的声明：
1、直接复制模板xml中的3028-3076行的内容，也就是"staManual"、"staAuto"、"statReset"这三个BOOL变量的声明，插入到实例声明代码结尾的后一行，要保持每一行的缩进与模板xml中的一致。
2、复制完之后，需要改写三个变量的偏移地址，也就是这行内容："<IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2048</IntegerAttribute>"，其中的数值"2048"就是它们的偏移地址；改写规则是："staManual"的偏移地址是最后一个实例声明的偏移地址+256；也就是上面最后一个实例的这一行："<IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2048</IntegerAttribute>"，其中的数值2048，就是最后一个实例的偏移地址；
staAuto"的偏移地址是"staManual"的偏移地址+1，"statReset"的偏移地址是"staAuto"的偏移地址+1；
注意只改写这三个实例的这一行的偏移地址，实例内部其它的内容不要修改；

###network区：HMI display、mode固定内容：
1、直接复制模板xml中3077-3459行代码，不作任何修改，包括每一行的缩进也要保持与模板xml中保持一致；然后插入到之前变量声明代码结尾的后一行；

###network区：实例程序：
1、
  1.1针对输入表格中，只有1对输入点的行内容，即“I点-工作位2”和“I点-原位2”的内容为空，方法为：复制模板xml中3460-3892行代码，作为一个单输入点实例的程序内容；
  1.2针对输入表格中，有2对输入点的行内容，即“I点-工作位2”和“I点-原位2”的内容不为空，方法为：复制模板xml中5625-6099行代码，作为一个双输入点实例的程序内容；
2、根据输入表格的内容，改写这个实例程序内容的相关参数，只改写相关参数，不要改变结构内容；具体需要改写哪些参数，请读取manualRow_2.xlsx和template2.xml的内容，了解表格内容与实例程序内容的对应关系。
3. 从模板xml中复制的程序代码，需要改写的参数有：
3.1 不管是单输入还是双输入类型，从模板xml中复制的程序代码，最后部分：从“</FlgNet></NetworkSource>”这一行开始，到“          <NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">”这一行结束，这一段代码中包含了若干ID编号，比如 <MultilingualText ID="13" CompositionName="Comment">、<MultilingualTextItem ID="14" CompositionName="Items">、<SW.Blocks.CompileUnit ID="19" CompositionName="CompileUnits">等等；这些行中都包含ID编号；这些ID编号，在生成XML的时候是不能重复的，需要按序号往后排列；比如这个实例程序中这一段内容的ID编号从13到19，那么下一个实例程序中这一段内容的ID编号，就从1A开始，注意ID编号用16进制表示。并且只是这一段内容的ID不能重复，其它部分比如Parts、Wires部分的ID是可以重复的。
3.2 3877行:"<Text>左缓存挡料气缸1</Text>",需要把其中“左缓存挡料气缸”替换成输入表格中H列的内容，也就是“实例注释”；
4、完成以上2步，就完成了一个实例程序内容的生成；接下来，按照以上两步的方法，根据输入表格的行内容，继续进行剩余实例程序内容的生成，直到把输入表格中所有行内容的实例程序内容都生成并改写完成为止。
5、针对最后一个实例程序，需要把这个实例程序的最后一段内容，从</FlgNet></NetworkSource>行开始，到最后一行：<NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">结束，总共41行，整体替换成模板xml中6967-7023行的内容；替换完之后，再改写对应的参数。




