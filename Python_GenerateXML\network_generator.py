import logging
import re
import copy
import os
# Ensure IdGenerator is available if used directly, though it's passed from XMLGenerator
# from xml_generator_new import IdGenerator # Assuming direct import for type hinting or standalone tests

class NetworkGenerator:
    def __init__(self, logger, template_filepath: str, id_generator_instance):
        self.logger = logger if logger else logging.getLogger('NetworkGenerator')
        if not self.logger.handlers: # Basic config if no logger is passed
            logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
        self.template_filepath = template_filepath
        self.id_generator = id_generator_instance
        self.logger.info(f"NetworkGenerator initialized with template: {template_filepath} and IdGenerator.")
        # 设置logger级别为DEBUG，以便查看所有调试信息
        self.logger.setLevel(logging.DEBUG)

    def _get_raw_template_lines(self, start_line_1_idx: int, end_line_1_idx: int) -> list[str]:
        """Reads a range of lines (1-indexed) from the template file."""
        lines_to_return = []
        try:
            with open(self.template_filepath, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
            
            # Adjust to 0-indexed for list slicing
            start_0_idx = max(0, start_line_1_idx - 1)
            # end_line_1_idx is inclusive, so for slicing, it becomes the exclusive upper bound
            end_0_idx = end_line_1_idx 
            
            if start_0_idx < len(all_lines):
                raw_lines = all_lines[start_0_idx:end_0_idx]
                
                # 在这里应用格式修正
                lines_to_return = self._fix_template_format(raw_lines)
                self.logger.debug(f"读取并修正了来自模板的 {len(lines_to_return)} 行: {start_line_1_idx}-{end_line_1_idx}")
            else:
                self.logger.warning(f"Template '{self.template_filepath}' start line {start_line_1_idx} is out of bounds (total lines: {len(all_lines)}).")
        except Exception as e:
            self.logger.error(f"Error reading template '{self.template_filepath}' lines {start_line_1_idx}-{end_line_1_idx}: {e}")
        return lines_to_return

    def _fix_template_format(self, lines: list[str]) -> list[str]:
        """修复模板文件中的XML格式问题，特别是Component和Address标签在同一行的情况"""
        result = []
        fixed_count = 0
        
        # 调试输出前10行看是否有问题
        debug_sample = min(10, len(lines))
        for i in range(debug_sample):
            if '<Component ' in lines[i] and '<Address ' in lines[i]:
                self.logger.warning(f"样本行{i}需要格式修复: {lines[i].strip()}")
        
        for line_idx, line in enumerate(lines):
            # 使用更健壮的方法检测格式问题
            if '<Component ' in line and '<Address ' in line:
                self.logger.debug(f"处理第{line_idx}行格式问题: {line.strip()}")
                
                # 更精确地提取缩进
                indent_match = re.match(r'^(\s*)', line)
                indent = indent_match.group(1) if indent_match else ''
                
                # 分隔Component和Address
                parts = []
                rest = line
                
                # 查找并处理所有Component标签
                while '<Component ' in rest:
                    comp_start = rest.find('<Component ')
                    if comp_start > 0:
                        parts.append(rest[:comp_start])
                        rest = rest[comp_start:]
                    
                    comp_end = rest.find('/>', comp_start)
                    if comp_end > 0:
                        comp_tag = rest[:comp_end+2]  # 包含"/>结束标记
                        parts.append(comp_tag)
                        rest = rest[comp_end+2:]
                    else:
                        break
                
                # 查找并处理所有Address标签
                new_rest = ''
                current_rest = rest
                while '<Address ' in current_rest:
                    addr_start = current_rest.find('<Address ')
                    if addr_start > 0:
                        new_rest += current_rest[:addr_start]
                        current_rest = current_rest[addr_start:]
                    
                    addr_end = current_rest.find('/>', addr_start)
                    if addr_end > 0:
                        addr_tag = current_rest[:addr_end+2]  # 包含"/>结束标记
                        parts.append(indent + addr_tag)  # 为Address标签添加缩进
                        current_rest = current_rest[addr_end+2:]
                    else:
                        new_rest += current_rest
                        current_rest = ''
                        break
                
                # 处理剩余部分
                if current_rest:
                    new_rest += current_rest
                if new_rest:
                    parts.append(indent + new_rest.strip())
                
                # 生成新行
                for part in parts:
                    if part.strip():  # 跳过空行
                        result.append(part)
                
                fixed_count += 1
                continue
            
            # 仅检查行末是否有与</Symbol>标签挤在一起的Address标签
            if '</Symbol>' in line and '<Address ' in line and line.find('<Address ') < line.find('</Symbol>'):
                self.logger.debug(f"处理第{line_idx}行Address与Symbol标签问题: {line.strip()}")
                
                # 提取缩进
                indent_match = re.match(r'^(\s*)', line)
                indent = indent_match.group(1) if indent_match else ''
                
                # 查找Address标签结束位置
                addr_start = line.find('<Address ')
                addr_end = line.find('/>', addr_start)
                
                if addr_end > 0:
                    before_addr = line[:addr_start]
                    addr_part = line[addr_start:addr_end+2]
                    after_addr = line[addr_end+2:]
                    
                    # 如果Address前有内容，添加
                    if before_addr.strip():
                        result.append(before_addr)
                    
                    # 添加Address行（保持缩进）
                    result.append(indent + addr_part)
                    
                    # 如果Address后有内容，添加
                    if after_addr.strip():
                        result.append(indent + after_addr)
                    
                    fixed_count += 1
                    continue
            
            # 没有需要修复的问题，保持原样
            result.append(line)
        
        self.logger.info(f"修复了{fixed_count}行格式问题")
        return result

    def _get_bit_offset(self, io_address: str) -> int:
        """
        Converts an I/O address string (e.g., "I132.0", "Q4.1") to a BitOffset.
        This is a simplified placeholder. True TIA Portal BitOffset calculation
        can be more complex depending on module configuration, start addresses, etc.
        Assumes: I/Q/M, then byte, then dot, then bit.
        Offset = Byte * 8 + Bit.
        Example: I132.0 -> 132 * 8 + 0 = 1056. Q4.1 -> 4 * 8 + 1 = 33.
        Returns -1 if address is invalid.
        """
        if not io_address or not isinstance(io_address, str):
            self.logger.warning(f"Invalid I/O address for bit offset calculation: {io_address}")
            return -1
        
        match = re.fullmatch(r'[IQM](\d+)\.(\d+)', io_address, re.IGNORECASE)
        if match:
            byte_part = int(match.group(1))
            bit_part = int(match.group(2))
            if 0 <= bit_part <= 7:
                offset = byte_part * 8 + bit_part
                self.logger.debug(f"Calculated BitOffset for {io_address}: {offset}")
                return offset
            else:
                self.logger.warning(f"Invalid bit part in I/O address {io_address}. Bit must be 0-7.")
                return -1
        else:
            self.logger.warning(f"Could not parse I/O address for BitOffset: {io_address}. Using default -1.")
            return -1 # Or raise an error

    def _xml_escape(self, text: str) -> str:
        """Escapes characters for XML content."""
        if not isinstance(text, str): return ""
        return text.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;").replace("\"", "&quot;").replace("'", "&apos;")

    def _modify_line_attribute(self, line: str, target_tag:str, attribute_name: str, new_value: str) -> tuple[str, bool]:
        """
        修改XML标签的属性值。支持更复杂的XML结构，如属性顺序不固定的情况。
        
        Args:
            line: 要修改的行内容
            target_tag: 目标XML标签名称
            attribute_name: 要修改的属性名称
            new_value: 新的属性值
            
        Returns:
            修改后的行内容和一个布尔值表示是否成功修改
        """
        # 构建更精确的正则表达式，处理属性可能在任意位置的情况
        escaped_tag = re.escape(target_tag)
        escaped_attr = re.escape(attribute_name)
        
        # 使用命名捕获组来简化替换
        pattern = rf'(<{escaped_tag}[^>]*)({escaped_attr}\s*=\s*")([^"]+)("[^>]*>)'
        
        # 记录匹配前的调试信息
        self.logger.debug(f"尝试匹配标签 '{target_tag}' 的属性 '{attribute_name}'")
        self.logger.debug(f"原始行: {line.strip()}")
        
        escaped_new_value = self._xml_escape(new_value)
        match = re.search(pattern, line)
        
        if match:
            # 组1:开始标签到属性名 组2:属性名=", 组3:属性值, 组4:"到>
            modified_line = f"{match.group(1)}{match.group(2)}{escaped_new_value}{match.group(4)}"
            self.logger.debug(f"成功匹配! 替换 '{match.group(3)}' 为 '{escaped_new_value}'")
            return modified_line, True
        else:
            self.logger.debug(f"未匹配到目标属性模式: {pattern}")
            return line, False

    def _modify_line_text_content(self, line: str, target_text_tag:str, new_text: str) -> tuple[str, bool]:
        pattern = rf'(<{target_text_tag}>)[^<]*(</{target_text_tag}>)'
        escaped_new_text = self._xml_escape(new_text)
        modified_line, count = re.subn(pattern, rf'\g<1>{escaped_new_text}\g<2>', line)
        return modified_line, count > 0

    def _normalize_xml_format(self, lines: list[str]) -> list[str]:
        """
        单独的函数来规范化XML格式，确保Component和Address标签在不同行，并保持正确缩进。
        """
        self.logger.info("开始规范化XML格式...")
        result = []
        fixed_lines_count = 0
        
        for i, line in enumerate(lines):
            line_number = i + 1
            # 确定当前行的缩进
            match_indent = re.match(r'^(\s*)', line)
            line_indent = match_indent.group(1) if match_indent else ''
            
            # 检查行中是否同时包含Component和Address
            if '<Component ' in line and '<Address ' in line:
                # 先尝试直接分割
                comp_pos = line.find('<Component ')
                addr_pos = line.find('<Address ')
                
                if comp_pos >= 0 and addr_pos > comp_pos:
                    # 找到Component标签结束位置
                    # 查找/>结束标记
                    comp_end = line.find('/>', comp_pos)
                    if comp_end > 0 and comp_end < addr_pos:
                        comp_end += 2  # 包含/>
                        # 分割为两行
                        comp_line = line[:comp_end]
                        addr_line = line_indent + line[addr_pos:]
                        
                        # 清理可能的额外内容
                        if '/>' in addr_line and '</Symbol>' in addr_line:
                            # 如果Address后面还有Symbol结束标记，也分割出来
                            addr_end = addr_line.find('/>') + 2
                            symbol_close = addr_line[addr_end:].strip()
                            addr_line = addr_line[:addr_end]
                            
                            result.append(comp_line)
                            result.append(addr_line)
                            if symbol_close:  # 如果有Symbol结束标记，单独一行
                                result.append(line_indent + symbol_close)
                        else:
                            result.append(comp_line)
                            result.append(addr_line)
                        
                        fixed_lines_count += 1
                        self.logger.debug(f"修复了第{line_number}行: 分割Component和Address为不同行")
                        continue
            
            # 特殊处理: 检查行尾是否有额外的Symbol结束标记
            addr_end = line.find('/>', line.find('<Address '))
            if addr_end > 0 and '</Symbol>' in line[addr_end+2:]:
                # 分离Address和Symbol结束标记
                split_pos = addr_end + 2
                addr_part = line[:split_pos]
                symbol_close = line_indent + line[split_pos:].strip()
                
                result.append(addr_part)
                if symbol_close.strip():
                    result.append(symbol_close)
                
                fixed_lines_count += 1
                self.logger.debug(f"修复了第{line_number}行: 分离Address和Symbol结束标记")
                continue
            
            # 如果没有进行格式修复，保留原行
            result.append(line)
        
        # 执行直接搜索替换，处理可能的顽固格式
        final_result = []
        pattern_fixed = 0
        
        for line in result:
            # 查找特定模式: <Component ... /><Address ... />
            pattern = r'(<Component [^>]*/>)(<Address [^>]*/>)'
            if re.search(pattern, line):
                indent = re.match(r'^(\s*)', line).group(1) if re.match(r'^(\s*)', line) else ''
                parts = re.split(pattern, line)
                if len(parts) >= 4:  # 分割成了: [前导内容, Component标签, Address标签, 后续内容]
                    before = parts[0]
                    comp_tag = parts[1]
                    addr_tag = parts[2]
                    after = ''.join(parts[3:])
                    
                    if before:
                        final_result.append(before)
                    final_result.append(indent + comp_tag)
                    final_result.append(indent + addr_tag)
                    if after:
                        final_result.append(indent + after)
                    
                    pattern_fixed += 1
                    continue
                
            final_result.append(line)
        
        if pattern_fixed > 0:
            fixed_lines_count += pattern_fixed
            self.logger.debug(f"使用正则表达式替换修复了{pattern_fixed}处Component和Address同行的问题")
        
        self.logger.info(f"XML格式规范化完成，共修复了{fixed_lines_count}处格式问题。")
        return final_result

    def _modify_network_parameters(self, network_lines: list[str], instance_data: dict, op_mode_data: dict, 
                                 is_single_input_template: bool = True, is_last_instance_footer: bool = False) -> list[str]:
        modified_block = list(network_lines) # Work on a copy
        inst_name_val = instance_data.get('name', 'DefaultInstName') # Get the cleaned value from instance_data
        self.logger.debug(f"Starting _modify_network_parameters for instance: {inst_name_val}, is_last_footer: {is_last_instance_footer}")

        if is_last_instance_footer:
            # Special handling for the replacement block (lines 6967-7023 from template)
            # This block mainly needs the Network Title within its own CompileUnit's ObjectList updated.
            # Template shows <MultilingualText ID="47" CompositionName="Title"> ... <Text>左缓存料盒搬运夹爪气缸</Text>
            # This text needs to become the current (last) instance's name.
            in_target_title_mltext = False
            target_title_uid = "47" # Based on provided snippet for 6967-7023
            target_title_text_uid = "48"

            for i, line in enumerate(modified_block):
                if f'<MultilingualText ID="{target_title_uid}"' in line and 'CompositionName="Title"' in line:
                    in_target_title_mltext = True
                    self.logger.debug(f"  Footer: Found target Title MLText (ID {target_title_uid}) start line.")
                    continue
                if in_target_title_mltext and f'<MultilingualTextItem ID="{target_title_text_uid}"' in line:
                    # The next line should contain <Culture>zh-CN</Culture>
                    # The line after that should contain <Text>...</Text>
                    if i + 2 < len(modified_block) and "<Text>" in modified_block[i+2]:
                        modified_line, success = self._modify_line_text_content(modified_block[i+2], "Text", inst_name_val)
                        if success:
                            modified_block[i+2] = modified_line
                            self.logger.info(f"  Footer: Patched last instance title (ID {target_title_text_uid}) to: {inst_name_val}")
                        in_target_title_mltext = False # Done with this title block
                        break # Assuming only one such title to patch in the footer
                if '</MultilingualText>' in line and in_target_title_mltext:
                     in_target_title_mltext = False # End of the current MLText block
            
            # 应用格式规范化
            return self._normalize_xml_format(modified_block)

        # Regular network parameter modification (not the last instance footer)
        param_map = {} # Define param_map_single_input, param_map_double_input here or pass them in
        if is_single_input_template:
            param_map = {
                "21": { "comp_name_desc": instance_data.get('forward_sensor_desc'), "addr_offset": self._get_bit_offset(instance_data.get('forward_sensor_addr')) },
                "22": { "comp_name_desc": instance_data.get('backward_sensor_desc'), "addr_offset": self._get_bit_offset(instance_data.get('backward_sensor_addr')) },
                "23": { "db_inst_name_idx3": inst_name_val },
                "24": { "db_inst_name_idx3": inst_name_val },
                "37": { "comp_name_desc": instance_data.get('forward_output_desc'), "addr_offset": self._get_bit_offset(instance_data.get('forward_output_addr')) },
                "38": { "comp_name_desc": instance_data.get('backward_output_desc'), "addr_offset": self._get_bit_offset(instance_data.get('backward_output_addr')) },
                "39": { "db_inst_name_idx3": inst_name_val }, 
                "40": { "db_inst_name_idx3": inst_name_val }, 
                "41": { "call_inst_name": inst_name_val } 
            }
        else: # Double input template (placeholders, UIds will be different)
            self.logger.info(f"Using parameter map for double-input template for {inst_name_val}")
            # Hypothetical UIDs for double-input template (lines 5625-6099 in template2.xml)
            # These UIDs (D21, D22, D21S, etc.) must match the actual UIDs in that section of template2.xml
            param_map = {
                # Primary sensors
                "D21": { "comp_name_desc": instance_data.get('forward_sensor_desc'), "addr_offset": self._get_bit_offset(instance_data.get('forward_sensor_addr')) },
                "D22": { "comp_name_desc": instance_data.get('backward_sensor_desc'), "addr_offset": self._get_bit_offset(instance_data.get('backward_sensor_addr')) },
                # Secondary sensors
                "D21S": { "comp_name_desc": instance_data.get('forward_sensor2_desc'), "addr_offset": self._get_bit_offset(instance_data.get('forward_sensor2_addr')) },
                "D22S": { "comp_name_desc": instance_data.get('backward_sensor2_desc'), "addr_offset": self._get_bit_offset(instance_data.get('backward_sensor2_addr')) },
                
                # Outputs (assuming similar UIDs but prefixed for clarity, or they might be different)
                "D37": { "comp_name_desc": instance_data.get('forward_output_desc'), "addr_offset": self._get_bit_offset(instance_data.get('forward_output_addr')) },
                "D38": { "comp_name_desc": instance_data.get('backward_output_desc'), "addr_offset": self._get_bit_offset(instance_data.get('backward_output_addr')) },
                
                # DB Instance Name accesses (assuming 3rd component is instance name)
                # These UIDs are also hypothetical
                "D23_DB": { "db_inst_name_idx3": inst_name_val },
                "D24_DB": { "db_inst_name_idx3": inst_name_val },
                "D39_DB": { "db_inst_name_idx3": inst_name_val },
                "D40_DB": { "db_inst_name_idx3": inst_name_val },
                
                # Call instance name
                "D41_CALL": { "call_inst_name": inst_name_val }
            }
            if not instance_data.get('forward_sensor2_desc'): # Log if secondary inputs are missing, as map expects them
                self.logger.warning(f"Instance {inst_name_val} is double-input type, but secondary sensor descriptions/addresses might be missing in instance_data.")

        active_uid_block = None
        active_call_uid = None
        component_count_in_symbol = 0
        in_network_title_objectlist = False
        network_title_text_uid_zh = None # e.g. "17" for single input network 1 title
        
        for i, line in enumerate(modified_block):
            uid_access_match = re.search(r'<Access Scope=\"[^\"]+\" UId=\"([^\"]+)\">', line)
            uid_call_match = re.search(r'<Call UId=\"([^\"]+)\">', line)
            
            if uid_access_match:
                active_uid_block = uid_access_match.group(1)
                active_call_uid = None # Reset call context
                component_count_in_symbol = 0
            elif uid_call_match:
                active_call_uid = uid_call_match.group(1)
                active_uid_block = None # Reset access context

            if active_uid_block and active_uid_block in param_map:
                uid_params = param_map[active_uid_block]
                if "<Symbol>" in line: component_count_in_symbol = 0
                if "</Symbol>" in line: component_count_in_symbol = -1

                if "<Component Name=" in line and component_count_in_symbol != -1:
                    component_count_in_symbol += 1
                    
                    if uid_params.get("comp_name_desc") and component_count_in_symbol == 1: # Assume desc is 1st component
                        modified_line, success = self._modify_line_attribute(line, "Component", "Name", uid_params["comp_name_desc"])
                        if success: modified_block[i] = modified_line
                    elif uid_params.get("db_inst_name_idx3") and component_count_in_symbol == 3:
                        modified_line, success = self._modify_line_attribute(line, "Component", "Name", uid_params["db_inst_name_idx3"])
                        if success: modified_block[i] = modified_line
                
                if uid_params.get("addr_offset") is not None and "BitOffset=" in line:
                    modified_line, success = self._modify_line_attribute(line, "Address", "BitOffset", str(uid_params["addr_offset"]))
                    if success: modified_block[i] = modified_line
            
            elif active_call_uid and active_call_uid in param_map:
                uid_params = param_map[active_call_uid]
                if uid_params.get("call_inst_name") and "<Instance Scope=\"LocalVariable\"" in line: # Look for the instance line
                    # The next line should be <Component Name=...>
                    if i + 1 < len(modified_block) and "<Component Name=" in modified_block[i+1]:
                        modified_line, success = self._modify_line_attribute(modified_block[i+1], "Component", "Name", uid_params["call_inst_name"])
                        if success: modified_block[i+1] = modified_line
            
            # Network title section for regular networks (not the last_instance_footer special case)
            if '<MultilingualText' in line and 'CompositionName="Title"' in line :
                # Try to capture the ID of the MultilingualTextItem for zh-CN
                # Example: <MultilingualText ID="16" CompositionName="Title"> ... <MultilingualTextItem ID="17" ... <Culture>zh-CN</Culture> <Text>...</Text>
                # This is a heuristic. A more robust way would be to ensure template has specific placeholders or fixed IDs.
                in_network_title_objectlist = True 
                # We need to find the ID of the item that has zh-CN text, e.g. "17"
                # For now, we'll assume the first zh-CN text under a Title MLT is the one.
            if in_network_title_objectlist and "<Culture>zh-CN</Culture>" in line:
                if i + 1 < len(modified_block) and "<Text>" in modified_block[i+1]:
                    modified_line, success = self._modify_line_text_content(modified_block[i+1], "Text", inst_name_val)
                    if success:
                        modified_block[i+1] = modified_line
                        self.logger.debug(f"  Regular NW: Patched network title to: {inst_name_val}")
                    in_network_title_objectlist = False # Modified, so reset
            if "</MultilingualText>" in line and in_network_title_objectlist and 'CompositionName="Title"' in line:
                in_network_title_objectlist = False # End of this title block

            if "</Access>" in line: active_uid_block = None
            if "</CallInfo>" in line: active_call_uid = None # End of Call block where instance name is
        
        # 应用格式规范化
        return self._normalize_xml_format(modified_block)

    def _regenerate_object_ids(self, network_lines: list[str]) -> list[str]:
        """
        重新生成CompileUnit及其内部所有对象的ID，按顺序编号
        """
        self.logger.info("[ID_REGEN] 开始为新实例块重新生成ID。")
        processed_lines = list(network_lines)
        
        # Step 1: 提取所有需要替换的ID及其标签名称
        id_mappings = {} # 格式: {旧ID: (标签名, 新ID)}
        regen_count = 0  # ID替换计数器
        
        # 先找到所有含有ID属性的XML标签
        for i, line in enumerate(processed_lines):
            line_trimmed = line.strip()
            
            # 使用正则表达式匹配所有带有ID属性的XML标签
            # 格式: <TagName ...ID="XXX"...>
            matches = re.finditer(r'<(\w+(?:\.\w+)*)[^>]*\s+ID="([^"]+)"[^>]*>', line)
            
            for match in matches:
                tag_name = match.group(1)
                old_id = match.group(2)
                
                # 避免重复处理同一个ID
                if old_id not in id_mappings:
                    # 获取下一个顺序ID
                    new_id = self.id_generator.get_next_id()
                    id_mappings[old_id] = (tag_name, new_id)
                    regen_count += 1
                    self.logger.debug(f"[ID_REGEN] 找到标签 <{tag_name}> 的ID='{old_id}', 将替换为 '{new_id}'")
        
        self.logger.info(f"[ID_REGEN] 找到 {regen_count} 个唯一ID需要替换。")
        
        if regen_count == 0:
            self.logger.warning("[ID_REGEN] 警告: 未在模板中找到任何ID属性!")
            return processed_lines
            
        # Step 2: 执行替换
        replacement_count = 0
        for i, line in enumerate(processed_lines):
            line_trimmed = line.strip()
            modified_line = line
            
            # 检查这一行是否包含需要替换的ID
            for old_id, (tag_name, new_id) in id_mappings.items():
                # 构建一个精确的正则表达式来匹配ID属性
                # 格式: ID="旧ID"
                pattern = r'ID="' + re.escape(old_id) + r'"'
                
                if re.search(pattern, modified_line):
                    # 替换ID
                    modified_line = re.sub(pattern, f'ID="{new_id}"', modified_line)
                    replacement_count += 1
                    self.logger.info(f"[ID_REGEN] 行{i+1}: 将 <{tag_name}> 的ID从 '{old_id}' 改为 '{new_id}'")
            
            if modified_line != line:
                processed_lines[i] = modified_line
            
        self.logger.info(f"[ID_REGEN] 完成ID重生成，共执行 {replacement_count} 次替换。")
        
        # 验证替换的ID数量与找到的ID数量是否相符
        if replacement_count < regen_count:
            self.logger.warning(f"[ID_REGEN] 警告: 只替换了 {replacement_count} 个ID，但找到了 {regen_count} 个需要替换的ID。")
        
        return processed_lines

    def generate_fixed_networks_part(self) -> list[str]:
        """Generates the fixed network part (HMI display, mode) as per Network.md rule 1."""
        self.logger.info("Generating fixed network part (template lines 3077-3459).")
        # Network.md: "直接复制模板xml中3077-3459行代码"
        return self._get_raw_template_lines(3077, 3459)

    def generate_instance_network(self, instance_data: dict, op_mode_data: dict, is_last_instance: bool = False) -> list[str]:
        """Generates network XML lines for a single instance."""
        inst_name = instance_data.get('name', 'UnknownInstance')
        self.logger.info(f"Generating network for instance: {inst_name}, is_last: {is_last_instance}")

        network_template_lines = []
        is_double_input = instance_data.get('is_double_input_type', False)
        if is_double_input:
            self.logger.debug(f"  Instance {inst_name} is double-input type. Using template lines 5625-6099.")
            network_template_lines = self._get_raw_template_lines(5625, 6099)
        else:
            self.logger.debug(f"  Instance {inst_name} is single-input type. Using template lines 3460-3904 (was 3460-3892).")
            # Network.md rule was 3460-3892. Extending to 3904 to include full ObjectList for ID regeneration.
            network_template_lines = self._get_raw_template_lines(3460, 3904) 

        if not network_template_lines:
            self.logger.error(f"  Could not read template lines for instance {inst_name}.")
            return []

        # Regenerate Object IDs first, as per new requirement
        self.logger.debug(f"  Regenerating Object IDs for instance {inst_name} network template.")
        processed_network_template_lines = self._regenerate_object_ids(copy.deepcopy(network_template_lines))

        # Modify parameters based on Excel data
        modified_network_lines = self._modify_network_parameters(
            processed_network_template_lines, # Use lines with regenerated IDs
            instance_data, 
            op_mode_data, 
            is_single_input_template=not is_double_input,
            is_last_instance_footer=False
        )
        
        if is_last_instance:
            self.logger.info(f"  Applying special rules for last instance: {inst_name}")
            # Network.md rule 4:
            # "针对最后一个实例程序，需要把这个实例程序的最后一段内容，从</FlgNet></NetworkSource>行开始，
            # 到最后一行：<NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">结束，
            # 总共41行，整体替换成模板xml中6967-7023行的内容；替换完之后，再改写对应的参数。"

            # Find the start of the section to replace.
            # The rule says "从</FlgNet></NetworkSource>行开始".
            # A typical CompileUnit ends like:
            #   </Wires>
            # </FlgNet></NetworkSource>  <-- This line
            #   <ProgrammingLanguage>LAD</ProgrammingLanguage>
            # </AttributeList>
            # <ObjectList> ... </ObjectList>
            # </SW.Blocks.CompileUnit>
            
            # The rule also mentions "到最后一行：<NetworkSource><FlgNet ...>结束". This phrasing is a bit confusing.
            # It seems to imply replacing the end of one CompileUnit with the start of another, or a specific footer.
            # The "总共41行" and "模板xml中6967-7023行的内容" (which is 57 lines, not 41) is key.
            
            # Let's assume the intention is to replace the *tail* of the *current* last instance's generated code
            # with a *specific block* from the template (lines 6967-7020).

            # Find "</FlgNet></NetworkSource>" in the *current* modified_network_lines.
            # This is tricky because the _modify_network_parameters might change line content.
            # For now, let's assume it's a relatively stable marker.
            
            replacement_section_start_marker = "</FlgNet></NetworkSource>"
            replacement_point_idx = -1
            for i, line in reversed(list(enumerate(modified_network_lines))):
                if replacement_section_start_marker in line:
                    replacement_point_idx = i
                    break
            
            if replacement_point_idx != -1:
                # Get the lines to insert from template (6967-7020, was 6967-7023)
                # Exclude the final </ObjectList>, </SW.Blocks.FB>, </Document> as they are added by the main generator
                replacement_lines = self._get_raw_template_lines(6967, 7020) 
                if replacement_lines:
                    # The rule says "从</FlgNet></NetworkSource>行开始...整体替换成模板xml中6967-7023行的内容"
                    # This implies the part of modified_network_lines from replacement_point_idx onwards is replaced.
                    
                    # Apply parameter modification to the replacement lines if necessary
                    # Based on the previous structure, the title for the last instance is within these lines (UIDs 47, 48)
                    self.logger.debug(f"  Modifying parameters for the replacement footer block (template lines 6967-7020).")
                    processed_replacement_lines = self._modify_network_parameters(
                        copy.deepcopy(replacement_lines), 
                        instance_data, 
                        op_mode_data, 
                        is_single_input_template=False, # This might need to be dynamic if footer structure varies
                        is_last_instance_footer=True # Signal that this is the special footer block
                    )
                    
                    self.logger.debug(f"  Replacing from line {replacement_point_idx} (marker: {replacement_section_start_marker}) onwards with processed template lines 6967-7020.")
                    modified_network_lines = modified_network_lines[:replacement_point_idx] + processed_replacement_lines
                else:
                    self.logger.error(f"  Could not read replacement template lines 6967-7020 for last instance.")
            else:
                self.logger.error(f"  Could not find marker '{replacement_section_start_marker}' in last instance network to apply special replacement.")

        return modified_network_lines

    def generate_all_networks(self, instances: list, op_mode_data: dict) -> list[str]:
        """Generates all network sections: fixed part and all instance networks."""
        all_network_lines = []

        # 1. Add HMI display, mode fixed content
        fixed_part_lines = self.generate_fixed_networks_part()
        if fixed_part_lines:
            all_network_lines.extend(fixed_part_lines)
            self.logger.info(f"Added {len(fixed_part_lines)} lines for fixed HMI/mode network part.")
        else:
            self.logger.warning("Fixed HMI/mode network part is empty.")

        # 2. Add instance programs
        num_instances = len(instances)
        for i, instance_data in enumerate(instances):
            is_last = (i == num_instances - 1)
            instance_network_lines = self.generate_instance_network(instance_data, op_mode_data, is_last_instance=is_last)
            if instance_network_lines:
                all_network_lines.extend(instance_network_lines)
                self.logger.info(f"Added {len(instance_network_lines)} lines for instance '{instance_data.get('name')}'.")
            else:
                self.logger.warning(f"Generated network for instance '{instance_data.get('name')}' is empty.")
        
        self.logger.info(f"Total network lines generated: {len(all_network_lines)}")
        return all_network_lines

    def fix_xml_format(self, input_file, output_file=None):
        """
        修复XML文件中的格式问题，特别是Component和Address标签在同一行的情况。
        可以作为单独的后处理步骤使用。
        
        Args:
            input_file: 输入XML文件路径
            output_file: 输出XML文件路径，默认覆盖输入文件
        """
        if output_file is None:
            output_file = input_file
        
        self.logger.info(f"正在修复XML格式问题: {input_file} -> {output_file}")
        
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        result_lines = []
        fixed_count = 0
        line_count = 0
        
        # 处理每一行
        for line in lines:
            line_count += 1
            # 检查是否有Component和Address在同一行的情况
            if '<Component' in line and '<Address' in line:
                # 提取缩进
                indent_match = re.match(r'^(\s*)', line)
                indent = indent_match.group(1) if indent_match else ''
                
                # 分解行
                component_start = line.find('<Component')
                address_start = line.find('<Address')
                
                if component_start >= 0 and address_start > component_start:
                    # 找到Component标签结束位置
                    component_end = line.find('/>', component_start)
                    
                    if component_end > 0 and component_end < address_start:
                        # 将行分成两部分
                        component_part = line[:component_end+2]  # 包含"/>结束标记
                        address_part = indent + line[address_start:]
                        
                        # 如果Address后有Symbol结束标记，需要再单独处理
                        symbol_end_idx = address_part.find('</Symbol>')
                        address_end_idx = address_part.find('/>')
                        
                        if symbol_end_idx > 0 and address_end_idx > 0 and address_end_idx < symbol_end_idx:
                            # 分成三部分
                            addr_close_idx = address_end_idx + 2  # 包含"/>结束标记
                            symbol_part = indent + address_part[addr_close_idx:].strip()
                            address_part = address_part[:addr_close_idx]
                            
                            # 添加分解后的行
                            result_lines.append(component_part)
                            result_lines.append(address_part)
                            result_lines.append(symbol_part)
                        else:
                            # 只分成两部分
                            result_lines.append(component_part)
                            result_lines.append(address_part)
                        
                        fixed_count += 1
                        continue
            
            # 如果没有特殊情况，原样保留行
            result_lines.append(line)
        
        # 写入结果到输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(result_lines)
        
        self.logger.info(f"XML格式修复完成，共处理了{line_count}行，修复了{fixed_count}处格式问题")
        return fixed_count

if __name__ == '__main__':
    # Minimal IdGenerator for standalone testing, defined inside the test block
    class IdGenerator:
        def __init__(self, start_id=0x19):
            self.current_id = start_id
            # Minimal logger for testing, or pass None/mock it
            self.logger = logging.getLogger('TestIdGen') 
            if not self.logger.handlers:
                self.logger.addHandler(logging.NullHandler()) # Avoid "No handlers" warning if not configured

        def get_next_id(self) -> str:
            next_id_val = self.current_id
            self.current_id += 1
            return hex(next_id_val)[2:].upper()
        
        def reset(self, start_id=0x19):
            self.current_id = start_id
            self.logger.info(f"TestIdGen reset to {hex(start_id)[2:].upper()}")

    # Example Usage (for testing NetworkGenerator independently)
    logger = logging.getLogger('TestNetworkGen')
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Mock data similar to what xml_generator_new.py would provide
    mock_op_mode = {'fb_name': 'FB_TestMain', 'op_mode_name': 'TestModeOpName'}
    mock_instances_data = [
        {
            'name': "Cylinder_One", 'comment': "First cylinder", 'is_double_input_type': False,
            'forward_sensor_desc': "I0.0 Sensor Fwd1", 'forward_sensor_addr': "I0.0",
            'backward_sensor_desc': "I0.1 Sensor Bwd1", 'backward_sensor_addr': "I0.1",
            'forward_output_desc': "Q0.0 Output Fwd1", 'forward_output_addr': "Q0.0",
            'backward_output_desc': "Q0.1 Output Bwd1", 'backward_output_addr': "Q0.1",
        },
        {
            'name': "Valve_Two_Double", 'comment': "Second valve, double",'is_double_input_type': True, # For testing double (map not defined yet)
            'forward_sensor_desc': "I1.0 Sensor Fwd2A", 'forward_sensor_addr': "I1.0",
            'backward_sensor_desc': "I1.1 Sensor Bwd2A", 'backward_sensor_addr': "I1.1",
            'forward_sensor2_desc': "I1.2 Sensor Fwd2B", 'forward_sensor2_addr': "I1.2",
            'backward_sensor2_desc': "I1.3 Sensor Bwd2B", 'backward_sensor_addr': "I1.3",
            'forward_output_desc': "Q1.0 Output Fwd2", 'forward_output_addr': "Q1.0",
            'backward_output_desc': "Q1.1 Output Bwd2", 'backward_output_addr': "Q1.1",
        },
         {
            'name': "Cylinder_Three_Last", 'comment': "Third cylinder, last", 'is_double_input_type': False,
            'forward_sensor_desc': "I2.0 Sensor Fwd3", 'forward_sensor_addr': "I2.0",
            'backward_sensor_desc': "I2.1 Sensor Bwd3", 'backward_sensor_addr': "I2.1",
            'forward_output_desc': "Q2.0 Output Fwd3", 'forward_output_addr': "Q2.0",
            'backward_output_desc': "Q2.1 Output Bwd3", 'backward_output_addr': "Q2.1",
        }
    ]
    
    # Create a dummy template.xml for testing _get_raw_template_lines
    # In a real scenario, this would be template2.xml
    dummy_template_file = "dummy_template_for_network_test.xml"
    with open(dummy_template_file, 'w', encoding='utf-8') as dtf:
        # Fixed network part (3077-3459)
        for i in range(3077, 3459 + 1): dtf.write(f"<FixedNetLine num='{i}'>Fixed content line {i}</FixedNetLine>\n")
        # Single input template (3460-3892) - based on Network 1 structure (UIDs 21-41 for parts, then ObjectList for title)
        dtf.write(f'<SW.Blocks.CompileUnit ID="DUMMY_COMP_UNIT_1" CompositionName="CompileUnits">\n')
        dtf.write(f'  <AttributeList><NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">\n')
        dtf.write(f'    <Parts>\n')
        dtf.write(f'      <Access Scope="GlobalVariable" UId="21"><Symbol><Component Name="TEMPLATE_I_FWD_DESC" /><Address Area="Input" Type="Bool" BitOffset="0" Informative="true" /></Symbol></Access>\n')
        dtf.write(f'      <Access Scope="GlobalVariable" UId="22"><Symbol><Component Name="TEMPLATE_I_BWD_DESC" /><Address Area="Input" Type="Bool" BitOffset="0" Informative="true" /></Symbol></Access>\n')
        # Simplified DB access, assuming instance name is 3rd component
        for uid_db in ["23", "24", "39", "40"]:
            dtf.write(f'      <Access Scope="GlobalVariable" UId="{uid_db}"><Symbol><Component Name="DB_ControlNodesC5" /><Component Name="em12" /><Component Name="DB_INSTANCE_NAME_PLACEHOLDER" /><Component Name="control" /><Component Name="someParam" /><Address Area="None" Type="Bool" BlockNumber="62" BitOffset="0" Informative="true" /></Symbol></Access>\n')
        dtf.write(f'      <Access Scope="GlobalVariable" UId="37"><Symbol><Component Name="TEMPLATE_Q_FWD_DESC" /><Address Area="Output" Type="Bool" BitOffset="0" Informative="true" /></Symbol></Access>\n')
        dtf.write(f'      <Access Scope="GlobalVariable" UId="38"><Symbol><Component Name="TEMPLATE_Q_BWD_DESC" /><Address Area="Output" Type="Bool" BitOffset="0" Informative="true" /></Symbol></Access>\n')
        dtf.write(f'      <Call UId="41"><CallInfo Name="FB4000_Valve"><Instance Scope="LocalVariable" UId="42"><Component Name="CALL_INSTANCE_NAME_PLACEHOLDER" /></Instance></CallInfo></Call>\n')
        dtf.write(f'    </Parts><Wires></Wires>\n') # Simplified Wires
        dtf.write(f'  </FlgNet></NetworkSource>\n') # Line 3892 as per rule if it means end of FlgNet
        dtf.write(f'  <ProgrammingLanguage>LAD</ProgrammingLanguage></AttributeList>\n')
        dtf.write(f'  <ObjectList>\n')
        dtf.write(f'    <MultilingualText ID="DUMMY_TITLE_MLT" CompositionName="Title"><ObjectList>\n')
        dtf.write(f'      <MultilingualTextItem ID="DUMMY_TITLE_MLT_ITEM_ZH" CompositionName="Items"><AttributeList><Culture>zh-CN</Culture><Text>TEMPLATE_NETWORK_TITLE_ZH</Text></AttributeList></MultilingualTextItem>\n')
        dtf.write(f'    </ObjectList></MultilingualText>\n')
        dtf.write(f'  </ObjectList>\n')
        dtf.write(f'</SW.Blocks.CompileUnit>\n') # Approx line 3904 for a full unit

        # Double input template (5625-6099) - basic placeholder
        # Enhanced to include hypothetical UIDs for testing the double-input param_map
        dtf.write(f'<SW.Blocks.CompileUnit ID="DUMMY_COMP_UNIT_DOUBLE" CompositionName="CompileUnits">\n')
        dtf.write(f'  <AttributeList><NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">\n')
        dtf.write(f'    <Parts>\n')
        dtf.write(f'      <Access Scope="GlobalVariable" UId="D21"><Symbol><Component Name="TEMPLATE_I_FWD1_DESC_D" /><Address Area="Input" Type="Bool" BitOffset="0" Informative="true" /></Symbol></Access>\n')
        dtf.write(f'      <Access Scope="GlobalVariable" UId="D22"><Symbol><Component Name="TEMPLATE_I_BWD1_DESC_D" /><Address Area="Input" Type="Bool" BitOffset="0" Informative="true" /></Symbol></Access>\n')
        dtf.write(f'      <Access Scope="GlobalVariable" UId="D21S"><Symbol><Component Name="TEMPLATE_I_FWD2_DESC_D" /><Address Area="Input" Type="Bool" BitOffset="0" Informative="true" /></Symbol></Access>\n')
        dtf.write(f'      <Access Scope="GlobalVariable" UId="D22S"><Symbol><Component Name="TEMPLATE_I_BWD2_DESC_D" /><Address Area="Input" Type="Bool" BitOffset="0" Informative="true" /></Symbol></Access>\n')
        
        for uid_db in ["D23_DB", "D24_DB", "D39_DB", "D40_DB"]:
            dtf.write(f'      <Access Scope="GlobalVariable" UId="{uid_db}"><Symbol><Component Name="DB_Ctrl_D" /><Component Name="em_D" /><Component Name="DB_INST_NAME_PLACEHOLDER_D" /><Component Name="ctrl_D" /><Component Name="param_D" /><Address Area="None" Type="Bool" BlockNumber="63" BitOffset="0" Informative="true" /></Symbol></Access>\n')
        
        dtf.write(f'      <Access Scope="GlobalVariable" UId="D37"><Symbol><Component Name="TEMPLATE_Q_FWD_DESC_D" /><Address Area="Output" Type="Bool" BitOffset="0" Informative="true" /></Symbol></Access>\n')
        dtf.write(f'      <Access Scope="GlobalVariable" UId="D38"><Symbol><Component Name="TEMPLATE_Q_BWD_DESC_D" /><Address Area="Output" Type="Bool" BitOffset="0" Informative="true" /></Symbol></Access>\n')
        dtf.write(f'      <Call UId="D41_CALL"><CallInfo Name="FB_VALVE_DOUBLE"><Instance Scope="LocalVariable" UId="D42_CALL_ITEM"><Component Name="CALL_INSTANCE_NAME_PLACEHOLDER_D" /></Instance></CallInfo></Call>\n')
        dtf.write(f'    </Parts><Wires></Wires>\n')
        dtf.write(f'  </FlgNet></NetworkSource>\n')
        dtf.write(f'  <ProgrammingLanguage>LAD</ProgrammingLanguage></AttributeList>\n')
        dtf.write(f'  <ObjectList>\n')
        dtf.write(f'    <MultilingualText ID="DUMMY_TITLE_MLT_D" CompositionName="Title"><ObjectList>\n')
        dtf.write(f'      <MultilingualTextItem ID="DUMMY_TITLE_MLT_ITEM_ZH_D" CompositionName="Items"><AttributeList><Culture>zh-CN</Culture><Text>TEMPLATE_NETWORK_TITLE_ZH_D</Text></AttributeList></MultilingualTextItem>\n')
        dtf.write(f'    </ObjectList></MultilingualText>\n')
        dtf.write(f'  </ObjectList>\n')
        dtf.write(f'</SW.Blocks.CompileUnit>\n')
        # Fill remaining lines for the double input section if any (5625-6099)
        # The above UIDs are just examples, the actual template section might be much longer.
        # For simplicity, the dummy template only includes lines for these UIDs. A more complete dummy
        # would replicate the line count 5625-6099.
        
        # Last instance footer replacement (6967-7023)
        dtf.write(f'<ReplacedPartStartFLAG/>\n') # Just a marker for test
        dtf.write(f'</FlgNet></NetworkSource>\n') # line 6967
        dtf.write(f'          <ProgrammingLanguage>LAD</ProgrammingLanguage>\n')
        dtf.write(f'        </AttributeList>\n')
        dtf.write(f'        <ObjectList>\n')
        dtf.write(f'          <MultilingualText ID="IGNORE_COMMENT_ID" CompositionName="Comment"><ObjectList><MultilingualTextItem><AttributeList><Culture>zh-CN</Culture><Text></Text></AttributeList></MultilingualTextItem></ObjectList></MultilingualText>\n')
        dtf.write(f'          <MultilingualText ID="47" CompositionName="Title"><ObjectList>\n') # UID from template for last instance title
        dtf.write(f'              <MultilingualTextItem ID="48" CompositionName="Items"><AttributeList><Culture>zh-CN</Culture><Text>LAST_INSTANCE_TITLE_PLACEHOLDER</Text></AttributeList></MultilingualTextItem>\n')
        dtf.write(f'          </ObjectList></MultilingualText>\n')
        dtf.write(f'        </ObjectList>\n')
        dtf.write(f'      </SW.Blocks.CompileUnit>\n')
        dtf.write(f'      <MultilingualText ID="FB_TOTAL_TITLE_ID" CompositionName="Title"><ObjectList><MultilingualTextItem><AttributeList><Culture>zh-CN</Culture><Text>FB_OVERALL_TITLE_TEXT</Text></AttributeList></MultilingualTextItem></ObjectList></MultilingualText>\n')
        dtf.write(f'    </ObjectList>\n')
        dtf.write(f'  </SW.Blocks.FB>\n')
        dtf.write(f'</Document>\n') # line 7023

    network_gen = NetworkGenerator(logger, template_filepath=dummy_template_file, id_generator_instance=IdGenerator()) # Use dummy for isolated test
    
    # Test _get_bit_offset
    logger.info(f"Bit offset for I132.0: {network_gen._get_bit_offset('I132.0')}") # Expected: 1056
    logger.info(f"Bit offset for Q4.1: {network_gen._get_bit_offset('Q4.1')}")     # Expected: 33
    logger.info(f"Bit offset for M0.7: {network_gen._get_bit_offset('M0.7')}")     # Expected: 7
    logger.info(f"Bit offset for I1.0: {network_gen._get_bit_offset('I1.0')}")       # Expected: 8
    logger.info(f"Bit offset for invalid: {network_gen._get_bit_offset('X1.0')}") # Expected: -1

    # Test generating all networks
    all_net_lines = network_gen.generate_all_networks(mock_instances_data, mock_op_mode)
    
    logger.info("First 10 generated network lines:")
    for line_idx, line_content in enumerate(all_net_lines[:10]):
        logger.info(f"  {line_idx + 1}: {line_content.strip()}")
    
    logger.info("Last 10 generated network lines (if any):")
    for line_idx, line_content in enumerate(all_net_lines[-10:]):
         logger.info(f"  {len(all_net_lines) - 10 + line_idx +1}: {line_content.strip()}")

    # Clean up dummy file
    try:
        os.remove(dummy_template_file)
        logger.info(f"Cleaned up {dummy_template_file}")
    except OSError as e:
        logger.error(f"Error removing {dummy_template_file}: {e}") 