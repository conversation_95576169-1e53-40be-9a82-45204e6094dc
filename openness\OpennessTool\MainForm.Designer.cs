namespace OpennessTool
{
    partial class MainForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOpenTIA = new System.Windows.Forms.Button();
            this.btnOpenProject = new System.Windows.Forms.Button();
            this.treeViewProject = new System.Windows.Forms.TreeView();
            this.panel1 = new System.Windows.Forms.Panel();
            this.label1 = new System.Windows.Forms.Label();
            this.panel2 = new System.Windows.Forms.Panel();
            this.btnRefresh = new System.Windows.Forms.Button();
            this.btnImportExcel = new System.Windows.Forms.Button();
            this.btnImportXML = new System.Windows.Forms.Button();
            this.btnExport = new System.Windows.Forms.Button();
            this.panelManualRow = new System.Windows.Forms.Panel();
            this.lblManualRowExcelPath = new System.Windows.Forms.Label();
            this.btnGenerateManualRowFB = new System.Windows.Forms.Button();
            this.btnSelectManualRowExcel = new System.Windows.Forms.Button();
            this.lblManualRowTitle = new System.Windows.Forms.Label();
            this.panel1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.panelManualRow.SuspendLayout();
            this.SuspendLayout();
            //
            // btnOpenTIA
            //
            this.btnOpenTIA.Location = new System.Drawing.Point(12, 12);
            this.btnOpenTIA.Name = "btnOpenTIA";
            this.btnOpenTIA.Size = new System.Drawing.Size(120, 30);
            this.btnOpenTIA.TabIndex = 0;
            this.btnOpenTIA.Text = "打开博图";
            this.btnOpenTIA.UseVisualStyleBackColor = true;
            this.btnOpenTIA.Click += new System.EventHandler(this.btnOpenTIA_Click);
            //
            // btnOpenProject
            //
            this.btnOpenProject.Location = new System.Drawing.Point(138, 12);
            this.btnOpenProject.Name = "btnOpenProject";
            this.btnOpenProject.Size = new System.Drawing.Size(120, 30);
            this.btnOpenProject.TabIndex = 1;
            this.btnOpenProject.Text = "打开项目";
            this.btnOpenProject.UseVisualStyleBackColor = true;
            this.btnOpenProject.Click += new System.EventHandler(this.btnOpenProject_Click);
            //
            // treeViewProject
            //
            this.treeViewProject.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewProject.Location = new System.Drawing.Point(0, 30);
            this.treeViewProject.Name = "treeViewProject";
            this.treeViewProject.Size = new System.Drawing.Size(389, 737);
            this.treeViewProject.TabIndex = 2;
            this.treeViewProject.AfterSelect += new System.Windows.Forms.TreeViewEventHandler(this.treeViewProject_AfterSelect);
            //
            // panel1
            //
            this.panel1.Controls.Add(this.treeViewProject);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Left;
            this.panel1.Location = new System.Drawing.Point(0, 50);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(389, 767);
            this.panel1.TabIndex = 3;
            //
            // label1
            //
            this.label1.Dock = System.Windows.Forms.DockStyle.Top;
            this.label1.Location = new System.Drawing.Point(0, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(389, 30);
            this.label1.TabIndex = 3;
            this.label1.Text = "项目结构";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            //
            // panel2
            //
            this.panel2.Controls.Add(this.btnRefresh);
            this.panel2.Controls.Add(this.btnImportExcel);
            this.panel2.Controls.Add(this.btnImportXML);
            this.panel2.Controls.Add(this.btnExport);
            this.panel2.Controls.Add(this.btnOpenProject);
            this.panel2.Controls.Add(this.btnOpenTIA);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel2.Location = new System.Drawing.Point(0, 0);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(960, 50);
            this.panel2.TabIndex = 4;
            //
            // btnRefresh
            //
            this.btnRefresh.Location = new System.Drawing.Point(828, 12);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(120, 30);
            this.btnRefresh.TabIndex = 6;
            this.btnRefresh.Text = "刷新项目";
            this.btnRefresh.UseVisualStyleBackColor = true;
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            //
            // btnImportExcel
            //
            this.btnImportExcel.Location = new System.Drawing.Point(390, 12);
            this.btnImportExcel.Name = "btnImportExcel";
            this.btnImportExcel.Size = new System.Drawing.Size(120, 30);
            this.btnImportExcel.TabIndex = 4;
            this.btnImportExcel.Text = "导入Excel";
            this.btnImportExcel.UseVisualStyleBackColor = true;
            this.btnImportExcel.Click += new System.EventHandler(this.btnImportExcel_Click);
            //
            // btnImportXML
            //
            this.btnImportXML.Location = new System.Drawing.Point(264, 12);
            this.btnImportXML.Name = "btnImportXML";
            this.btnImportXML.Size = new System.Drawing.Size(120, 30);
            this.btnImportXML.TabIndex = 3;
            this.btnImportXML.Text = "导入XML";
            this.btnImportXML.UseVisualStyleBackColor = true;
            this.btnImportXML.Click += new System.EventHandler(this.btnImportXML_Click);
            //
            // btnExport
            //
            this.btnExport.Enabled = false;
            this.btnExport.Location = new System.Drawing.Point(516, 12);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(120, 30);
            this.btnExport.TabIndex = 2;
            this.btnExport.Text = "导出";
            this.btnExport.UseVisualStyleBackColor = true;
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            //
            // panelManualRow
            //
            this.panelManualRow.Controls.Add(this.lblManualRowExcelPath);
            this.panelManualRow.Controls.Add(this.btnGenerateManualRowFB);
            this.panelManualRow.Controls.Add(this.btnSelectManualRowExcel);
            this.panelManualRow.Controls.Add(this.lblManualRowTitle);
            this.panelManualRow.Dock = System.Windows.Forms.DockStyle.Right;
            this.panelManualRow.Location = new System.Drawing.Point(560, 50);
            this.panelManualRow.Name = "panelManualRow";
            this.panelManualRow.Size = new System.Drawing.Size(400, 767);
            this.panelManualRow.TabIndex = 5;
            //
            // lblManualRowExcelPath
            //
            this.lblManualRowExcelPath.Location = new System.Drawing.Point(10, 80);
            this.lblManualRowExcelPath.Name = "lblManualRowExcelPath";
            this.lblManualRowExcelPath.Size = new System.Drawing.Size(380, 40);
            this.lblManualRowExcelPath.TabIndex = 3;
            this.lblManualRowExcelPath.Text = "未选择文件";
            //
            // btnGenerateManualRowFB
            //
            this.btnGenerateManualRowFB.Location = new System.Drawing.Point(10, 130);
            this.btnGenerateManualRowFB.Name = "btnGenerateManualRowFB";
            this.btnGenerateManualRowFB.Size = new System.Drawing.Size(150, 30);
            this.btnGenerateManualRowFB.TabIndex = 2;
            this.btnGenerateManualRowFB.Text = "手动行FB块生成";
            this.btnGenerateManualRowFB.UseVisualStyleBackColor = true;
            this.btnGenerateManualRowFB.Click += new System.EventHandler(this.btnGenerateManualRowFB_Click);
            //
            // btnSelectManualRowExcel
            //
            this.btnSelectManualRowExcel.Location = new System.Drawing.Point(10, 40);
            this.btnSelectManualRowExcel.Name = "btnSelectManualRowExcel";
            this.btnSelectManualRowExcel.Size = new System.Drawing.Size(150, 30);
            this.btnSelectManualRowExcel.TabIndex = 1;
            this.btnSelectManualRowExcel.Text = "选择手动行Excel";
            this.btnSelectManualRowExcel.UseVisualStyleBackColor = true;
            this.btnSelectManualRowExcel.Click += new System.EventHandler(this.btnSelectManualRowExcel_Click);
            //
            // lblManualRowTitle
            //
            this.lblManualRowTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblManualRowTitle.Location = new System.Drawing.Point(0, 0);
            this.lblManualRowTitle.Name = "lblManualRowTitle";
            this.lblManualRowTitle.Size = new System.Drawing.Size(400, 30);
            this.lblManualRowTitle.TabIndex = 0;
            this.lblManualRowTitle.Text = "手动行程序生成";
            this.lblManualRowTitle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            //
            // MainForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(960, 817);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.panelManualRow);
            this.Controls.Add(this.panel2);
            this.Name = "MainForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "TIA Openness 工具";
            this.Load += new System.EventHandler(this.MainForm_Load);
            this.panel1.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panelManualRow.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button btnOpenTIA;
        private System.Windows.Forms.Button btnOpenProject;
        private System.Windows.Forms.TreeView treeViewProject;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.Button btnImportXML;
        private System.Windows.Forms.Button btnImportExcel;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Panel panelManualRow;
        private System.Windows.Forms.Button btnSelectManualRowExcel;
        private System.Windows.Forms.Button btnGenerateManualRowFB;
        private System.Windows.Forms.Label lblManualRowExcelPath;
        private System.Windows.Forms.Label lblManualRowTitle;
    }
}