﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.Pkcs</name>
  </assembly>
  <members>
    <member name="T:System.Security.Cryptography.CryptographicAttributeObject">
      <summary>Contains a type and a collection of values associated with that type.</summary>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObject.#ctor(System.Security.Cryptography.Oid)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> class using an attribute represented by the specified <see cref="T:System.Security.Cryptography.Oid" /> object.</summary>
      <param name="oid">The attribute to store in this <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObject.#ctor(System.Security.Cryptography.Oid,System.Security.Cryptography.AsnEncodedDataCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> class using an attribute represented by the specified <see cref="T:System.Security.Cryptography.Oid" /> object and the set of values associated with that attribute represented by the specified <see cref="T:System.Security.Cryptography.AsnEncodedDataCollection" /> collection.</summary>
      <param name="oid">The attribute to store in this <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object.</param>
      <param name="values">The set of values associated with the attribute represented by the <paramref name="oid" /> parameter.</param>
      <exception cref="T:System.InvalidOperationException">The collection contains duplicate items.</exception>
    </member>
    <member name="P:System.Security.Cryptography.CryptographicAttributeObject.Oid">
      <summary>Gets the <see cref="T:System.Security.Cryptography.Oid" /> object that specifies the object identifier for the attribute.</summary>
      <returns>The object identifier for the attribute.</returns>
    </member>
    <member name="P:System.Security.Cryptography.CryptographicAttributeObject.Values">
      <summary>Gets the <see cref="T:System.Security.Cryptography.AsnEncodedDataCollection" /> collection that contains the set of values that are associated with the attribute.</summary>
      <returns>The set of values that is associated with the attribute.</returns>
    </member>
    <member name="T:System.Security.Cryptography.CryptographicAttributeObjectCollection">
      <summary>Contains a set of <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> objects.</summary>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObjectCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObjectCollection.#ctor(System.Security.Cryptography.CryptographicAttributeObject)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> class, adding a specified <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> to the collection.</summary>
      <param name="attribute">A <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object that is added to the collection.</param>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObjectCollection.Add(System.Security.Cryptography.AsnEncodedData)">
      <summary>Adds the specified <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object to the collection.</summary>
      <param name="asnEncodedData">The <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <returns>
        <see langword="true" /> if the method returns the zero-based index of the added item; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObjectCollection.Add(System.Security.Cryptography.CryptographicAttributeObject)">
      <summary>Adds the specified <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object to the collection.</summary>
      <param name="attribute">The <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <exception cref="T:System.InvalidOperationException">The specified item already exists in the collection.</exception>
      <returns>
        <see langword="true" /> if the method returns the zero-based index of the added item; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObjectCollection.CopyTo(System.Security.Cryptography.CryptographicAttributeObject[],System.Int32)">
      <summary>Copies the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection to an array of <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> objects.</summary>
      <param name="array">An array of <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> objects that the collection is copied to.</param>
      <param name="index">The zero-based index in <paramref name="array" /> to which the collection is to be copied.</param>
      <exception cref="T:System.ArgumentException">One of the arguments provided to a method was not valid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <see langword="null" /> was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of an argument was outside the allowable range of values as defined by the called method.</exception>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObjectCollection.GetEnumerator">
      <summary>Gets a <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectEnumerator" /> object for the collection.</summary>
      <returns>
        <see langword="true" /> if the method returns a <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectEnumerator" /> object that can be used to enumerate the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObjectCollection.Remove(System.Security.Cryptography.CryptographicAttributeObject)">
      <summary>Removes the specified <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object from the collection.</summary>
      <param name="attribute">The <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object to remove from the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attribute" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObjectCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of this <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection to an <see cref="T:System.Array" /> array, starting at a particular index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> array that is the destination of the elements copied from this <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" />. The <see cref="T:System.Array" /> array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObjectCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.CryptographicAttributeObjectCollection.Count">
      <summary>Gets the number of items in the collection.</summary>
      <returns>The number of items in the collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.CryptographicAttributeObjectCollection.IsSynchronized">
      <summary>Gets a value that indicates whether access to the collection is synchronized, or thread safe.</summary>
      <returns>
        <see langword="true" /> if access to the collection is thread safe; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.CryptographicAttributeObjectCollection.Item(System.Int32)">
      <summary>Gets the <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object at the specified index in the collection.</summary>
      <param name="index">An <see cref="T:System.Int32" /> value that represents the zero-based index of the <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object to retrieve.</param>
      <returns>The <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object at the specified index.</returns>
    </member>
    <member name="P:System.Security.Cryptography.CryptographicAttributeObjectCollection.SyncRoot">
      <summary>Gets an <see cref="T:System.Object" /> object used to synchronize access to the collection.</summary>
      <returns>An <see cref="T:System.Object" /> object used to synchronize access to the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection.</returns>
    </member>
    <member name="T:System.Security.Cryptography.CryptographicAttributeObjectEnumerator">
      <summary>Provides enumeration functionality for the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObjectEnumerator.MoveNext">
      <summary>Advances the enumeration to the next <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object in the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection.</summary>
      <returns>
        <see langword="true" /> if the enumeration successfully moved to the next <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object; <see langword="false" /> if the enumerator is at the end of the enumeration.</returns>
    </member>
    <member name="M:System.Security.Cryptography.CryptographicAttributeObjectEnumerator.Reset">
      <summary>Resets the enumeration to the first <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object in the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection.</summary>
    </member>
    <member name="P:System.Security.Cryptography.CryptographicAttributeObjectEnumerator.Current">
      <summary>Gets the current <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object from the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object that represents the current cryptographic attribute in the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.CryptographicAttributeObjectEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the current <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object from the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.CryptographicAttributeObject" /> object that represents the current cryptographic attribute in the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.AlgorithmIdentifier">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.AlgorithmIdentifier" /> class defines the algorithm used for a cryptographic operation.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.AlgorithmIdentifier.#ctor">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.AlgorithmIdentifier.#ctor" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.AlgorithmIdentifier" /> class by using a set of default parameters.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.AlgorithmIdentifier.#ctor(System.Security.Cryptography.Oid)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.AlgorithmIdentifier.#ctor(System.Security.Cryptography.Oid)" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.AlgorithmIdentifier" /> class with the specified algorithm identifier.</summary>
      <param name="oid">An object identifier for the algorithm.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.AlgorithmIdentifier.#ctor(System.Security.Cryptography.Oid,System.Int32)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.AlgorithmIdentifier.#ctor(System.Security.Cryptography.Oid,System.Int32)" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.AlgorithmIdentifier" /> class with the specified algorithm identifier and key length.</summary>
      <param name="oid">An object identifier for the algorithm.</param>
      <param name="keyLength">The length, in bits, of the key.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.AlgorithmIdentifier.KeyLength">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.AlgorithmIdentifier.KeyLength" /> property sets or retrieves the key length, in bits. This property is not used for algorithms that use a fixed key length.</summary>
      <returns>An int value that represents the key length, in bits.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.AlgorithmIdentifier.Oid">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.AlgorithmIdentifier.Oid" /> property sets or retrieves the <see cref="T:System.Security.Cryptography.Oid" /> object that specifies the object identifier for the algorithm.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.Oid" /> object that represents the algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.AlgorithmIdentifier.Parameters">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.AlgorithmIdentifier.Parameters" /> property sets or retrieves any parameters required by the algorithm.</summary>
      <returns>An array of byte values that specifies any parameters required by the algorithm.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.CmsRecipient">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> class defines the recipient of a CMS/PKCS #7 message.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipient.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType,System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> class with a specified certificate and recipient identifier type, using the default encryption mode for the public key algorithm.</summary>
      <param name="recipientIdentifierType">The scheme to use for identifying which recipient certificate was used.</param>
      <param name="certificate">The certificate to use when encrypting for this recipient.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="certificate" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="recipientIdentifierType" /> value is not supported.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipient.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType,System.Security.Cryptography.X509Certificates.X509Certificate2,System.Security.Cryptography.RSAEncryptionPadding)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> class with a specified RSA certificate, RSA encryption padding, and subject identifier.</summary>
      <param name="recipientIdentifierType">The scheme to use for identifying which recipient certificate was used.</param>
      <param name="certificate">The certificate to use when encrypting for this recipient.</param>
      <param name="rsaEncryptionPadding">The RSA padding mode to use when encrypting for this recipient.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="certificate" /> or <paramref name="rsaEncryptionPadding" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="certificate" /> parameter public key is not recognized as an RSA public key.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipient.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> class with a specified certificate, using the default encryption mode for the public key algorithm and an <see cref="F:System.Security.Cryptography.Pkcs.SubjectIdentifierType.IssuerAndSerialNumber" /> subject identifier.</summary>
      <param name="certificate">The certificate to use when encrypting for this recipient.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="certificate" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipient.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,System.Security.Cryptography.RSAEncryptionPadding)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> class with a specified RSA certificate and RSA encryption padding, using an <see cref="F:System.Security.Cryptography.Pkcs.SubjectIdentifierType.IssuerAndSerialNumber" /> subject identifier.</summary>
      <param name="certificate">The certificate to use when encrypting for this recipient.</param>
      <param name="rsaEncryptionPadding">The RSA padding mode to use when encrypting for this recipient.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="certificate" /> or <paramref name="rsaEncryptionPadding" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="certificate" /> parameter public key is not recognized as an RSA public key.

-or-

The <paramref name="recipientIdentifierType" /> value is not supported.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsRecipient.Certificate">
      <summary>Gets the certificate to use when encrypting for this recipient.</summary>
      <returns>The certificate to use when encrypting for this recipient.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsRecipient.RecipientIdentifierType">
      <summary>Gets the scheme to use for identifying which recipient certificate was used.</summary>
      <returns>The scheme to use for identifying which recipient certificate was used.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsRecipient.RSAEncryptionPadding">
      <summary>Gets the RSA encryption padding to use when encrypting for this recipient.</summary>
      <returns>The RSA encryption padding to use when encrypting for this recipient.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> class represents a set of <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> objects. <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> implements the <see cref="T:System.Collections.ICollection" /> interface.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.#ctor">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.#ctor" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.#ctor(System.Security.Cryptography.Pkcs.CmsRecipient)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.#ctor(System.Security.Cryptography.Pkcs.CmsRecipient)" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> class and adds the specified recipient.</summary>
      <param name="recipient">An instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> class that represents the specified CMS/PKCS #7 recipient.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType,System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType,System.Security.Cryptography.X509Certificates.X509Certificate2Collection)" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> class and adds recipients based on the specified subject identifier and set of certificates that identify the recipients.</summary>
      <param name="recipientIdentifierType">A member of the <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifierType" /> enumeration that specifies the type of subject identifier.</param>
      <param name="certificates">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> collection that contains the certificates that identify the recipients.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.Add(System.Security.Cryptography.Pkcs.CmsRecipient)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.Add(System.Security.Cryptography.Pkcs.CmsRecipient)" /> method adds a recipient to the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</summary>
      <param name="recipient">A <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> object that represents the recipient to add to the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="recipient" /> is <see langword="null" />.</exception>
      <returns>If the method succeeds, the method returns an <see cref="T:System.Int32" /> value that represents the zero-based position where the recipient is to be inserted.  
  
 If the method fails, it throws an exception.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.CopyTo(System.Array,System.Int32)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.CopyTo(System.Array,System.Int32)" /> method copies the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection to an array.</summary>
      <param name="array">An <see cref="T:System.Array" /> object to which the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection is to be copied.</param>
      <param name="index">The zero-based index in <paramref name="array" /> where the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection is copied.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is not large enough to hold the specified elements.  

-or-

<paramref name="array" /> does not contain the proper number of dimensions.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is outside the range of elements in <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.CopyTo(System.Security.Cryptography.Pkcs.CmsRecipient[],System.Int32)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.CopyTo(System.Security.Cryptography.Pkcs.CmsRecipient[],System.Int32)" /> method copies the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection to a <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> array.</summary>
      <param name="array">An array of <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> objects where the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection is to be copied.</param>
      <param name="index">The zero-based index for the array of <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> objects in <paramref name="array" /> to which the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection is copied.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is not large enough to hold the specified elements.  

-or-

<paramref name="array" /> does not contain the proper number of dimensions.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is outside the range of elements in <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.GetEnumerator">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.GetEnumerator" /> method returns a <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator" /> object for the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator" /> object that can be used to enumerate the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.Remove(System.Security.Cryptography.Pkcs.CmsRecipient)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.Remove(System.Security.Cryptography.Pkcs.CmsRecipient)" /> method removes a recipient from the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</summary>
      <param name="recipient">A <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> object that represents the recipient to remove from the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="recipient" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.CmsRecipientCollection.System#Collections#IEnumerable#GetEnumerator" /> method returns a <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator" /> object for the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator" /> object that can be used to enumerate the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsRecipientCollection.Count">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.CmsRecipientCollection.Count" /> property retrieves the number of items in the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</summary>
      <returns>An <see cref="T:System.Int32" /> value that represents the number of items in the collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsRecipientCollection.IsSynchronized">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.CmsRecipientCollection.IsSynchronized" /> property retrieves whether access to the collection is synchronized, or thread safe. This property always returns <see langword="false" />, which means that the collection is not thread safe.</summary>
      <returns>A <see cref="T:System.Boolean" /> value of <see langword="false" />, which means that the collection is not thread safe.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsRecipientCollection.Item(System.Int32)">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.CmsRecipientCollection.Item(System.Int32)" /> property retrieves the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> object at the specified index in the collection.</summary>
      <param name="index">An <see cref="T:System.Int32" /> value that represents the index in the collection. The index is zero based.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of an argument was outside the allowable range of values as defined by the called method.</exception>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> object at the specified index.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsRecipientCollection.SyncRoot">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.CmsRecipientCollection.SyncRoot" /> property retrieves an <see cref="T:System.Object" /> object used to synchronize access to the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</summary>
      <returns>An <see cref="T:System.Object" /> object that is used to synchronize access to the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator" /> class provides enumeration functionality for the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection. <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator" /> implements the <see cref="T:System.Collections.IEnumerator" /> interface.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator.MoveNext">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator.MoveNext" /> method advances the enumeration to the next <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> object in the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</summary>
      <returns>
        <see langword="true" /> if the enumeration successfully moved to the next <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> object; <see langword="false" /> if the enumeration moved past the last item in the enumeration.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator.Reset">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator.Reset" /> method resets the enumeration to the first <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> object in the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</summary>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator.Current">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator.Current" /> property retrieves the current <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> object from the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> object that represents the current recipient in the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator.System#Collections#IEnumerator#Current">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.CmsRecipientEnumerator.System#Collections#IEnumerator#Current" /> property retrieves the current <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> object from the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" /> object that represents the current recipient in the <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipientCollection" /> collection.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.CmsSigner">
      <summary>Represents a potential signer for a CMS/PKCS#7 signed message.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsSigner.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> class with default values.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsSigner.#ctor(System.Security.Cryptography.CspParameters)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> class from a persisted key.</summary>
      <param name="parameters">The CSP parameters to describe which signing key to use.</param>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsSigner.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> class with a specified subject identifier type.</summary>
      <param name="signerIdentifierType">The scheme to use for identifying which signing certificate was used.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsSigner.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType,System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> class with a specified signer certificate and subject identifier type.</summary>
      <param name="signerIdentifierType">The scheme to use for identifying which signing certificate was used.</param>
      <param name="certificate">The certificate whose private key will be used to sign a message.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsSigner.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType,System.Security.Cryptography.X509Certificates.X509Certificate2,System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> class with a specified signer certificate, subject identifier type, and private key object.</summary>
      <param name="signerIdentifierType">One of the enumeration values that specifies the scheme to use for identifying which signing certificate was used.</param>
      <param name="certificate">The certificate whose private key will be used to sign a message.</param>
      <param name="privateKey">The private key object to use when signing the message.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsSigner.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType,System.Security.Cryptography.X509Certificates.X509Certificate2,System.Security.Cryptography.RSA,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Initializes a new instance of the CmsSigner class with a specified signer certificate, subject identifier type, private key object, and RSA signature padding.</summary>
      <param name="signerIdentifierType">One of the enumeration values that specifies the scheme to use for identifying which signing certificate was used.</param>
      <param name="certificate">The certificate whose private key will be used to sign a message.</param>
      <param name="privateKey">The private key object to use when signing the message.</param>
      <param name="signaturePadding">The RSA signature padding to use.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.CmsSigner.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> class with a specified signer certificate.</summary>
      <param name="certificate">The certificate whose private key will be used to sign a message.</param>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsSigner.Certificate">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.CmsSigner.Certificate" /> property sets or retrieves the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object that represents the signing certificate.</summary>
      <returns>An  <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object that represents the signing certificate.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsSigner.Certificates">
      <summary>Gets a collection of certificates which are considered with <see cref="F:System.Security.Cryptography.X509Certificates.X509IncludeOption.WholeChain" /> and <see cref="F:System.Security.Cryptography.X509Certificates.X509IncludeOption.ExcludeRoot" />.</summary>
      <returns>A collection of certificates which are considered with <see cref="F:System.Security.Cryptography.X509Certificates.X509IncludeOption.WholeChain" /> and <see cref="F:System.Security.Cryptography.X509Certificates.X509IncludeOption.ExcludeRoot" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsSigner.DigestAlgorithm">
      <summary>Gets or sets the algorithm identifier for the hash algorithm to use with the signature.</summary>
      <returns>The algorithm identifier for the hash algorithm to use with the signature.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsSigner.IncludeOption">
      <summary>Gets or sets the option indicating how much of a the signer certificate's certificate chain should be embedded in the signed message.</summary>
      <exception cref="T:System.ArgumentException">One of the arguments provided to a method was not valid.</exception>
      <returns>One of the enumeration values that indicates how much of a the signer certificate's certificate chain should be embedded in the signed message.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsSigner.PrivateKey">
      <summary>Gets or sets the private key object to use during signing.</summary>
      <returns>The private key to use during signing, or <see langword="null" /> to use the private key associated with the <see cref="P:System.Security.Cryptography.Pkcs.CmsSigner.Certificate" /> property.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsSigner.SignaturePadding">
      <summary>Gets or sets the RSA signature padding to use.</summary>
      <returns>The RSA signature padding to use.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsSigner.SignedAttributes">
      <summary>Gets a collections of attributes to associate with this signature that are also protected by the signature.</summary>
      <returns>A collections of attributes to associate with this signature that are also protected by the signature.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsSigner.SignerIdentifierType">
      <summary>Gets the scheme to use for identifying which signing certificate was used.</summary>
      <exception cref="T:System.ArgumentException">One of the arguments provided to a method was not valid.</exception>
      <returns>The scheme to use for identifying which recipient certificate was used.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.CmsSigner.UnsignedAttributes">
      <summary>Gets a collections of attributes to associate with this signature that are not protected by the signature.</summary>
      <returns>A collections of attributes to associate with this signature that are not protected by the signature.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.ContentInfo">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.ContentInfo" /> class represents the CMS/PKCS #7 ContentInfo data structure as defined in the CMS/PKCS #7 standards document. This data structure is the basis for all CMS/PKCS #7 messages.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.ContentInfo.#ctor(System.Byte[])">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.ContentInfo.#ctor(System.Byte[])" /> constructor  creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.ContentInfo" /> class by using an array of byte values as the data and a default <paramref name="object identifier" /> (OID) that represents the content type.</summary>
      <param name="content">An array of byte values that represents the data from which to create the <see cref="T:System.Security.Cryptography.Pkcs.ContentInfo" /> object.</param>
      <exception cref="T:System.ArgumentNullException">A null reference  was passed to a method that does not accept it as a valid argument.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.ContentInfo.#ctor(System.Security.Cryptography.Oid,System.Byte[])">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.ContentInfo.#ctor(System.Security.Cryptography.Oid,System.Byte[])" /> constructor  creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.ContentInfo" /> class by using the specified content type and an array of byte values as the data.</summary>
      <param name="contentType">An <see cref="T:System.Security.Cryptography.Oid" /> object that contains an <c>object identifier</c> (OID) that specifies the content type of the content. This can be data, digestedData, encryptedData, envelopedData, hashedData, signedAndEnvelopedData, or signedData.  For more information, see  Remarks.</param>
      <param name="content">An array of byte values that represents the data from which to create the <see cref="T:System.Security.Cryptography.Pkcs.ContentInfo" /> object.</param>
      <exception cref="T:System.ArgumentNullException">A null reference  was passed to a method that does not accept it as a valid argument.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.ContentInfo.GetContentType(System.Byte[])">
      <summary>Retrieves the outer content type of an encoded CMS ContentInfo message.</summary>
      <param name="encodedMessage">An array of byte values that represents the encoded CMS ContentInfo message from which to retrieve the outer content type.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="encodedMessage" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="encodedMessage" /> cannot be decoded as a valid CMS ContentInfo value.</exception>
      <returns>The outer content type of the specified encoded CMS ContentInfo message.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.ContentInfo.GetContentType(System.ReadOnlySpan{System.Byte})">
      <summary>Retrieves the outer content type of an encoded CMS ContentInfo message.</summary>
      <param name="encodedMessage">A read-only span of byte values that represents the encoded CMS ContentInfo message from which to retrieve the outer content type.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="encodedMessage" /> cannot be decoded as a valid CMS ContentInfo value.</exception>
      <returns>The outer content type of the specified encoded CMS ContentInfo message.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.ContentInfo.Content">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.ContentInfo.Content" /> property  retrieves the content of the CMS/PKCS #7 message.</summary>
      <returns>An array of byte values that represents the content data.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.ContentInfo.ContentType">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.ContentInfo.ContentType" /> property  retrieves the <see cref="T:System.Security.Cryptography.Oid" /> object that contains the <paramref name="object identifier" /> (OID)  of the content type of the inner content of the CMS/PKCS #7 message.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.Oid" /> object that contains the OID value that represents the content type.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.EnvelopedCms">
      <summary>Represents a CMS/PKCS#7 structure for enveloped data.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.EnvelopedCms" /> class with default values.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.#ctor(System.Security.Cryptography.Pkcs.ContentInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.EnvelopedCms" /> class with specified content information.</summary>
      <param name="contentInfo">The message content to encrypt.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="contentInfo" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.#ctor(System.Security.Cryptography.Pkcs.ContentInfo,System.Security.Cryptography.Pkcs.AlgorithmIdentifier)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.EnvelopedCms" /> class with a specified symmetric encryption algorithm and content information.</summary>
      <param name="contentInfo">The message content to encrypt.</param>
      <param name="encryptionAlgorithm">The identifier for the symmetric encryption algorithm to use when encrypting the message content.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="contentInfo" /> or <paramref name="encryptionAlgorithm" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.Decode(System.Byte[])">
      <summary>Decodes an array of bytes as a CMS/PKCS#7 EnvelopedData message.</summary>
      <param name="encodedMessage">The byte array containing the sequence of bytes to decode.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="encodedMessage" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="encodedMessage" /> parameter was not successfully decoded.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.Decode(System.ReadOnlySpan{System.Byte})">
      <summary>Decodes the provided data as a CMS/PKCS#7 EnvelopedData message.</summary>
      <param name="encodedMessage">The data to decode.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="encodedMessage" /> parameter was not successfully decoded.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.Decrypt">
      <summary>Decrypts the contents of the decoded enveloped CMS/PKCS#7 message via any available recipient by searching certificate stores for a matching certificate and key.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <exception cref="T:System.InvalidOperationException">A method call was invalid for the object's current state.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.Decrypt(System.Security.Cryptography.Pkcs.RecipientInfo)">
      <summary>Decrypts the contents of the decoded enveloped CMS/PKCS#7 message via a specified recipient info by searching certificate stores for a matching certificate and key.</summary>
      <param name="recipientInfo">The recipient info to use for decryption.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="recipientInfo" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <exception cref="T:System.InvalidOperationException">A method call was invalid for the object's current state.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.Decrypt(System.Security.Cryptography.Pkcs.RecipientInfo,System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Decrypts the contents of the decoded enveloped CMS/PKCS#7 message via a specified recipient info with a specified private key.</summary>
      <param name="recipientInfo">The recipient info to use for decryption.</param>
      <param name="privateKey">The private key to use to decrypt the recipient-specific information.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="recipientInfo" /> or <paramref name="privateKey" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <exception cref="T:System.InvalidOperationException">A method call was invalid for the object's current state.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.Decrypt(System.Security.Cryptography.Pkcs.RecipientInfo,System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Decrypts the contents of the decoded enveloped CMS/PKCS#7 message via a specified recipient info by searching certificate stores and a provided collection for a matching certificate and key.</summary>
      <param name="recipientInfo">The recipient info to use for decryption.</param>
      <param name="extraStore">A collection of certificates to use in addition to the certificate stores for finding a recipient certificate and private key.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="recipientInfo" /> or <paramref name="extraStore" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <exception cref="T:System.InvalidOperationException">A method call was invalid for the object's current state.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.Decrypt(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Decrypts the contents of the decoded enveloped CMS/PKCS#7 message via any available recipient info by searching certificate stores and a provided collection for a matching certificate and key.</summary>
      <param name="extraStore">A collection of certificates to use in addition to the certificate stores for finding a recipient certificate and private key.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="extraStore" /> parameter was <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <exception cref="T:System.InvalidOperationException">A method call was invalid for the object's current state.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.Encode">
      <summary>Encodes the contents of the enveloped CMS/PKCS#7 message and returns it as a byte array.</summary>
      <exception cref="T:System.InvalidOperationException">A method call was invalid for the object's current state.</exception>
      <returns>A byte array representing the encoded form of the CMS/PKCS#7 message.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.Encrypt(System.Security.Cryptography.Pkcs.CmsRecipient)">
      <summary>Encrypts the contents of the CMS/PKCS#7 message for a single specified recipient.</summary>
      <param name="recipient">The recipient information describing the single recipient of this message.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="recipient" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.EnvelopedCms.Encrypt(System.Security.Cryptography.Pkcs.CmsRecipientCollection)">
      <summary>Encrypts the contents of the CMS/PKCS#7 message for one or more recipients.</summary>
      <param name="recipients">A collection describing the recipients for the message.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="recipients" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.EnvelopedCms.Certificates">
      <summary>Gets the collection of certificates associated with the enveloped CMS/PKCS#7 message.</summary>
      <returns>The collection of certificates associated with the enveloped CMS/PKCS#7 message.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.EnvelopedCms.ContentEncryptionAlgorithm">
      <summary>Gets the identifier of the symmetric encryption algorithm associated with this message.</summary>
      <returns>The identifier of the symmetric encryption algorithm associated with this message.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.EnvelopedCms.ContentInfo">
      <summary>Gets the content information for the enveloped CMS/PKCS#7 message.</summary>
      <returns>The content information for the enveloped CMS/PKCS#7 message.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.EnvelopedCms.RecipientInfos">
      <summary>Gets a collection that represents the recipients list for a decoded message. The default value is an empty collection.</summary>
      <returns>A collection that represents the recipients list for a decoded message. The default value is an empty collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.EnvelopedCms.UnprotectedAttributes">
      <summary>Gets the collection of unprotected (unencrypted) attributes associated with the enveloped CMS/PKCS#7 message.</summary>
      <returns>The collection of unprotected (unencrypted) attributes associated with the enveloped CMS/PKCS#7 message.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.EnvelopedCms.Version">
      <summary>Gets the version of the decoded enveloped CMS/PKCS#7 message.</summary>
      <returns>The version of the decoded enveloped CMS/PKCS#7 message.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo" /> class defines key agreement recipient information. Key agreement algorithms typically use the Diffie-Hellman key agreement algorithm, in which the two parties that establish a shared cryptographic key both take part in its generation and, by definition, agree on that key. This is in contrast to key transport algorithms, in which one party generates the key unilaterally and sends, or transports it, to the other party.</summary>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.Date">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.Date" /> property retrieves the date and time of the start of the key agreement protocol by the originator.</summary>
      <exception cref="T:System.InvalidOperationException">The recipient identifier type is not a subject key identifier.</exception>
      <returns>The date and time of the start of the key agreement protocol by the originator.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.EncryptedKey">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.EncryptedKey" /> property retrieves the encrypted recipient keying material.</summary>
      <returns>An array of byte values that contain the encrypted recipient keying material.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.KeyEncryptionAlgorithm">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.KeyEncryptionAlgorithm" /> property retrieves the algorithm used to perform the key agreement.</summary>
      <returns>The value of the algorithm used to perform the key agreement.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.OriginatorIdentifierOrKey">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.OriginatorIdentifierOrKey" /> property retrieves information about the originator of the key agreement for key agreement algorithms that warrant it.</summary>
      <returns>An object that contains information about the originator of the key agreement.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.OtherKeyAttribute">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.OtherKeyAttribute" /> property retrieves attributes of the keying material.</summary>
      <exception cref="T:System.InvalidOperationException">The recipient identifier type is not a subject key identifier.</exception>
      <returns>The attributes of the keying material.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.RecipientIdentifier">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.RecipientIdentifier" /> property retrieves the identifier of the recipient.</summary>
      <returns>The identifier of the recipient.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.Version">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo.Version" /> property retrieves the version of the key agreement recipient. This is automatically set for  objects in this class, and the value  implies that the recipient is taking part in a key agreement algorithm.</summary>
      <returns>The version of the <see cref="T:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.KeyTransRecipientInfo">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.KeyTransRecipientInfo" /> class defines key transport recipient information.        Key transport algorithms typically use the RSA algorithm, in which  an originator establishes a shared cryptographic key with a recipient by generating that key and  then transporting it to the recipient. This is in contrast to key agreement algorithms, in which the two parties that will be using a cryptographic key both take part in its generation, thereby mutually agreeing to that key.</summary>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.KeyTransRecipientInfo.EncryptedKey">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.KeyTransRecipientInfo.EncryptedKey" /> property retrieves the encrypted key for this key transport recipient.</summary>
      <returns>An array of byte values that represents the encrypted key.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.KeyTransRecipientInfo.KeyEncryptionAlgorithm">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.KeyTransRecipientInfo.KeyEncryptionAlgorithm" /> property retrieves the key encryption algorithm used to encrypt the content encryption key.</summary>
      <returns>An  <see cref="T:System.Security.Cryptography.Pkcs.AlgorithmIdentifier" /> object that stores the key encryption algorithm identifier.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.KeyTransRecipientInfo.RecipientIdentifier">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.KeyTransRecipientInfo.RecipientIdentifier" /> property retrieves the subject identifier associated with the encrypted content.</summary>
      <returns>A   <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifier" /> object that  stores the identifier of the recipient taking part in the key transport.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.KeyTransRecipientInfo.Version">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.KeyTransRecipientInfo.Version" /> property retrieves the version of the key transport recipient. The version of the key transport recipient is automatically set for  objects in this class, and the value  implies that the recipient is taking part in a key transport algorithm.</summary>
      <returns>An int value that represents the version of the key transport <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs12Builder">
      <summary>Enables the creation of PKCS#12 PFX data values. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Builder.#ctor">
      <summary>Initializes a new value of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12Builder" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Builder.AddSafeContentsEncrypted(System.Security.Cryptography.Pkcs.Pkcs12SafeContents,System.Byte[],System.Security.Cryptography.PbeParameters)">
      <summary>Add contents to the PFX in an bundle encrypted with a byte-based password from a byte array.</summary>
      <param name="safeContents">The contents to add to the PFX.</param>
      <param name="passwordBytes">The byte array to use as a password when encrypting the contents.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the contents.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="safeContents" /> or <paramref name="pbeParameters" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="safeContents" /> parameter value is already encrypted.</exception>
      <exception cref="T:System.InvalidOperationException">The PFX is already sealed (<see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Builder.IsSealed" /> is <see langword="true" />).</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="pbeParameters" /> indicates that <see cref="F:System.Security.Cryptography.PbeEncryptionAlgorithm.TripleDes3KeyPkcs12" /> should be used, which requires <see cref="T:System.Char" />-based passwords.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Builder.AddSafeContentsEncrypted(System.Security.Cryptography.Pkcs.Pkcs12SafeContents,System.ReadOnlySpan{System.Byte},System.Security.Cryptography.PbeParameters)">
      <summary>Add contents to the PFX in an bundle encrypted with a byte-based password from a span.</summary>
      <param name="safeContents">The contents to add to the PFX.</param>
      <param name="passwordBytes">The byte span to use as a password when encrypting the contents.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the contents.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="safeContents" /> or <paramref name="pbeParameters" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="safeContents" /> parameter value is already encrypted.</exception>
      <exception cref="T:System.InvalidOperationException">The PFX is already sealed (<see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Builder.IsSealed" /> is <see langword="true" />).</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="pbeParameters" /> indicates that <see cref="F:System.Security.Cryptography.PbeEncryptionAlgorithm.TripleDes3KeyPkcs12" /> should be used, which requires <see cref="T:System.Char" />-based passwords.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Builder.AddSafeContentsEncrypted(System.Security.Cryptography.Pkcs.Pkcs12SafeContents,System.ReadOnlySpan{System.Char},System.Security.Cryptography.PbeParameters)">
      <summary>Add contents to the PFX in an bundle encrypted with a char-based password from a span.</summary>
      <param name="safeContents">The contents to add to the PFX.</param>
      <param name="password">The span to use as a password when encrypting the contents.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the contents.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="safeContents" /> or <paramref name="pbeParameters" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="safeContents" /> parameter value is already encrypted.</exception>
      <exception cref="T:System.InvalidOperationException">The PFX is already sealed (<see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Builder.IsSealed" /> is <see langword="true" />).</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Builder.AddSafeContentsEncrypted(System.Security.Cryptography.Pkcs.Pkcs12SafeContents,System.String,System.Security.Cryptography.PbeParameters)">
      <summary>Add contents to the PFX in an bundle encrypted with a char-based password from a string.</summary>
      <param name="safeContents">The contents to add to the PFX.</param>
      <param name="password">The string to use as a password when encrypting the contents.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the contents.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="safeContents" /> or <paramref name="pbeParameters" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="safeContents" /> parameter value is already encrypted.</exception>
      <exception cref="T:System.InvalidOperationException">The PFX is already sealed (<see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Builder.IsSealed" /> is <see langword="true" />).</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Builder.AddSafeContentsUnencrypted(System.Security.Cryptography.Pkcs.Pkcs12SafeContents)">
      <summary>Add contents to the PFX without encrypting them.</summary>
      <param name="safeContents">The contents to add to the PFX.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="safeContents" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The PFX is already sealed (<see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Builder.IsSealed" /> is <see langword="true" />).</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Builder.Encode">
      <summary>Encodes the contents of a sealed PFX and returns it as a byte array.</summary>
      <exception cref="T:System.InvalidOperationException">The PFX is not sealed (<see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Builder.IsSealed" /> is <see langword="false" />).</exception>
      <returns>A byte array representing the encoded form of the PFX.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Builder.SealWithMac(System.ReadOnlySpan{System.Char},System.Security.Cryptography.HashAlgorithmName,System.Int32)">
      <summary>Seals the PFX against further changes by applying a password-based Message Authentication Code (MAC) over the contents with a password from a span.</summary>
      <param name="password">The password to use as a key for computing the MAC.</param>
      <param name="hashAlgorithm">The hash algorithm to use when computing the MAC.</param>
      <param name="iterationCount">The iteration count for the Key Derivation Function (KDF) used in computing the MAC.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="iterationCount" /> parameter is less than or equal to 0.</exception>
      <exception cref="T:System.InvalidOperationException">The PFX is already sealed (<see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Builder.IsSealed" /> is <see langword="true" />).</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Builder.SealWithMac(System.String,System.Security.Cryptography.HashAlgorithmName,System.Int32)">
      <summary>Seals the PFX against further changes by applying a password-based Message Authentication Code (MAC) over the contents with a password from a string.</summary>
      <param name="password">The password to use as a key for computing the MAC.</param>
      <param name="hashAlgorithm">The hash algorithm to use when computing the MAC.</param>
      <param name="iterationCount">The iteration count for the Key Derivation Function (KDF) used in computing the MAC.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="iterationCount" /> parameter is less than or equal to 0.</exception>
      <exception cref="T:System.InvalidOperationException">The PFX is already sealed (<see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Builder.IsSealed" /> is <see langword="true" />).</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Builder.SealWithoutIntegrity">
      <summary>Seals the PFX from further changes without applying tamper-protection.</summary>
      <exception cref="T:System.InvalidOperationException">The PFX is already sealed (<see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Builder.IsSealed" /> is <see langword="true" />).</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Builder.TryEncode(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to encode the contents of a sealed PFX into a provided buffer.</summary>
      <param name="destination">The byte span to receive the PKCS#12 PFX data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.InvalidOperationException">The PFX is not sealed (<see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Builder.IsSealed" /> is <see langword="false" />).</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12Builder.IsSealed">
      <summary>Gets a value that indicates whether the PFX data has been sealed.</summary>
      <returns>A value that indicates whether the PFX data has been sealed.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs12CertBag">
      <summary>Represents the PKCS#12 CertBag. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12CertBag.#ctor(System.Security.Cryptography.Oid,System.ReadOnlyMemory{System.Byte})">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12CertBag" /> class using the specified certificate type and encoding.</summary>
      <param name="certificateType">The Object Identifier (OID) for the certificate type.</param>
      <param name="encodedCertificate">The encoded certificate value.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="certificateType" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="encodedCertificate" /> parameter does not represent a single ASN.1 BER-encoded value.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12CertBag.GetCertificate">
      <summary>Gets the contents of the CertBag interpreted as an X.509 public key certificate.</summary>
      <exception cref="T:System.InvalidOperationException">The content type is not the X.509 public key certificate content type.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents were not valid for the X.509 certificate content type.</exception>
      <returns>A certificate decoded from the contents of the CertBag.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12CertBag.GetCertificateType">
      <summary>Gets the Object Identifier (OID) which identifies the content type of the encoded certificte value.</summary>
      <returns>The Object Identifier (OID) which identifies the content type of the encoded certificate value.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12CertBag.EncodedCertificate">
      <summary>Gets the uninterpreted certificate contents of the CertSafeBag.</summary>
      <returns>The uninterpreted certificate contents of the CertSafeBag.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12CertBag.IsX509Certificate">
      <summary>Gets a value indicating whether the content type of the encoded certificate value is the X.509 public key certificate content type.</summary>
      <returns>
        <see langword="true" /> if the content type is the X.509 public key certificate content type (1.2.840.113549.1.9.22.1); otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs12ConfidentialityMode">
      <summary>Represents the kind of encryption associated with a PKCS#12 SafeContents value.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.Pkcs12ConfidentialityMode.None">
      <summary>The SafeContents value is not encrypted.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.Pkcs12ConfidentialityMode.Password">
      <summary>The SafeContents value is encrypted with a password.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.Pkcs12ConfidentialityMode.PublicKey">
      <summary>The SafeContents value is encrypted using public key cryptography.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.Pkcs12ConfidentialityMode.Unknown">
      <summary>The kind of encryption applied to the SafeContents is unknown or could not be determined.</summary>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs12Info">
      <summary>Represents the data from PKCS#12 PFX contents. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Info.Decode(System.ReadOnlyMemory{System.Byte},System.Int32@,System.Boolean)">
      <summary>Reads the provided data as a PKCS#12 PFX and returns an object view of the contents.</summary>
      <param name="encodedBytes">The data to interpret as a PKCS#12 PFX.</param>
      <param name="bytesConsumed">When this method returns, contains a value that indicates the number of bytes from <paramref name="encodedBytes" /> which were read by this method. This parameter is treated as uninitialized.</param>
      <param name="skipCopy">
        <see langword="true" /> to store <paramref name="encodedBytes" /> without making a defensive copy; otherwise, <see langword="false" />. The default is <see langword="false" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of the <paramref name="encodedBytes" /> parameter were not successfully decoded as a PKCS#12 PFX.</exception>
      <returns>An object view of the PKCS#12 PFX decoded from the input.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Info.VerifyMac(System.ReadOnlySpan{System.Char})">
      <summary>Attempts to verify the integrity of the <see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Info.AuthenticatedSafe" /> contents with a password represented by a <c>System.ReadOnlySpan{System.Char}</c>.</summary>
      <param name="password">The password to use to attempt to verify integrity.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Info.IntegrityMode" /> value is not <see cref="F:System.Security.Cryptography.Pkcs.Pkcs12IntegrityMode.Password" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The hash algorithm option specified by the PKCS#12 PFX contents could not be identified or is not supported by this platform.</exception>
      <returns>
        <see langword="true" /> if the password successfully verifies the integrity of the <see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Info.AuthenticatedSafe" /> contents; <see langword="false" /> if the password is not correct or the contents have been altered.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12Info.VerifyMac(System.String)">
      <summary>Attempts to verify the integrity of the <see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Info.AuthenticatedSafe" /> contents with a password represented by a <see cref="T:System.String" />.</summary>
      <param name="password">The password to use to attempt to verify integrity.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Info.IntegrityMode" /> value is not <see cref="F:System.Security.Cryptography.Pkcs.Pkcs12IntegrityMode.Password" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The hash algorithm option specified by the PKCS#12 PFX contents could not be identified or is not supported by this platform.</exception>
      <returns>
        <see langword="true" /> if the password successfully verifies the integrity of the <see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Info.AuthenticatedSafe" /> contents; <see langword="false" /> if the password is not correct or the contents have been altered.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12Info.AuthenticatedSafe">
      <summary>Gets a read-only collection of the SafeContents values present in the PFX AuthenticatedSafe.</summary>
      <returns>A read-only collection of the SafeContents values present in the PFX AuthenticatedSafe.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12Info.IntegrityMode">
      <summary>Gets a value that indicates the type of tamper protection provided for the <see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Info.AuthenticatedSafe" /> contents.</summary>
      <returns>One of the enumeration members that indicates the type of tamper protection provided for the <see cref="P:System.Security.Cryptography.Pkcs.Pkcs12Info.AuthenticatedSafe" /> contents.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs12IntegrityMode">
      <summary>Represents the type of anti-tampering applied to a PKCS#12 PFX value.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.Pkcs12IntegrityMode.None">
      <summary>The PKCS#12 PFX value is not protected from tampering.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.Pkcs12IntegrityMode.Password">
      <summary>The PKCS#12 PFX value is protected from tampering with a Message Authentication Code (MAC) keyed with a password.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.Pkcs12IntegrityMode.PublicKey">
      <summary>The PKCS#12 PFX value is protected from tampering with a digital signature using public key cryptography.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.Pkcs12IntegrityMode.Unknown">
      <summary>The type of anti-tampering applied to the PKCS#12 PFX is unknown or could not be determined.</summary>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs12KeyBag">
      <summary>Represents the KeyBag from PKCS#12, a container whose contents are a PKCS#8 PrivateKeyInfo. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12KeyBag.#ctor(System.ReadOnlyMemory{System.Byte},System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12KeyBag" /> from an existing encoded PKCS#8 PrivateKeyInfo value.</summary>
      <param name="pkcs8PrivateKey">A BER-encoded PKCS#8 PrivateKeyInfo value.</param>
      <param name="skipCopy">
        <see langword="true" /> to store <paramref name="pkcs8PrivateKey" /> without making a defensive copy; otherwise, <see langword="false" />. The default is <see langword="false" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="pkcs8privateKey" /> parameter does not represent a single ASN.1 BER-encoded value.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12KeyBag.Pkcs8PrivateKey">
      <summary>Gets a memory value containing the PKCS#8 PrivateKeyInfo value transported by this bag.</summary>
      <returns>A memory value containing the PKCS#8 PrivateKeyInfo value transported by this bag.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs12SafeBag">
      <summary>Defines the core behavior of a SafeBag value from the PKCS#12 specification and provides a base for derived classes.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeBag.#ctor(System.String,System.ReadOnlyMemory{System.Byte},System.Boolean)">
      <summary>Called from constructors in derived classes to initialize the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12SafeBag" /> class.</summary>
      <param name="bagIdValue">The Object Identifier (OID), in dotted decimal form, indicating the data type of this SafeBag.</param>
      <param name="encodedBagValue">The ASN.1 BER encoded value of the SafeBag contents.</param>
      <param name="skipCopy">
        <see langword="true" /> to store <paramref name="encodedBagValue" /> without making a defensive copy; otherwise, <see langword="false" />. The default is <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="bagIdValue" /> parameter is <see langword="null" /> or the empty string.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="encodedBagValue" /> parameter does not represent a single ASN.1 BER-encoded value.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeBag.Encode">
      <summary>Encodes the SafeBag value and returns it as a byte array.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The object identifier value passed to the constructor was invalid.</exception>
      <returns>A byte array representing the encoded form of the SafeBag.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeBag.GetBagId">
      <summary>Gets the Object Identifier (OID) identifying the content type of this SafeBag.</summary>
      <returns>The Object Identifier (OID) identifying the content type of this SafeBag.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeBag.TryEncode(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to encode the SafeBag value into a provided buffer.</summary>
      <param name="destination">The byte span to receive the encoded SafeBag value.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The object identifier value passed to the constructor was invalid.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12SafeBag.Attributes">
      <summary>Gets the modifiable collection of attributes to encode with the SafeBag value.</summary>
      <returns>The modifiable collection of attributes to encode with the SafeBag value.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12SafeBag.EncodedBagValue">
      <summary>Gets the ASN.1 BER encoding of the contents of this SafeBag.</summary>
      <returns>The ASN.1 BER encoding of the contents of this SafeBag.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs12SafeContents">
      <summary>Represents a PKCS#12 SafeContents value. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12SafeContents" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.AddCertificate(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Adds a certificate to the SafeContents via a new <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12CertBag" /> and returns the newly created bag instance.</summary>
      <param name="certificate">The certificate to add.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="certificate" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">This instance is read-only.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="certificate" /> parameter is in an invalid state.</exception>
      <returns>The bag instance which was added to the SafeContents.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.AddKeyUnencrypted(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Adds an asymmetric private key to the SafeContents via a new <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12KeyBag" /> and returns the newly created bag instance.</summary>
      <param name="key">The asymmetric private key to add.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">This instance is read-only.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key export failed.</exception>
      <returns>The bag instance which was added to the SafeContents.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.AddNestedContents(System.Security.Cryptography.Pkcs.Pkcs12SafeContents)">
      <summary>Adds a nested SafeContents to the SafeContents via a new <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12SafeContentsBag" /> and returns the newly created bag instance.</summary>
      <param name="safeContents">The nested contents to add to the SafeContents.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="safeContents" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="safeContents" /> parameter is encrypted.</exception>
      <exception cref="T:System.InvalidOperationException">This instance is read-only.</exception>
      <returns>The bag instance which was added to the SafeContents.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.AddSafeBag(System.Security.Cryptography.Pkcs.Pkcs12SafeBag)">
      <summary>Adds a SafeBag to the SafeContents.</summary>
      <param name="safeBag">The SafeBag value to add.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="safeBag" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">This instance is read-only.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.AddSecret(System.Security.Cryptography.Oid,System.ReadOnlyMemory{System.Byte})">
      <summary>Adds an ASN.1 BER-encoded value with a specified type identifier to the SafeContents via a new <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12SecretBag" /> and returns the newly created bag instance.</summary>
      <param name="secretType">The Object Identifier (OID) which identifies the data type of the secret value.</param>
      <param name="secretValue">The BER-encoded value representing the secret to add.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="secretType" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">This instance is read-only.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="secretValue" /> parameter does not represent a single ASN.1 BER-encoded value.</exception>
      <returns>The bag instance which was added to the SafeContents.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.AddShroudedKey(System.Security.Cryptography.AsymmetricAlgorithm,System.Byte[],System.Security.Cryptography.PbeParameters)">
      <summary>Adds an encrypted asymmetric private key to the SafeContents via a new <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12ShroudedKeyBag" /> from a byte-based password in an array and returns the newly created bag instance.</summary>
      <param name="key">The asymmetric private key to add.</param>
      <param name="passwordBytes">The bytes to use as a password when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">This instance is read-only.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key export failed.</exception>
      <returns>The bag instance which was added to the SafeContents.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.AddShroudedKey(System.Security.Cryptography.AsymmetricAlgorithm,System.ReadOnlySpan{System.Byte},System.Security.Cryptography.PbeParameters)">
      <summary>Adds an encrypted asymmetric private key to the SafeContents via a new <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12ShroudedKeyBag" /> from a byte-based password in a span and returns the newly created bag instance.</summary>
      <param name="key">The asymmetric private key to add.</param>
      <param name="passwordBytes">The bytes to use as a password when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">This instance is read-only.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key export failed.</exception>
      <returns>The bag instance which was added to the SafeContents.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.AddShroudedKey(System.Security.Cryptography.AsymmetricAlgorithm,System.ReadOnlySpan{System.Char},System.Security.Cryptography.PbeParameters)">
      <summary>Adds an encrypted asymmetric private key to the SafeContents via a new <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12ShroudedKeyBag" /> from a character-based password in a span and returns the newly created bag instance.</summary>
      <param name="key">The asymmetric private key to add.</param>
      <param name="password">The password to use when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">This instance is read-only.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key export failed.</exception>
      <returns>The bag instance which was added to the SafeContents.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.AddShroudedKey(System.Security.Cryptography.AsymmetricAlgorithm,System.String,System.Security.Cryptography.PbeParameters)">
      <summary>Adds an encrypted asymmetric private key to the SafeContents via a new <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12ShroudedKeyBag" /> from a character-based password in a string and returns the newly created bag instance.</summary>
      <param name="key">The asymmetric private key to add.</param>
      <param name="password">The password to use when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">This instance is read-only.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key export failed.</exception>
      <returns>The bag instance which was added to the SafeContents.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.Decrypt(System.Byte[])">
      <summary>Decrypts the contents of this SafeContents value using a byte-based password from an array.</summary>
      <param name="passwordBytes">The bytes to use as a password for decrypting the encrypted contents.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.ConfidentialityMode" /> property is not <see cref="F:System.Security.Cryptography.Pkcs.Pkcs12ConfidentialityMode.Password" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents were not successfully decrypted.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.Decrypt(System.ReadOnlySpan{System.Byte})">
      <summary>Decrypts the contents of this SafeContents value using a byte-based password from a span.</summary>
      <param name="passwordBytes">The bytes to use as a password for decrypting the encrypted contents.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.ConfidentialityMode" /> property is not <see cref="F:System.Security.Cryptography.Pkcs.Pkcs12ConfidentialityMode.Password" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents were not successfully decrypted.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.Decrypt(System.ReadOnlySpan{System.Char})">
      <summary>Decrypts the contents of this SafeContents value using a character-based password from a span.</summary>
      <param name="password">The password to use for decrypting the encrypted contents.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.ConfidentialityMode" /> property is not <see cref="F:System.Security.Cryptography.Pkcs.Pkcs12ConfidentialityMode.Password" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents were not successfully decrypted.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.Decrypt(System.String)">
      <summary>Decrypts the contents of this SafeContents value using a character-based password from a string.</summary>
      <param name="password">The password to use for decrypting the encrypted contents.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.ConfidentialityMode" /> property is not <see cref="F:System.Security.Cryptography.Pkcs.Pkcs12ConfidentialityMode.Password" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents were not successfully decrypted.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.GetBags">
      <summary>Gets an enumerable representation of the SafeBag values contained within the SafeContents.</summary>
      <exception cref="T:System.InvalidOperationException">The contents are encrypted.</exception>
      <returns>An enumerable representation of the SafeBag values contained within the SafeContents.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.ConfidentialityMode">
      <summary>Gets a value that indicates the type of encryption applied to the contents.</summary>
      <returns>One of the enumeration values that indicates the type of encryption applied to the contents. The default value is <see cref="F:System.Security.Cryptography.Pkcs.Pkcs12ConfidentialityMode.None" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12SafeContents.IsReadOnly">
      <summary>Gets a value that indicates whether this instance in a read-only state.</summary>
      <returns>
        <see langword="true" /> if this value is in a read-only state; otherwise, <see langword="false" />. The default value is <see langword="false" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs12SafeContentsBag">
      <summary>Represents the SafeContentsBag from PKCS#12, a container whose contents are a PKCS#12 SafeContents value. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12SafeContentsBag.SafeContents">
      <summary>Gets the SafeContents value contained within this bag.</summary>
      <returns>The SafeContents value contained within this bag.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs12SecretBag">
      <summary>Represents the SecretBag from PKCS#12, a container whose contents are arbitrary data with a type identifier. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12SecretBag.GetSecretType">
      <summary>Gets the Object Identifier (OID) which identifies the data type of the secret value.</summary>
      <returns>The Object Identifier (OID) which identifies the data type of the secret value.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12SecretBag.SecretValue">
      <summary>Gets a memory value containing the BER-encoded contents of the bag.</summary>
      <returns>A memory value containing the BER-encoded contents of the bag.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs12ShroudedKeyBag">
      <summary>Represents the ShroudedKeyBag from PKCS#12, a container whose contents are a PKCS#8 EncryptedPrivateKeyInfo. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs12ShroudedKeyBag.#ctor(System.ReadOnlyMemory{System.Byte},System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs12ShroudedKeyBag" /> from an existing encoded PKCS#8 EncryptedPrivateKeyInfo value.</summary>
      <param name="encryptedPkcs8PrivateKey">A BER-encoded PKCS#8 EncryptedPrivateKeyInfo value.</param>
      <param name="skipCopy">
        <see langword="true" /> to store <paramref name="encryptedPkcs8PrivateKey" /> without making a defensive copy; otherwise, <see langword="false" />. The default is <see langword="false" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="encryptedPkcs8privateKey" /> parameter does not represent a single ASN.1 BER-encoded value.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs12ShroudedKeyBag.EncryptedPkcs8PrivateKey">
      <summary>Gets a memory value containing the PKCS#8 EncryptedPrivateKeyInfo value transported by this bag.</summary>
      <returns>A memory value containing the PKCS#8 EncryptedPrivateKeyInfo value transported by this bag.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo">
      <summary>Enables the inspection of and creation of PKCS#8 PrivateKeyInfo and EncryptedPrivateKeyInfo values. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.#ctor(System.Security.Cryptography.Oid,System.Nullable{System.ReadOnlyMemory{System.Byte}},System.ReadOnlyMemory{System.Byte},System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo" /> class.</summary>
      <param name="algorithmId">The Object Identifier (OID) identifying the asymmetric algorithm this key is for.</param>
      <param name="algorithmParameters">The BER-encoded algorithm parameters associated with this key, or <see langword="null" /> to omit algorithm parameters when encoding.</param>
      <param name="privateKey">The algorithm-specific encoded private key.</param>
      <param name="skipCopies">
        <see langword="true" /> to store <paramref name="algorithmParameters" /> and <paramref name="privateKey" /> without making a defensive copy; otherwise, <see langword="false" />. The default is <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="algorithmId" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="algorithmParameters" /> parameter is not <see langword="null" />, empty, or a single BER-encoded value.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.Create(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Exports a specified key as a PKCS#8 PrivateKeyInfo and returns its decoded interpretation.</summary>
      <param name="privateKey">The private key to represent in a PKCS#8 PrivateKeyInfo.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="privateKey" /> parameter is <see langword="null" />.</exception>
      <returns>The decoded interpretation of the exported PKCS#8 PrivateKeyInfo.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.Decode(System.ReadOnlyMemory{System.Byte},System.Int32@,System.Boolean)">
      <summary>Reads the provided data as a PKCS#8 PrivateKeyInfo and returns an object view of the contents.</summary>
      <param name="source">The data to interpret as a PKCS#8 PrivateKeyInfo value.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <param name="skipCopy">
        <see langword="true" /> to store <paramref name="source" /> without making a defensive copy; otherwise, <see langword="false" />. The default is <see langword="false" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of the <paramref name="source" /> parameter were not successfully decoded as a PKCS#8 PrivateKeyInfo.</exception>
      <returns>An object view of the contents decoded as a PKCS#8 PrivateKeyInfo.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.DecryptAndDecode(System.ReadOnlySpan{System.Byte},System.ReadOnlyMemory{System.Byte},System.Int32@)">
      <summary>Decrypts the provided data using the provided byte-based password and decodes the output into an object view of the PKCS#8 PrivateKeyInfo.</summary>
      <param name="passwordBytes">The bytes to use as a password when decrypting the key material.</param>
      <param name="source">The data to read as a PKCS#8 EncryptedPrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents of <paramref name="source" /> indicate the Key Derivation Function (KDF) to apply is the legacy PKCS#12 KDF, which requires <see cref="T:System.Char" />-based passwords.

-or-

The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.</exception>
      <returns>An object view of the contents decrypted decoded as a PKCS#8 PrivateKeyInfo.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.DecryptAndDecode(System.ReadOnlySpan{System.Char},System.ReadOnlyMemory{System.Byte},System.Int32@)">
      <summary>Decrypts the provided data using the provided character-based password and decodes the output into an object view of the PKCS#8 PrivateKeyInfo.</summary>
      <param name="password">The password to use when decrypting the key material.</param>
      <param name="source">The bytes of a PKCS#8 EncryptedPrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <returns>An object view of the contents decrypted decoded as a PKCS#8 PrivateKeyInfo.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.Encode">
      <summary>Encodes the property data of this instance as a PKCS#8 PrivateKeyInfo and returns the encoding as a byte array.</summary>
      <returns>A byte array representing the encoded form of the PKCS#8 PrivateKeyInfo.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.Encrypt(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.PbeParameters)">
      <summary>Produces a PKCS#8 EncryptedPrivateKeyInfo from the property contents of this object after encrypting with the specified byte-based password and encryption parameters.</summary>
      <param name="passwordBytes">The bytes to use as a password when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="pbeParameters" /> indicates that <see cref="F:System.Security.Cryptography.PbeEncryptionAlgorithm.TripleDes3KeyPkcs12" /> should be used, which requires <see cref="T:System.Char" />-based passwords.</exception>
      <returns>A byte array containing the encoded form of the PKCS#8 EncryptedPrivateKeyInfo.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.Encrypt(System.ReadOnlySpan{System.Char},System.Security.Cryptography.PbeParameters)">
      <summary>Produces a PKCS#8 EncryptedPrivateKeyInfo from the property contents of this object after encrypting with the specified character-based password and encryption parameters.</summary>
      <param name="password">The password to use when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <returns>A byte array containing the encoded form of the PKCS#8 EncryptedPrivateKeyInfo.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.TryEncode(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to encode the property data of this instance as a PKCS#8 PrivateKeyInfo, writing the results into a provided buffer.</summary>
      <param name="destination">The byte span to receive the PKCS#8 PrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.TryEncrypt(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.PbeParameters,System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to produce a PKCS#8 EncryptedPrivateKeyInfo from the property contents of this object after encrypting with the specified byte-based password and encryption parameters, writing the results into a provided buffer.</summary>
      <param name="passwordBytes">The bytes to use as a password when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <param name="destination">The byte span to receive the PKCS#8 EncryptedPrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.TryEncrypt(System.ReadOnlySpan{System.Char},System.Security.Cryptography.PbeParameters,System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to produce a PKCS#8 EncryptedPrivateKeyInfo from the property contents of this object after encrypting with the specified character-based password and encryption parameters, writing the result into a provided buffer.</summary>
      <param name="password">The password to use when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <param name="destination">The byte span to receive the PKCS#8 EncryptedPrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.AlgorithmId">
      <summary>Gets the Object Identifier (OID) value identifying the algorithm this key is for.</summary>
      <returns>The Object Identifier (OID) value identifying the algorithm this key is for.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.AlgorithmParameters">
      <summary>Gets a memory value containing the BER-encoded algorithm parameters associated with this key.</summary>
      <returns>A memory value containing the BER-encoded algorithm parameters associated with this key, or <see langword="null" /> if no parameters were present.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.Attributes">
      <summary>Gets the modifiable collection of attributes for this private key.</summary>
      <returns>The modifiable collection of attributes to encode with the private key.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs8PrivateKeyInfo.PrivateKeyBytes">
      <summary>Gets a memory value that represents the algorithm-specific encoded private key.</summary>
      <returns>A memory value that represents the algorithm-specific encoded private key.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject">
      <summary>Represents an attribute used for CMS/PKCS #7 and PKCS #9 operations.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject" /> class using a specified <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object as its attribute type and value.</summary>
      <param name="asnEncodedData">An object that contains the PKCS #9 attribute type and value to use.</param>
      <exception cref="T:System.ArgumentException">The length of the <paramref name="Value" /> member of the <paramref name="Oid" /> member of <paramref name="asnEncodedData" /> is zero.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="Oid" /> member of <paramref name="asnEncodedData" /> is <see langword="null" />.  
  
 -or-  
  
 The <paramref name="Value" /> member of the <paramref name="Oid" /> member of <paramref name="asnEncodedData" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject.#ctor(System.Security.Cryptography.Oid,System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject" /> class using a specified <see cref="T:System.Security.Cryptography.Oid" /> object as the attribute type and a specified ASN.1 encoded data as the attribute value.</summary>
      <param name="oid">An object that represents the PKCS #9 attribute type.</param>
      <param name="encodedData">An array of byte values that represents the PKCS #9 attribute value.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject.#ctor(System.String,System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject" /> class using a specified string representation of an object identifier (OID) as the attribute type and a specified ASN.1 encoded data as the attribute value.</summary>
      <param name="oid">The string representation of an OID that represents the PKCS #9 attribute type.</param>
      <param name="encodedData">An array of byte values that contains the PKCS #9 attribute value.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Copies a PKCS #9 attribute type and value for this <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject" /> from the specified <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="asnEncodedData">An object that contains the PKCS #9 attribute type and value to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="asnEncodeData" /> does not represent a compatible attribute type.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject.Oid">
      <summary>Gets an <see cref="T:System.Security.Cryptography.Oid" /> object that represents the type of attribute associated with this <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject" /> object.</summary>
      <returns>An object that represents the type of attribute associated with this <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9AttributeObject" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs9ContentType">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9ContentType" /> class defines the type of the content of a CMS/PKCS #7 message.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9ContentType.#ctor">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.Pkcs9ContentType.#ctor" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9ContentType" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9ContentType.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Copies information from an <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="asnEncodedData">The <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object from which to copy information.</param>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs9ContentType.ContentType">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.Pkcs9ContentType.ContentType" /> property gets an <see cref="T:System.Security.Cryptography.Oid" /> object that contains the content type.</summary>
      <returns>An  <see cref="T:System.Security.Cryptography.Oid" /> object that contains the content type.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription" /> class defines the description of the content of a CMS/PKCS #7 message.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription.#ctor">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription.#ctor" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription.#ctor(System.Byte[])">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription.#ctor(System.Byte[])" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription" /> class by using the specified array of byte values as the encoded description of the content of a CMS/PKCS #7 message.</summary>
      <param name="encodedDocumentDescription">An array of byte values that specifies the encoded description of the CMS/PKCS #7 message.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription.#ctor(System.String)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription.#ctor(System.String)" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription" /> class by using the specified description of the content of a CMS/PKCS #7 message.</summary>
      <param name="documentDescription">An instance of the <see cref="T:System.String" /> class that specifies the description for the CMS/PKCS #7 message.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Copies information from an <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="asnEncodedData">The <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object from which to copy information.</param>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription.DocumentDescription">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.Pkcs9DocumentDescription.DocumentDescription" /> property retrieves the document description.</summary>
      <returns>A <see cref="T:System.String" /> object that contains the document description.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs9DocumentName">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9DocumentName" /> class defines the name of a CMS/PKCS #7 message.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentName.#ctor">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentName.#ctor" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9DocumentName" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentName.#ctor(System.Byte[])">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentName.#ctor(System.Byte[])" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9DocumentName" /> class by using the specified array of byte values as the encoded name of the content of a CMS/PKCS #7 message.</summary>
      <param name="encodedDocumentName">An array of byte values that specifies the encoded name of the CMS/PKCS #7 message.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentName.#ctor(System.String)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentName.#ctor(System.String)" /> constructor creates an instance of the  <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9DocumentName" /> class by using the specified name for the CMS/PKCS #7 message.</summary>
      <param name="documentName">A  <see cref="T:System.String" /> object that specifies the name for the CMS/PKCS #7 message.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9DocumentName.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Copies information from an <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="asnEncodedData">The <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object from which to copy information.</param>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs9DocumentName.DocumentName">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.Pkcs9DocumentName.DocumentName" /> property retrieves the document name.</summary>
      <returns>A <see cref="T:System.String" /> object that contains the document name.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs9LocalKeyId">
      <summary>Represents the LocalKeyId attribute from PKCS#9.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9LocalKeyId.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9LocalKeyId" /> class with an empty key identifier value.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9LocalKeyId.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9LocalKeyId" /> class with a key identifier specified by a byte array.</summary>
      <param name="keyId">A byte array containing the key identifier.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9LocalKeyId.#ctor(System.ReadOnlySpan{System.Byte})">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9LocalKeyId" /> class with a key identifier specified by a byte span.</summary>
      <param name="keyId">A byte array containing the key identifier.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9LocalKeyId.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Copies information from a <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="asnEncodedData">The <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object from which to copy information.</param>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs9LocalKeyId.KeyId">
      <summary>Gets a memory value containing the key identifier from this attribute.</summary>
      <returns>A memory value containing the key identifier from this attribute.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs9MessageDigest">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9MessageDigest" /> class defines the message digest of a CMS/PKCS #7 message.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9MessageDigest.#ctor">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.Pkcs9MessageDigest.#ctor" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9MessageDigest" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9MessageDigest.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Copies information from an <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="asnEncodedData">The <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object from which to copy information.</param>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs9MessageDigest.MessageDigest">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.Pkcs9MessageDigest.MessageDigest" /> property retrieves the message digest.</summary>
      <returns>An array of byte values that contains the message digest.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Pkcs9SigningTime">
      <summary>Defines the signing date and time of a signature. A <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9SigningTime" /> object can be used as an authenticated attribute of a <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> object when an authenticated date and time are to accompany a digital signature.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9SigningTime.#ctor">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.Pkcs9SigningTime.#ctor" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9SigningTime" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9SigningTime.#ctor(System.Byte[])">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.Pkcs9SigningTime.#ctor(System.Byte[])" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9SigningTime" /> class by using the specified array of byte values as the encoded signing date and time of the content of a CMS/PKCS #7 message.</summary>
      <param name="encodedSigningTime">An array of byte values that specifies the encoded signing date and time of the CMS/PKCS #7 message.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9SigningTime.#ctor(System.DateTime)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.Pkcs9SigningTime.#ctor(System.DateTime)" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.Pkcs9SigningTime" /> class by using the specified signing date and time.</summary>
      <param name="signingTime">A <see cref="T:System.DateTime" /> structure that represents the signing date and time of the signature.</param>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Pkcs9SigningTime.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Copies information from a <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="asnEncodedData">The <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object from which to copy information.</param>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Pkcs9SigningTime.SigningTime">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.Pkcs9SigningTime.SigningTime" /> property retrieves a <see cref="T:System.DateTime" /> structure that represents the date and time that the message was signed.</summary>
      <returns>A <see cref="T:System.DateTime" /> structure that contains the date and time the document was signed.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.PublicKeyInfo">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.PublicKeyInfo" /> class represents information associated with a public key.</summary>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.PublicKeyInfo.Algorithm">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.PublicKeyInfo.Algorithm" /> property retrieves the algorithm identifier associated with the public key.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.Pkcs.AlgorithmIdentifier" /> object that represents the algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.PublicKeyInfo.KeyValue">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.PublicKeyInfo.KeyValue" /> property retrieves the value of the encoded public component of the public key pair.</summary>
      <returns>An array of byte values  that represents the encoded public component of the public key pair.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.RecipientInfo">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> class represents information about a CMS/PKCS #7 message recipient. The <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> class is an abstract class inherited by the <see cref="T:System.Security.Cryptography.Pkcs.KeyAgreeRecipientInfo" /> and <see cref="T:System.Security.Cryptography.Pkcs.KeyTransRecipientInfo" /> classes.</summary>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.RecipientInfo.EncryptedKey">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.RecipientInfo.EncryptedKey" /> abstract property retrieves the encrypted recipient keying material.</summary>
      <returns>An array of byte values that contain the encrypted recipient keying material.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.RecipientInfo.KeyEncryptionAlgorithm">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.RecipientInfo.KeyEncryptionAlgorithm" /> abstract property retrieves the algorithm used to perform the key establishment.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.Pkcs.AlgorithmIdentifier" /> object that contains the value of the algorithm used to establish the key between the originator and recipient of the CMS/PKCS #7 message.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.RecipientInfo.RecipientIdentifier">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.RecipientInfo.RecipientIdentifier" /> abstract property retrieves the identifier of the recipient.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifier" /> object that contains the identifier of the recipient.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.RecipientInfo.Type">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.RecipientInfo.Type" /> property retrieves the type of the recipient. The type of the recipient determines which of two major protocols is used to establish a key between the originator and the recipient of a CMS/PKCS #7 message.</summary>
      <returns>A value of the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoType" /> enumeration that defines the type of the recipient.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.RecipientInfo.Version">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.RecipientInfo.Version" /> abstract property retrieves the version of the recipient information. Derived classes automatically set this property for their objects, and the value indicates whether it is using PKCS #7 or Cryptographic Message Syntax (CMS) to protect messages. The version also implies whether the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> object establishes a cryptographic key by a key agreement algorithm or a key transport algorithm.</summary>
      <returns>An <see cref="T:System.Int32" /> value that represents the version of the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> class represents a collection of <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> objects. <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> implements the <see cref="T:System.Collections.ICollection" /> interface.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.RecipientInfoCollection.CopyTo(System.Array,System.Int32)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.RecipientInfoCollection.CopyTo(System.Array,System.Int32)" /> method copies the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection to an array.</summary>
      <param name="array">An <see cref="T:System.Array" /> object to which  the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection is to be copied.</param>
      <param name="index">The zero-based index in <paramref name="array" /> where the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection is copied.</param>
      <exception cref="T:System.ArgumentException">One of the arguments provided to a method was not valid.</exception>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of an argument was outside the allowable range of values as defined by the called method.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.RecipientInfoCollection.CopyTo(System.Security.Cryptography.Pkcs.RecipientInfo[],System.Int32)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.RecipientInfoCollection.CopyTo(System.Security.Cryptography.Pkcs.RecipientInfo[],System.Int32)" /> method copies the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection to a <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> array.</summary>
      <param name="array">An array of <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> objects where the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection is to be copied.</param>
      <param name="index">The zero-based index in <paramref name="array" /> where the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection is copied.</param>
      <exception cref="T:System.ArgumentException">One of the arguments provided to a method was not valid.</exception>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of an argument was outside the allowable range of values as defined by the called method.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.RecipientInfoCollection.GetEnumerator">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.RecipientInfoCollection.GetEnumerator" /> method returns a <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator" /> object for the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator" /> object that can be used to enumerate the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.RecipientInfoCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.RecipientInfoCollection.System#Collections#IEnumerable#GetEnumerator" /> method returns a <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator" /> object for the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator" /> object that can be used to enumerate the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.RecipientInfoCollection.Count">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.RecipientInfoCollection.Count" /> property retrieves the number of items in the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</summary>
      <returns>An int value that represents the number of items in the collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.RecipientInfoCollection.IsSynchronized">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.RecipientInfoCollection.IsSynchronized" /> property retrieves whether access to the collection is synchronized, or thread safe. This property always returns <see langword="false" />, which means the collection is not thread safe.</summary>
      <returns>A <see cref="T:System.Boolean" /> value of <see langword="false" />, which means the collection is not thread safe.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.RecipientInfoCollection.Item(System.Int32)">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.RecipientInfoCollection.Item(System.Int32)" /> property retrieves the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> object at the specified index in the collection.</summary>
      <param name="index">An int value that represents the index in the collection. The index is zero based.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of an argument was outside the allowable range of values as defined by the called method.</exception>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> object at the specified index.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.RecipientInfoCollection.SyncRoot">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.RecipientInfoCollection.SyncRoot" /> property retrieves an <see cref="T:System.Object" /> object used to synchronize access to the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</summary>
      <returns>An <see cref="T:System.Object" /> object used to synchronize access to the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator" /> class provides enumeration functionality for the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection. <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator" /> implements the <see cref="T:System.Collections.IEnumerator" /> interface.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator.MoveNext">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator.MoveNext" /> method advances the enumeration to the next <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> object in the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</summary>
      <returns>This method returns a bool that specifies whether the enumeration successfully advanced. If the enumeration successfully moved to the next <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> object, the method returns <see langword="true" />. If the enumeration moved past the last item in the enumeration, it returns <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator.Reset">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator.Reset" /> method resets the enumeration to the first <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> object in the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</summary>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator.Current">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator.Current" /> property retrieves the current <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> object from the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> object that represents the current recipient information structure in the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator.System#Collections#IEnumerator#Current">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.RecipientInfoEnumerator.System#Collections#IEnumerator#Current" /> property retrieves the current <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> object from the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfo" /> object that represents the current recipient information structure in the <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoCollection" /> collection.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.RecipientInfoType">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.RecipientInfoType" /> enumeration defines the types of recipient information.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.RecipientInfoType.KeyAgreement">
      <summary>Key agreement recipient information.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.RecipientInfoType.KeyTransport">
      <summary>Key transport recipient information.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.RecipientInfoType.Unknown">
      <summary>The recipient information type is unknown.</summary>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest">
      <summary>Represents a time-stamping request from IETF RFC 3161.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.CreateFromData(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.Oid,System.Nullable{System.ReadOnlyMemory{System.Byte}},System.Boolean,System.Security.Cryptography.X509Certificates.X509ExtensionCollection)">
      <summary>Creates a timestamp request by hashing the provided data with a specified algorithm.</summary>
      <param name="data">The data to timestamp, which will be hashed by this method.</param>
      <param name="hashAlgorithm">The hash algorithm to use with this timestamp request.</param>
      <param name="requestedPolicyId">The Object Identifier (OID) for a timestamp policy the Timestamp Authority (TSA) should use, or <see langword="null" /> to express no preference.</param>
      <param name="nonce">An optional nonce (number used once) to uniquely identify this request to pair it with the response. The value is interpreted as an unsigned big-endian integer and may be normalized to the encoding format.</param>
      <param name="requestSignerCertificates">
        <see langword="true" /> to indicate the Timestamp Authority (TSA) must include the signing certificate in the issued timestamp token; otherwise, <see langword="false" />.</param>
      <param name="extensions">An optional collection of extensions to include in the request.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is not a known hash algorithm.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest" /> representing the chosen values.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.CreateFromHash(System.ReadOnlyMemory{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.Oid,System.Nullable{System.ReadOnlyMemory{System.Byte}},System.Boolean,System.Security.Cryptography.X509Certificates.X509ExtensionCollection)">
      <summary>Create a timestamp request using a pre-computed hash value and the name of the hash algorithm.</summary>
      <param name="hash">The pre-computed hash value to be timestamped.</param>
      <param name="hashAlgorithm">The hash algorithm used to produce <paramref name="hash" />.</param>
      <param name="requestedPolicyId">The Object Identifier (OID) for the timestamp policy that the Timestamp Authority (TSA) should use, or <see langword="null" /> to express no preference.</param>
      <param name="nonce">An optional value used to uniquely match a request to a response, or <see langword="null" /> to not include a nonce in the request.</param>
      <param name="requestSignerCertificates">
        <see langword="true" /> to indicate the Timestamp Authority (TSA) must include the signing certificate in the issued timestamp token; otherwise, <see langword="false" />.</param>
      <param name="extensions">An optional collection of extensions to include in the request.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is not a known hash algorithm.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest" /> representing the chosen values.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.CreateFromHash(System.ReadOnlyMemory{System.Byte},System.Security.Cryptography.Oid,System.Security.Cryptography.Oid,System.Nullable{System.ReadOnlyMemory{System.Byte}},System.Boolean,System.Security.Cryptography.X509Certificates.X509ExtensionCollection)">
      <summary>Create a timestamp request using a pre-computed hash value and the Object Identifier for the hash algorithm.</summary>
      <param name="hash">The pre-computed hash value to be timestamped.</param>
      <param name="hashAlgorithmId">The Object Identifier (OID) for the hash algorithm that produced <paramref name="hash" />.</param>
      <param name="requestedPolicyId">The Object Identifier (OID) for a timestamp policy the Timestamp Authority (TSA) should use, or <see langword="null" /> to express no preference.</param>
      <param name="nonce">An optional nonce (number used once) to uniquely identify this request to pair it with the response. The value is interpreted as an unsigned big-endian integer and may be normalized to the encoding format.</param>
      <param name="requestSignerCertificates">
        <see langword="true" /> to indicate the Timestamp Authority (TSA) must include the signing certificate in the issued timestamp token; otherwise, <see langword="false" />.</param>
      <param name="extensions">An optional collection of extensions to include in the request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="hashAlgorithmId" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithmId" />.<see cref="P:System.Security.Cryptography.Oid.Value" /> is not a valid OID.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest" /> representing the chosen values.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.CreateFromSignerInfo(System.Security.Cryptography.Pkcs.SignerInfo,System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.Oid,System.Nullable{System.ReadOnlyMemory{System.Byte}},System.Boolean,System.Security.Cryptography.X509Certificates.X509ExtensionCollection)">
      <summary>Creates a timestamp request by hashing the signature of the provided signer with a specified algorithm.</summary>
      <param name="signerInfo">The CMS signer information to build a timestamp request for.</param>
      <param name="hashAlgorithm">The hash algorithm to use with this timestamp request.</param>
      <param name="requestedPolicyId">The Object Identifier (OID) for the timestamp policy that the Timestamp Authority (TSA) should use, or <see langword="null" /> to express no preference.</param>
      <param name="nonce">An optional nonce (number used once) to uniquely identify this request to pair it with the response. The value is interpreted as an unsigned big-endian integer and may be normalized to the encoding format.</param>
      <param name="requestSignerCertificates">
        <see langword="true" /> to indicate the Timestamp Authority (TSA) must include the signing certificate in the issued timestamp token; otherwise, <see langword="false" />.</param>
      <param name="extensions">An optional collection of extensions to include in the request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="signerInfo" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is not a known hash algorithm.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest" /> representing the chosen values.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.Encode">
      <summary>Encodes the timestamp request and returns it as a byte array.</summary>
      <returns>A byte array containing the DER-encoded timestamp request.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.GetExtensions">
      <summary>Gets a collection with a copy of the extensions present on this request.</summary>
      <returns>A collection with a copy of the extensions present on this request.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.GetMessageHash">
      <summary>Gets the data hash for this timestamp request.</summary>
      <returns>The data hash for this timestamp request as a read-only memory value.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.GetNonce">
      <summary>Gets the nonce for this timestamp request.</summary>
      <returns>The nonce for this timestamp request as a read-only memory value, if one was present; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.ProcessResponse(System.ReadOnlyMemory{System.Byte},System.Int32@)">
      <summary>Combines an encoded timestamp response with this request to produce a <see cref="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampToken" />.</summary>
      <param name="responseBytes">The DER encoded timestamp response.</param>
      <param name="bytesConsumed">When this method returns, the number of bytes that were read from <paramref name="responseBytes" />. This parameter is treated as uninitialized.</param>
      <returns>The timestamp token from the response that corresponds to this request.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.TryDecode(System.ReadOnlyMemory{System.Byte},System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest@,System.Int32@)">
      <summary>Attemps to interpret the contents of <paramref name="encodedBytes" /> as a DER-encoded Timestamp Request.</summary>
      <param name="encodedBytes">The buffer containing a DER-encoded timestamp request.</param>
      <param name="request">When this method returns, the successfully decoded timestamp request if decoding succeeded, or <see langword="null" /> if decoding failed. This parameter is treated as uninitialized.</param>
      <param name="bytesConsumed">When this method returns, the number of bytes that were read from <paramref name="encodedBytes" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" /> if <paramref name="encodedBytes" /> was successfully interpreted as a Timestamp Request; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.TryEncode(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to encode the instance as an IETF RFC 3161 <c>TimeStampReq</c>, writing the bytes into the provided buffer.</summary>
      <param name="destination">The buffer to receive the encoded request.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" />if <paramref name="destination" /> is long enough to receive the encoded request; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.HasExtensions">
      <summary>Indicates whether or not the request has extensions.</summary>
      <returns>
        <see langword="true" /> if the request has any extensions; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.HashAlgorithmId">
      <summary>Gets the Object Identifier (OID) for the hash algorithm associated with the request.</summary>
      <returns>The Object Identifier (OID) for the hash algorithm associated with the request.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.RequestedPolicyId">
      <summary>Gets the policy ID for the request, or <see langword="null" /> when no policy ID was requested.</summary>
      <returns>The policy ID for the request, or <see langword="null" /> when no policy ID was requested.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.RequestSignerCertificate">
      <summary>Gets a value indicating whether or not the request indicated that the timestamp authority certificate is required to be in the response.</summary>
      <returns>
        <see langword="true" /> if the response must include the timestamp authority certificate; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampRequest.Version">
      <summary>Gets the data format version number for this request.</summary>
      <returns>The data format version number for this request.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampToken">
      <summary>Represents a time-stamp token from IETF RFC 3161.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampToken.AsSignedCms">
      <summary>Gets a Signed Cryptographic Message Syntax (CMS) representation of the RFC3161 time-stamp token.</summary>
      <returns>The <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> representation of the <see cref="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampToken" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampToken.TryDecode(System.ReadOnlyMemory{System.Byte},System.Security.Cryptography.Pkcs.Rfc3161TimestampToken@,System.Int32@)">
      <summary>Attemps to interpret the contents of <paramref name="encodedBytes" /> as a DER-encoded time-stamp token.</summary>
      <param name="encodedBytes">The buffer containing a DER-encoded time-stamp token.</param>
      <param name="token">When this method returns, the successfully decoded time-stamp token if decoding succeeded, or <see langword="null" /> if decoding failed. This parameter is treated as uninitialized.</param>
      <param name="bytesConsumed">When this method returns, the number of bytes that were read from <paramref name="encodedBytes" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" /> if <paramref name="encodedBytes" /> was successfully interpreted as a time-stamp token; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampToken.VerifySignatureForData(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.X509Certificates.X509Certificate2@,System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Verifies that the current token is a valid time-stamp token for the provided data.</summary>
      <param name="data">The data to verify against this time-stamp token.</param>
      <param name="signerCertificate">When this method returns, the certificate from the Timestamp Authority (TSA) which signed this token, or <see langword="null" /> if a signer certificate cannot be determined. This parameter is treated as uninitialized.</param>
      <param name="extraCandidates">An optional collection of certificates to consider as the Timestamp Authority (TSA) certificates, in addition to any certificates that may be included within the token.</param>
      <returns>
        <see langword="true" /> if the Timestamp Authority (TSA) certificate was found, the certificate public key validates the token signature, and the token matches the hash for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampToken.VerifySignatureForHash(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.X509Certificates.X509Certificate2@,System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Verifies that the current token is a valid time-stamp token for the provided data hash and algorithm identifier.</summary>
      <param name="hash">The cryptographic hash to verify against this time-stamp token.</param>
      <param name="hashAlgorithm">The algorithm which produced <paramref name="hash" />.</param>
      <param name="signerCertificate">When this method returns, the certificate from the Timestamp Authority (TSA) which signed this token, or <see langword="null" /> if a signer certificate cannot be determined. This parameter is treated as uninitialized.</param>
      <param name="extraCandidates">An optional collection of certificates to consider as the Timestamp Authority (TSA) certificates, in addition to any certificates that may be included within the token.</param>
      <returns>
        <see langword="true" /> if the Timestamp Authority (TSA) certificate was found, the certificate public key validates the token signature, and the token matches the hash for the provided data hash and algorithm; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampToken.VerifySignatureForHash(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.Oid,System.Security.Cryptography.X509Certificates.X509Certificate2@,System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Verifies that the current token is a valid time-stamp token for the provided data hash and algorithm identifier.</summary>
      <param name="hash">The cryptographic hash to verify against this time-stamp token.</param>
      <param name="hashAlgorithmId">The OID of the hash algorithm.</param>
      <param name="signerCertificate">When this method returns, the certificate from the Timestamp Authority (TSA) which signed this token, or <see langword="null" /> if a signer certificate cannot be determined. This parameter is treated as uninitialized.</param>
      <param name="extraCandidates">An optional collection of certificates to consider as the Timestamp Authority (TSA) certificates, in addition to any certificates that may be included within the token.</param>
      <returns>
        <see langword="true" /> if the Timestamp Authority (TSA) certificate was found, the certificate public key validates the token signature, and the token matches the hash for the provided data hash and algorithm; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampToken.VerifySignatureForSignerInfo(System.Security.Cryptography.Pkcs.SignerInfo,System.Security.Cryptography.X509Certificates.X509Certificate2@,System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Verifies that the current token is a valid time-stamp token for the provided <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" />.</summary>
      <param name="signerInfo">The CMS signer information to verify the timestamp was built for.</param>
      <param name="signerCertificate">When this method returns, the certificate from the Timestamp Authority (TSA) that signed this token, or <see langword="null" /> if a signer certificate cannot be determined. This parameter is treated as uninitialized.</param>
      <param name="extraCandidates">An optional collection of certificates to consider as the Timestamp Authority (TSA) certificates, in addition to any certificates that may be included within the token.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="signerInfo" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the Timestamp Authority (TSA) certificate was found, the certificate public key validates the token signature, and the token matches the signature for <paramref name="signerInfo" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampToken.TokenInfo">
      <summary>Gets the details of this time-stamp token as a <see cref="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo" />.</summary>
      <returns>The details of this time-stamp token as a <see cref="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo">
      <summary>Represents the timestamp token information class defined in RFC3161 as TSTInfo.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.#ctor(System.Security.Cryptography.Oid,System.Security.Cryptography.Oid,System.ReadOnlyMemory{System.Byte},System.ReadOnlyMemory{System.Byte},System.DateTimeOffset,System.Nullable{System.Int64},System.Boolean,System.Nullable{System.ReadOnlyMemory{System.Byte}},System.Nullable{System.ReadOnlyMemory{System.Byte}},System.Security.Cryptography.X509Certificates.X509ExtensionCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo" /> class with the specified parameters.</summary>
      <param name="policyId">An OID representing the TSA's policy under which the response was produced.</param>
      <param name="hashAlgorithmId">A hash algorithm OID of the data to be timestamped.</param>
      <param name="messageHash">A hash value of the data to be timestamped.</param>
      <param name="serialNumber">An integer assigned by the TSA to the <see cref="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo" />.</param>
      <param name="timestamp">The timestamp encoded in the token.</param>
      <param name="accuracyInMicroseconds">The accuracy with which <paramref name="timestamp" /> is compared. Also see <paramref name="isOrdering" />.</param>
      <param name="isOrdering">
        <see langword="true" /> to ensure that every timestamp token from the same TSA can always be ordered based on the <paramref name="timestamp" />, regardless of the accuracy; <see langword="false" /> to make <paramref name="timestamp" /> indicate when token has been created by the TSA.</param>
      <param name="nonce">The nonce associated with this timestamp token. Using a nonce always allows to detect replays, and hence its use is recommended.</param>
      <param name="timestampAuthorityName">The hint in the TSA name identification. The actual identification of the entity that signed the response will always occur through the use of the certificate identifier.</param>
      <param name="extensions">The extension values associated with the timestamp.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The ASN.1 data is corrupted.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.Encode">
      <summary>Encodes this object into a TSTInfo value.</summary>
      <returns>The encoded TSTInfo value.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.GetExtensions">
      <summary>Gets the extension values associated with the timestamp.</summary>
      <returns>The extension values associated with the timestamp.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.GetMessageHash">
      <summary>Gets the data representing the message hash.</summary>
      <returns>The data representing the message hash.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.GetNonce">
      <summary>Gets the nonce associated with this timestamp token.</summary>
      <returns>The nonce associated with this timestamp token.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.GetSerialNumber">
      <summary>Gets an integer assigned by the TSA to the <see cref="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo" />.</summary>
      <returns>An integer assigned by the TSA to the <see cref="T:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.GetTimestampAuthorityName">
      <summary>Gets the data representing the hint in the TSA name identification.</summary>
      <returns>The data representing the hint in the TSA name identification.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.TryDecode(System.ReadOnlyMemory{System.Byte},System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo@,System.Int32@)">
      <summary>Decodes an encoded TSTInfo value.</summary>
      <param name="encodedBytes">The input or source buffer.</param>
      <param name="timestampTokenInfo">When this method returns <see langword="true" />, the decoded data. When this method returns <see langword="false" />, the value is <see langword="null" />, meaning the data could not be decoded.</param>
      <param name="bytesConsumed">The number of bytes used for decoding.</param>
      <returns>
        <see langword="true" /> if the operation succeeded; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.TryEncode(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to encode this object as a TSTInfo value, writing the result into the provided buffer.</summary>
      <param name="destination">The destination buffer.</param>
      <param name="bytesWritten">When this method returns <see langword="true" />, contains the bytes written to the <paramref name="destination" /> buffer.</param>
      <returns>
        <see langword="true" /> if the operation succeeded; <see langword="false" /> if the buffer size was insufficient.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.AccuracyInMicroseconds">
      <summary>Gets the accuracy with which <see cref="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.Timestamp" /> is compared.</summary>
      <returns>The accuracy with which <see cref="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.Timestamp" /> is compared.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.HasExtensions">
      <summary>Gets a value indicating whether there are any extensions associated with this timestamp token.</summary>
      <returns>
        <see langword="true" /> if there are any extensions associated with this timestamp token; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.HashAlgorithmId">
      <summary>Gets an OID of the hash algorithm.</summary>
      <returns>An OID of the hash algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.IsOrdering">
      <summary>Gets a value indicating if every timestamp token from the same TSA can always be ordered based on the <see cref="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.Timestamp" />, regardless of the accuracy. If the value is <see langword="false" />, <see cref="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.Timestamp" /> indicates when the token has been created by the TSA.</summary>
      <returns>
        <see langword="true" /> if every timestamp token from the same TSA can always be ordered based on the <see cref="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.Timestamp" />; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.PolicyId">
      <summary>Gets an OID representing the TSA's policy under which the response was produced.</summary>
      <returns>An OID representing the TSA's policy under which the response was produced.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.Timestamp">
      <summary>Gets the timestamp encoded in the token.</summary>
      <returns>The timestamp encoded in the token.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.Rfc3161TimestampTokenInfo.Version">
      <summary>Gets the version of the timestamp token.</summary>
      <returns>The version of the timestamp token.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.SignedCms">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> class enables signing and verifying of CMS/PKCS #7 messages.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.#ctor">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.#ctor" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> class.</summary>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.#ctor(System.Security.Cryptography.Pkcs.ContentInfo)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.#ctor(System.Security.Cryptography.Pkcs.ContentInfo)" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> class by using the specified content information as the inner content.</summary>
      <param name="contentInfo">A <see cref="T:System.Security.Cryptography.Pkcs.ContentInfo" /> object that specifies the content information as the inner content of the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> message.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.#ctor(System.Security.Cryptography.Pkcs.ContentInfo,System.Boolean)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.#ctor(System.Security.Cryptography.Pkcs.ContentInfo,System.Boolean)" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> class by using the specified content information as the inner content and by using the detached state.</summary>
      <param name="contentInfo">A <see cref="T:System.Security.Cryptography.Pkcs.ContentInfo" /> object that specifies the content information as the inner content of the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> message.</param>
      <param name="detached">A <see cref="T:System.Boolean" /> value that specifies whether the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> object is for a detached signature. If <paramref name="detached" /> is <see langword="true" />, the signature is detached. If <paramref name="detached" /> is <see langword="false" />, the signature is not detached.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType)" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> class by using the specified subject identifier type as the default subject identifier type for signers.</summary>
      <param name="signerIdentifierType">A <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifierType" /> member that specifies the default subject identifier type for signers.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType,System.Security.Cryptography.Pkcs.ContentInfo)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType,System.Security.Cryptography.Pkcs.ContentInfo)" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> class by using the specified subject identifier type as the default subject identifier type for signers and content information as the inner content.</summary>
      <param name="signerIdentifierType">A <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifierType" /> member that specifies the default subject identifier type for signers.</param>
      <param name="contentInfo">A <see cref="T:System.Security.Cryptography.Pkcs.ContentInfo" /> object that specifies the content information as the inner content of the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> message.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType,System.Security.Cryptography.Pkcs.ContentInfo,System.Boolean)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.#ctor(System.Security.Cryptography.Pkcs.SubjectIdentifierType,System.Security.Cryptography.Pkcs.ContentInfo,System.Boolean)" /> constructor creates an instance of the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> class by using the specified subject identifier type as the default subject identifier type for signers, the content information as the inner content, and by using the detached state.</summary>
      <param name="signerIdentifierType">A <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifierType" /> member that specifies the default subject identifier type for signers.</param>
      <param name="contentInfo">A <see cref="T:System.Security.Cryptography.Pkcs.ContentInfo" /> object that specifies the content information as the inner content of the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> message.</param>
      <param name="detached">A <see cref="T:System.Boolean" /> value that specifies whether the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> object is for a detached signature. If <paramref name="detached" /> is <see langword="true" />, the signature is detached. If detached is <see langword="false" />, the signature is not detached.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.AddCertificate(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Adds a certificate to the collection of certificates for the encoded CMS/PKCS #7 message.</summary>
      <param name="certificate">The certificate to add to the collection.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate already exists in the collection.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.CheckHash">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.CheckHash" /> method verifies the data integrity of the CMS/PKCS #7 message. <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.CheckHash" /> is a specialized method used in specific security infrastructure applications that only wish to check the hash of the CMS message, rather than perform a full digital signature verification. <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.CheckHash" /> does not authenticate the author nor sender of the message because this method does not involve verifying a digital signature. For general-purpose checking of the integrity and authenticity of a CMS/PKCS #7 message, use the <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.CheckSignature(System.Boolean)" /> or <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.CheckSignature(System.Security.Cryptography.X509Certificates.X509Certificate2Collection,System.Boolean)" /> methods.</summary>
      <exception cref="T:System.InvalidOperationException">A method call was invalid for the object's current state.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.CheckSignature(System.Boolean)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.CheckSignature(System.Boolean)" /> method verifies the digital signatures on the signed CMS/PKCS #7 message and, optionally, validates the signers' certificates.</summary>
      <param name="verifySignatureOnly">A <see cref="T:System.Boolean" /> value that specifies whether only the digital signatures are verified without the signers' certificates being validated.  
  
 If <paramref name="verifySignatureOnly" /> is <see langword="true" />, only the digital signatures are verified. If it is <see langword="false" />, the digital signatures are verified, the signers' certificates are validated, and the purposes of the certificates are validated. The purposes of a certificate are considered valid if the certificate has no key usage or if the key usage supports digital signatures or nonrepudiation.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <exception cref="T:System.InvalidOperationException">A method call was invalid for the object's current state.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.CheckSignature(System.Security.Cryptography.X509Certificates.X509Certificate2Collection,System.Boolean)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.CheckSignature(System.Security.Cryptography.X509Certificates.X509Certificate2Collection,System.Boolean)" /> method verifies the digital signatures on the signed CMS/PKCS #7 message by using the specified collection of certificates and, optionally, validates the signers' certificates.</summary>
      <param name="extraStore">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object that can be used to validate the certificate chain. If no additional certificates are to be used to validate the certificate chain, use <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.CheckSignature(System.Boolean)" /> instead of <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.CheckSignature(System.Security.Cryptography.X509Certificates.X509Certificate2Collection,System.Boolean)" />.</param>
      <param name="verifySignatureOnly">A <see cref="T:System.Boolean" /> value that specifies whether only the digital signatures are verified without the signers' certificates being validated.  
  
 If <paramref name="verifySignatureOnly" /> is <see langword="true" />, only the digital signatures are verified. If it is <see langword="false" />, the digital signatures are verified, the signers' certificates are validated, and the purposes of the certificates are validated. The purposes of a certificate are considered valid if the certificate has no key usage or if the key usage supports digital signatures or nonrepudiation.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <exception cref="T:System.InvalidOperationException">A method call was invalid for the object's current state.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.ComputeSignature">
      <summary>Creates a signature and adds the signature to the CMS/PKCS #7 message.</summary>
      <exception cref="T:System.InvalidOperationException">.NET Framework (all versions) and .NET Core 3.0 and later: The recipient certificate is not specified.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core version 2.2 and earlier: No signer certificate was provided.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.ComputeSignature(System.Security.Cryptography.Pkcs.CmsSigner)">
      <summary>Creates a signature using the specified signer and adds the signature to the CMS/PKCS #7 message.</summary>
      <param name="signer">A <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> object that represents the signer.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.ComputeSignature(System.Security.Cryptography.Pkcs.CmsSigner,System.Boolean)">
      <summary>Creates a signature using the specified signer and adds the signature to the CMS/PKCS #7 message.</summary>
      <param name="signer">A <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> object that represents the signer.</param>
      <param name="silent">.NET Core and .NET 5+ only: <see langword="true" /> to request opening keys with PIN prompts disabled, where supported; otherwise, <see langword="false" />. In .NET Framework, this parameter is not used and a PIN prompt is always shown, if required.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="signer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <exception cref="T:System.InvalidOperationException">.NET Framework only: A signing certificate is not specified.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: A signing certificate is not specified.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.Decode(System.Byte[])">
      <summary>Decodes an encoded <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> message.</summary>
      <param name="encodedMessage">An array of byte values that represents the encoded CMS/PKCS#7 message to be decoded.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="encodedMessage" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="encodedMessage" /> could not be decoded successfully.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.Decode(System.ReadOnlySpan{System.Byte})">
      <param name="encodedMessage">A read-only span of byte values that represents the encoded CMS/PKCS#7 message to be decoded.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="encodedMessage" /> could not be decoded successfully.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.Encode">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.Encode" /> method encodes the information in the object into a CMS/PKCS #7 message.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <exception cref="T:System.InvalidOperationException">A method call was invalid for the object's current state.</exception>
      <returns>An array of byte values that represents the encoded message. The encoded message can be decoded by the <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.Decode(System.Byte[])" /> method.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.RemoveCertificate(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Removes the specified certificate from the collection of certificates for the encoded CMS/PKCS #7 message.</summary>
      <param name="certificate">The certificate to remove from the collection.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate was not found.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.RemoveSignature(System.Int32)">
      <summary>Removes the signature at the specified index of the <see cref="P:System.Security.Cryptography.Pkcs.SignedCms.SignerInfos" /> collection.</summary>
      <param name="index">The zero-based index of the signature to remove.</param>
      <exception cref="T:System.InvalidOperationException">A CMS/PKCS #7 message is not signed, and <paramref name="index" /> is invalid.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.  
  
 -or-  
  
 <paramref name="index" /> is greater than the signature count minus 1.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The signature could not be removed.  
  
 -or-  
  
 An internal cryptographic error occurred.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignedCms.RemoveSignature(System.Security.Cryptography.Pkcs.SignerInfo)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignedCms.RemoveSignature(System.Security.Cryptography.Pkcs.SignerInfo)" /> method removes the signature for the specified <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object.</summary>
      <param name="signerInfo">A <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object that represents the countersignature being removed.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of an argument was outside the allowable range of values as defined by the called method.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignedCms.Certificates">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignedCms.Certificates" /> property retrieves the certificates associated with the encoded CMS/PKCS #7 message.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> collection that represents the set of certificates for the encoded CMS/PKCS #7 message.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignedCms.ContentInfo">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignedCms.ContentInfo" /> property retrieves the inner contents of the encoded CMS/PKCS #7 message.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.ContentInfo" /> object that represents the contents of the encoded CMS/PKCS #7 message.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignedCms.Detached">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignedCms.Detached" /> property retrieves whether the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> object is for a detached signature.</summary>
      <returns>A <see cref="T:System.Boolean" /> value that specifies whether the <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> object is for a detached signature. If this property is <see langword="true" />, the signature is detached. If this property is <see langword="false" />, the signature is not detached.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignedCms.SignerInfos">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignedCms.SignerInfos" /> property retrieves the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection associated with the CMS/PKCS #7 message.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> object that represents the signer information for the CMS/PKCS #7 message.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignedCms.Version">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignedCms.Version" /> property retrieves the version of the CMS/PKCS #7 message.</summary>
      <returns>An int value that represents the CMS/PKCS #7 message version.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.SignerInfo">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> class represents a signer associated with a <see cref="T:System.Security.Cryptography.Pkcs.SignedCms" /> object that represents a CMS/PKCS #7 message.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfo.AddUnsignedAttribute(System.Security.Cryptography.AsnEncodedData)">
      <summary>Adds the specified attribute to the current document.</summary>
      <param name="unsignedAttribute">The ASN.1 encoded attribute to add to the document.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Cannot find the original signer.
        
 -or-

ASN1 corrupted data.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfo.CheckHash">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.CheckHash" /> method verifies the data integrity of the CMS/PKCS #7 message signer information. <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.CheckHash" /> is a specialized method used in specific security infrastructure applications in which the subject uses the HashOnly member of the <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifierType" /> enumeration when setting up a <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> object. <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.CheckHash" /> does not authenticate the signer information because this method does not involve verifying a digital signature. For general-purpose checking of the integrity and authenticity of CMS/PKCS #7 message signer information and countersignatures, use the <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.CheckSignature(System.Boolean)" /> or <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.CheckSignature(System.Security.Cryptography.X509Certificates.X509Certificate2Collection,System.Boolean)" /> methods.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfo.CheckSignature(System.Boolean)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.CheckSignature(System.Boolean)" /> method verifies the digital signature of the message and, optionally, validates the certificate.</summary>
      <param name="verifySignatureOnly">A bool value that specifies whether only the digital signature is verified. If <paramref name="verifySignatureOnly" /> is <see langword="true" />, only the signature is verified. If <paramref name="verifySignatureOnly" /> is <see langword="false" />, the digital signature is verified, the certificate chain is validated, and the purposes of the certificates are validated. The purposes of the certificate are considered valid if the certificate has no key usage or if the key usage supports digital signature or nonrepudiation.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <exception cref="T:System.InvalidOperationException">A method call was invalid for the object's current state.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfo.CheckSignature(System.Security.Cryptography.X509Certificates.X509Certificate2Collection,System.Boolean)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.CheckSignature(System.Security.Cryptography.X509Certificates.X509Certificate2Collection,System.Boolean)" /> method verifies the digital signature of the message by using the specified collection of certificates and, optionally, validates the certificate.</summary>
      <param name="extraStore">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object that can be used to validate the chain. If no additional certificates are to be used to validate the chain, use <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.CheckSignature(System.Boolean)" /> instead of <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.CheckSignature(System.Security.Cryptography.X509Certificates.X509Certificate2Collection,System.Boolean)" />.</param>
      <param name="verifySignatureOnly">A bool value that specifies whether only the digital signature is verified. If <paramref name="verifySignatureOnly" /> is <see langword="true" />, only the signature is verified. If <paramref name="verifySignatureOnly" /> is <see langword="false" />, the digital signature is verified, the certificate chain is validated, and the purposes of the certificates are validated. The purposes of the certificate are considered valid if the certificate has no key usage or if the key usage supports digital signature or nonrepudiation.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
      <exception cref="T:System.InvalidOperationException">A method call was invalid for the object's current state.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfo.ComputeCounterSignature">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.ComputeCounterSignature" /> method prompts the user to select a signing certificate, creates a countersignature, and adds the signature to the CMS/PKCS #7 message. Countersignatures are restricted to one level.</summary>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfo.ComputeCounterSignature(System.Security.Cryptography.Pkcs.CmsSigner)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.ComputeCounterSignature(System.Security.Cryptography.Pkcs.CmsSigner)" /> method creates a countersignature by using the specified signer and adds the signature to the CMS/PKCS #7 message. Countersignatures are restricted to one level.</summary>
      <param name="signer">A <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> object that represents the counter signer.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfo.GetSignature">
      <summary>Retrieves the signature for the current <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object.</summary>
      <returns>The signature for the current <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfo.RemoveCounterSignature(System.Int32)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.RemoveCounterSignature(System.Int32)" /> method removes the countersignature at the specified index of the <see cref="P:System.Security.Cryptography.Pkcs.SignerInfo.CounterSignerInfos" /> collection.</summary>
      <param name="index">The zero-based index of the countersignature to remove.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfo.RemoveCounterSignature(System.Security.Cryptography.Pkcs.SignerInfo)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfo.RemoveCounterSignature(System.Security.Cryptography.Pkcs.SignerInfo)" /> method removes the countersignature for the specified <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object.</summary>
      <param name="counterSignerInfo">A <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object that represents the countersignature being removed.</param>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of an argument was outside the allowable range of values as defined by the called method.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A cryptographic operation could not be completed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfo.RemoveUnsignedAttribute(System.Security.Cryptography.AsnEncodedData)">
      <summary>Removes the specified attribute from the current document.</summary>
      <param name="unsignedAttribute">The ASN.1 encoded attribute to remove from the document.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Cannot find the original signer.

 -or-

Attribute not found.

 -or-
 
ASN1 corrupted data.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfo.Certificate">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfo.Certificate" /> property retrieves the signing certificate associated with the signer information.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object that represents the signing certificate.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfo.CounterSignerInfos">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfo.CounterSignerInfos" /> property retrieves the set of counter signers associated with the signer information.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection that represents the counter signers for the signer information. If there are no counter signers, the property is an empty collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfo.DigestAlgorithm">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfo.DigestAlgorithm" /> property retrieves the <see cref="T:System.Security.Cryptography.Oid" /> object that represents the hash algorithm used in the computation of the signatures.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.Oid" /> object that represents the hash algorithm used with the signature.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfo.SignatureAlgorithm">
      <summary>Gets the identifier for the signature algorithm used by the current <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object.</summary>
      <returns>The identifier for the signature algorithm used by the current <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfo.SignedAttributes">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfo.SignedAttributes" /> property retrieves the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection of signed attributes that is associated with the signer information. Signed attributes are signed along with the rest of the message content.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection that represents the signed attributes. If there are no signed attributes, the property is an empty collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfo.SignerIdentifier">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfo.SignerIdentifier" /> property retrieves the certificate identifier of the signer associated with the signer information.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifier" /> object that uniquely identifies the certificate associated with the signer information.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfo.UnsignedAttributes">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfo.UnsignedAttributes" /> property retrieves the <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection of unsigned attributes that is associated with the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> content. Unsigned attributes can be modified without invalidating the signature.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.CryptographicAttributeObjectCollection" /> collection that represents the unsigned attributes. If there are no unsigned attributes, the property is an empty collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfo.Version">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfo.Version" /> property retrieves the signer information version.</summary>
      <returns>An int value that specifies the signer information version.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.SignerInfoCollection">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> class represents a collection of <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> objects. <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> implements the <see cref="T:System.Collections.ICollection" /> interface.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfoCollection.CopyTo(System.Array,System.Int32)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfoCollection.CopyTo(System.Array,System.Int32)" /> method copies the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection to an array.</summary>
      <param name="array">An <see cref="T:System.Array" /> object to which the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection is to be copied.</param>
      <param name="index">The zero-based index in <paramref name="array" /> where the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection is copied.</param>
      <exception cref="T:System.ArgumentException">One of the arguments provided to a method was not valid.</exception>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of an argument was outside the allowable range of values as defined by the called method.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfoCollection.CopyTo(System.Security.Cryptography.Pkcs.SignerInfo[],System.Int32)">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfoCollection.CopyTo(System.Security.Cryptography.Pkcs.SignerInfo[],System.Int32)" /> method copies the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection to a <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> array.</summary>
      <param name="array">An array of <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> objects where the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection is to be copied.</param>
      <param name="index">The zero-based index in <paramref name="array" /> where the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection is copied.</param>
      <exception cref="T:System.ArgumentException">One of the arguments provided to a method was not valid.</exception>
      <exception cref="T:System.ArgumentNullException">A null reference was passed to a method that does not accept it as a valid argument.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of an argument was outside the allowable range of values as defined by the called method.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfoCollection.GetEnumerator">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfoCollection.GetEnumerator" /> method returns a <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoEnumerator" /> object for the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoEnumerator" /> object that can be used to enumerate the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfoCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfoCollection.System#Collections#IEnumerable#GetEnumerator" /> method returns a <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoEnumerator" /> object for the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoEnumerator" /> object that can be used to enumerate the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfoCollection.Count">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfoCollection.Count" /> property retrieves the number of items in the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</summary>
      <returns>An int value that represents the number of items in the collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfoCollection.IsSynchronized">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfoCollection.IsSynchronized" /> property retrieves whether access to the collection is synchronized, or thread safe. This property always returns <see langword="false" />, which means the collection is not thread safe.</summary>
      <returns>A <see cref="T:System.Boolean" /> value of <see langword="false" />, which means the collection is not thread safe.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfoCollection.Item(System.Int32)">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfoCollection.Item(System.Int32)" /> property retrieves the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object at the specified index in the collection.</summary>
      <param name="index">An int value that represents the index in the collection. The index is zero based.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of an argument was outside the allowable range of values as defined by the called method.</exception>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object  at the specified index.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfoCollection.SyncRoot">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfoCollection.SyncRoot" /> property retrieves an <see cref="T:System.Object" /> object is used to synchronize access to the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</summary>
      <returns>An <see cref="T:System.Object" /> object is used to synchronize access to the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.SignerInfoEnumerator">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoEnumerator" /> class provides enumeration functionality for the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection. <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoEnumerator" /> implements the <see cref="T:System.Collections.IEnumerator" /> interface.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfoEnumerator.MoveNext">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfoEnumerator.MoveNext" /> method advances the enumeration to the next   <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object in the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</summary>
      <returns>This method returns a bool value that specifies whether the enumeration successfully advanced. If the enumeration successfully moved to the next <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object, the method returns <see langword="true" />. If the enumeration moved past the last item in the enumeration, it returns <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SignerInfoEnumerator.Reset">
      <summary>The <see cref="M:System.Security.Cryptography.Pkcs.SignerInfoEnumerator.Reset" /> method resets the enumeration to the first <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object in the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</summary>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfoEnumerator.Current">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfoEnumerator.Current" /> property retrieves the current <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object from the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object that represents the current signer information structure in the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SignerInfoEnumerator.System#Collections#IEnumerator#Current">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SignerInfoEnumerator.System#Collections#IEnumerator#Current" /> property retrieves the current <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object from the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Pkcs.SignerInfo" /> object that represents the current signer information structure in the <see cref="T:System.Security.Cryptography.Pkcs.SignerInfoCollection" /> collection.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.SubjectIdentifier">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifier" /> class defines the type of the identifier of a subject, such as a <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> or a <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" />.  The subject can be identified by the certificate issuer and serial number or the subject key.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Pkcs.SubjectIdentifier.MatchesCertificate(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Verifies if the specified certificate's subject identifier matches current subject identifier instance.</summary>
      <param name="certificate">The certificate to match with the current subject identifier instance.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Invalid subject identifier type.</exception>
      <returns>
        <see langword="true" /> if the specified certificate's identifier matches the current subject identifier instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SubjectIdentifier.Type">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SubjectIdentifier.Type" /> property retrieves the type of subject identifier. The subject can be identified by the certificate issuer and serial number or the subject key.</summary>
      <returns>A member of the <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifierType" /> enumeration that identifies the type of subject.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SubjectIdentifier.Value">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SubjectIdentifier.Value" /> property retrieves the value of the subject identifier. Use the <see cref="P:System.Security.Cryptography.Pkcs.SubjectIdentifier.Type" /> property to determine the type of subject identifier, and use the <see cref="P:System.Security.Cryptography.Pkcs.SubjectIdentifier.Value" /> property to retrieve the corresponding value.</summary>
      <returns>An <see cref="T:System.Object" /> object that represents the value of the subject identifier. This <see cref="T:System.Object" /> can be one of the following objects as determined by the <see cref="P:System.Security.Cryptography.Pkcs.SubjectIdentifier.Type" /> property.  
  
 <list type="table"><listheader><term><see cref="P:System.Security.Cryptography.Pkcs.SubjectIdentifier.Type" /> property</term><description> Object</description></listheader><item><term> IssuerAndSerialNumber</term><description><see cref="T:System.Security.Cryptography.Xml.X509IssuerSerial" /></description></item><item><term> SubjectKeyIdentifier</term><description><see cref="T:System.String" /></description></item></list></returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKey">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKey" /> class defines the type of the identifier of a subject, such as a <see cref="T:System.Security.Cryptography.Pkcs.CmsSigner" /> or a <see cref="T:System.Security.Cryptography.Pkcs.CmsRecipient" />.  The subject can be identified by the certificate issuer and serial number, the hash of the subject key, or the subject key.</summary>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKey.Type">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKey.Type" /> property retrieves the type of subject identifier or key. The subject can be identified by the certificate issuer and serial number, the hash of the subject key, or the subject key.</summary>
      <returns>A member of the <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKeyType" /> enumeration that specifies the type of subject identifier.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKey.Value">
      <summary>The <see cref="P:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKey.Value" /> property retrieves the value of the subject identifier or  key. Use the <see cref="P:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKey.Type" /> property to determine the type of subject identifier or key, and use the <see cref="P:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKey.Value" /> property to retrieve the corresponding value.</summary>
      <returns>An <see cref="T:System.Object" /> object that represents the value of the subject identifier or key. This <see cref="T:System.Object" /> can be one of the following objects as determined by the <see cref="P:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKey.Type" /> property.  
  
 <list type="table"><listheader><term><see cref="P:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKey.Type" /> property</term><description> Object</description></listheader><item><term> IssuerAndSerialNumber</term><description><see cref="T:System.Security.Cryptography.Xml.X509IssuerSerial" /></description></item><item><term> SubjectKeyIdentifier</term><description><see cref="T:System.String" /></description></item><item><term> PublicKeyInfo</term><description><see cref="T:System.Security.Cryptography.Pkcs.PublicKeyInfo" /></description></item></list></returns>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKeyType">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKeyType" /> enumeration defines how a subject is identified.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKeyType.IssuerAndSerialNumber">
      <summary>The subject is identified by the certificate issuer and serial number.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKeyType.PublicKeyInfo">
      <summary>The subject is identified by the public key.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKeyType.SubjectKeyIdentifier">
      <summary>The subject is identified by the hash of the subject key.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.SubjectIdentifierOrKeyType.Unknown">
      <summary>The type is unknown.</summary>
    </member>
    <member name="T:System.Security.Cryptography.Pkcs.SubjectIdentifierType">
      <summary>The <see cref="T:System.Security.Cryptography.Pkcs.SubjectIdentifierType" /> enumeration defines the type of subject identifier.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.SubjectIdentifierType.IssuerAndSerialNumber">
      <summary>The subject is identified by the certificate issuer and serial number.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.SubjectIdentifierType.NoSignature">
      <summary>The subject is identified as taking part in an integrity check operation that uses only a hashing algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.SubjectIdentifierType.SubjectKeyIdentifier">
      <summary>The subject is identified by the hash of the subject's public key. The hash algorithm used is determined by the signature algorithm suite in the subject's certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Pkcs.SubjectIdentifierType.Unknown">
      <summary>The type of subject identifier is unknown.</summary>
    </member>
    <member name="T:System.Security.Cryptography.Xml.X509IssuerSerial">
      <summary>Represents the &lt;<see langword="X509IssuerSerial" />&gt; element of an XML digital signature.</summary>
    </member>
    <member name="P:System.Security.Cryptography.Xml.X509IssuerSerial.IssuerName">
      <summary>Gets or sets an X.509 certificate issuer's distinguished name.</summary>
      <returns>An X.509 certificate issuer's distinguished name.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.X509IssuerSerial.SerialNumber">
      <summary>Gets or sets an X.509 certificate issuer's serial number.</summary>
      <returns>An X.509 certificate issuer's serial number.</returns>
    </member>
  </members>
</doc>