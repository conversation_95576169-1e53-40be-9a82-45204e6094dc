<?xml version="1.0" encoding="utf-8"?>
<Document>
  <Engineering version="V18" />
  <DocumentInfo>
    <Created>2025-05-20T08:40:25.9827523Z</Created>
    <ExportSetting>WithDefaults, WithReadOnly</ExportSetting>
    <InstalledProducts>
      <Product>
        <DisplayName>Totally Integrated Automation Portal</DisplayName>
        <DisplayVersion>V18 Update 4</DisplayVersion>
      </Product>
      <OptionPackage>
        <DisplayName>TIA Portal Openness</DisplayName>
        <DisplayVersion>V18 Update 4</DisplayVersion>
      </OptionPackage>
      <OptionPackage>
        <DisplayName>TIA Portal Teamcenter Gateway</DisplayName>
        <DisplayVersion>V18</DisplayVersion>
      </OptionPackage>
      <OptionPackage>
        <DisplayName>TIA Portal Version Control Interface</DisplayName>
        <DisplayVersion>V18</DisplayVersion>
      </OptionPackage>
      <Product>
        <DisplayName>SINAMICS Startdrive Advanced</DisplayName>
        <DisplayVersion>V18 SP2 Update 1</DisplayVersion>
      </Product>
      <OptionPackage>
        <DisplayName>SINAMICS Startdrive G110M, G120, G120C, G120D, G120P, G115D</DisplayName>
        <DisplayVersion>V18 SP2 Update 1</DisplayVersion>
      </OptionPackage>
      <OptionPackage>
        <DisplayName>SINAMICS Startdrive G130, G150, S120, S150, SINAMICS MV, S210, G220, S200</DisplayName>
        <DisplayVersion>V18 SP2 Update 1</DisplayVersion>
      </OptionPackage>
      <Product>
        <DisplayName>STEP 7 Professional</DisplayName>
        <DisplayVersion>V18 Update 4</DisplayVersion>
      </Product>
      <OptionPackage>
        <DisplayName>STEP 7 Safety</DisplayName>
        <DisplayVersion>V18</DisplayVersion>
      </OptionPackage>
      <Product>
        <DisplayName>WinCC Advanced / Unified PC</DisplayName>
        <DisplayVersion>V18 Update 4</DisplayVersion>
      </Product>
    </InstalledProducts>
  </DocumentInfo>
  <SW.Blocks.FB ID="0">
    <AttributeList>
      <AutoNumber>true</AutoNumber>
      <CodeModifiedDate ReadOnly="true">2025-05-20T08:39:11.9116629Z</CodeModifiedDate>
      <CompileDate ReadOnly="true">2025-05-20T08:39:11.9116629Z</CompileDate>
      <CreationDate ReadOnly="true">2025-04-19T10:29:52.467511Z</CreationDate>
      <HandleErrorsWithinBlock ReadOnly="true">false</HandleErrorsWithinBlock>
      <HeaderAuthor />
      <HeaderFamily />
      <HeaderName />
      <HeaderVersion>0.1</HeaderVersion>
      <Interface><Sections xmlns="http://www.siemens.com/automation/Openness/SW/Interface/v5">
  <Section Name="Input" />
  <Section Name="Output" />
  <Section Name="InOut" />
  <Section Name="Static">
    <Member Name="NA1C101左缓存挡料气缸1" Datatype="&quot;FB4000_Valve&quot;" Accessibility="Public">
      <AttributeList>
        <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
        <BooleanAttribute Name="ExternalAccessible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalVisible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalWritable" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserVisible" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserReadOnly" Informative="true" SystemDefined="true">false</BooleanAttribute>
        <BooleanAttribute Name="UserDeletable" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="SetPoint" SystemDefined="true">true</BooleanAttribute>
      </AttributeList>
      <Comment>
        <MultiLanguageText Lang="zh-CN">左缓存挡料气缸1</MultiLanguageText>
        <MultiLanguageText Lang="en-GB">CYL01 cylinder</MultiLanguageText>
      </Comment>
      <Sections>
        <Section Name="Input">
          <Member Name="I_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="I_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="VentValveAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ManualMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="AutoRunMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="E_StopStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ResetStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exFwdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exBkdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CmdClearFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CondFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="simulation" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">48</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="hold" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">80</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Output">
          <Member Name="Q_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">96</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Q_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">97</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">98</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">99</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">100</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">101</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWrk" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">102</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsRst" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">103</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWR" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">104</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">105</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">106</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">107</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="error" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">108</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="InOut">
          <Member Name="FwdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">112</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">144</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Static">
          <Member Name="CylinderCmd" Datatype="Struct">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">176</IntegerAttribute>
            </AttributeList>
            <Member Name="FwdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IWP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IHP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QFwd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QBkd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValve" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="Moving" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">17</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">18</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">19</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">20</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">21</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">22</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">23</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">24</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">25</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">26</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">27</IntegerAttribute>
              </AttributeList>
            </Member>
          </Member>
          <Member Name="PanelIndex" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">208</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpw" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">224</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">225</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag1" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">226</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag2" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">227</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnOneTime" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">240</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
      </Sections>
    </Member>
    <Member Name="NA1C201左缓存挡料气缸2" Datatype="&quot;FB4000_Valve&quot;" Accessibility="Public">
      <AttributeList>
        <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
        <BooleanAttribute Name="ExternalAccessible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalVisible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalWritable" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserVisible" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserReadOnly" Informative="true" SystemDefined="true">false</BooleanAttribute>
        <BooleanAttribute Name="UserDeletable" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="SetPoint" SystemDefined="true">true</BooleanAttribute>
      </AttributeList>
      <Comment>
        <MultiLanguageText Lang="zh-CN">左缓存挡料气缸1</MultiLanguageText>
        <MultiLanguageText Lang="en-GB">CYL01 cylinder</MultiLanguageText>
      </Comment>
      <Sections>
        <Section Name="Input">
          <Member Name="I_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="I_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="VentValveAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ManualMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="AutoRunMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="E_StopStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ResetStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exFwdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exBkdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CmdClearFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CondFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="simulation" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">48</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="hold" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">80</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Output">
          <Member Name="Q_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">96</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Q_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">97</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">98</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">99</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">100</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">101</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWrk" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">102</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsRst" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">103</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWR" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">104</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">105</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">106</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">107</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="error" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">108</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="InOut">
          <Member Name="FwdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">112</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">144</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Static">
          <Member Name="CylinderCmd" Datatype="Struct">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">176</IntegerAttribute>
            </AttributeList>
            <Member Name="FwdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IWP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IHP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QFwd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QBkd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValve" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="Moving" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">17</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">18</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">19</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">20</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">21</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">22</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">23</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">24</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">25</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">26</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">27</IntegerAttribute>
              </AttributeList>
            </Member>
          </Member>
          <Member Name="PanelIndex" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">208</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpw" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">224</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">225</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag1" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">226</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag2" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">227</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnOneTime" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">240</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
      </Sections>
    </Member>
    <Member Name="NA1C301左缓存挡料气缸3" Datatype="&quot;FB4000_Valve&quot;" Accessibility="Public">
      <AttributeList>
        <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
        <BooleanAttribute Name="ExternalAccessible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalVisible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalWritable" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserVisible" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserReadOnly" Informative="true" SystemDefined="true">false</BooleanAttribute>
        <BooleanAttribute Name="UserDeletable" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="SetPoint" SystemDefined="true">true</BooleanAttribute>
      </AttributeList>
      <Comment>
        <MultiLanguageText Lang="zh-CN">左缓存挡料气缸1</MultiLanguageText>
        <MultiLanguageText Lang="en-GB">CYL01 cylinder</MultiLanguageText>
      </Comment>
      <Sections>
        <Section Name="Input">
          <Member Name="I_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="I_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="VentValveAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ManualMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="AutoRunMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="E_StopStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ResetStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exFwdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exBkdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CmdClearFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CondFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="simulation" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">48</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="hold" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">80</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Output">
          <Member Name="Q_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">96</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Q_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">97</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">98</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">99</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">100</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">101</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWrk" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">102</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsRst" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">103</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWR" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">104</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">105</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">106</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">107</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="error" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">108</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="InOut">
          <Member Name="FwdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">112</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">144</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Static">
          <Member Name="CylinderCmd" Datatype="Struct">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">176</IntegerAttribute>
            </AttributeList>
            <Member Name="FwdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IWP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IHP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QFwd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QBkd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValve" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="Moving" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">17</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">18</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">19</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">20</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">21</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">22</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">23</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">24</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">25</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">26</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">27</IntegerAttribute>
              </AttributeList>
            </Member>
          </Member>
          <Member Name="PanelIndex" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">208</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpw" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">224</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">225</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag1" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">226</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag2" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">227</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnOneTime" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">240</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
      </Sections>
    </Member>
    <Member Name="NA1C401左缓存挡料气缸4" Datatype="&quot;FB4000_Valve&quot;" Accessibility="Public">
      <AttributeList>
        <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
        <BooleanAttribute Name="ExternalAccessible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalVisible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalWritable" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserVisible" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserReadOnly" Informative="true" SystemDefined="true">false</BooleanAttribute>
        <BooleanAttribute Name="UserDeletable" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="SetPoint" SystemDefined="true">true</BooleanAttribute>
      </AttributeList>
      <Comment>
        <MultiLanguageText Lang="zh-CN">左缓存挡料气缸1</MultiLanguageText>
        <MultiLanguageText Lang="en-GB">CYL01 cylinder</MultiLanguageText>
      </Comment>
      <Sections>
        <Section Name="Input">
          <Member Name="I_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="I_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="VentValveAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ManualMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="AutoRunMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="E_StopStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ResetStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exFwdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exBkdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CmdClearFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CondFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="simulation" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">48</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="hold" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">80</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Output">
          <Member Name="Q_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">96</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Q_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">97</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">98</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">99</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">100</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">101</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWrk" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">102</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsRst" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">103</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWR" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">104</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">105</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">106</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">107</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="error" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">108</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="InOut">
          <Member Name="FwdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">112</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">144</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Static">
          <Member Name="CylinderCmd" Datatype="Struct">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">176</IntegerAttribute>
            </AttributeList>
            <Member Name="FwdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IWP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IHP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QFwd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QBkd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValve" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="Moving" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">17</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">18</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">19</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">20</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">21</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">22</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">23</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">24</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">25</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">26</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">27</IntegerAttribute>
              </AttributeList>
            </Member>
          </Member>
          <Member Name="PanelIndex" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">208</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpw" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">224</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">225</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag1" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">226</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag2" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">227</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnOneTime" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">240</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
      </Sections>
    </Member>
    <Member Name="NA1C401左缓存料盘抽出气缸" Datatype="&quot;FB4000_Valve&quot;" Accessibility="Public">
      <AttributeList>
        <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
        <BooleanAttribute Name="ExternalAccessible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalVisible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalWritable" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserVisible" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserReadOnly" Informative="true" SystemDefined="true">false</BooleanAttribute>
        <BooleanAttribute Name="UserDeletable" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="SetPoint" SystemDefined="true">true</BooleanAttribute>
      </AttributeList>
      <Comment>
        <MultiLanguageText Lang="zh-CN">左缓存挡料气缸1</MultiLanguageText>
        <MultiLanguageText Lang="en-GB">CYL01 cylinder</MultiLanguageText>
      </Comment>
      <Sections>
        <Section Name="Input">
          <Member Name="I_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="I_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="VentValveAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ManualMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="AutoRunMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="E_StopStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ResetStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exFwdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exBkdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CmdClearFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CondFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="simulation" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">48</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="hold" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">80</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Output">
          <Member Name="Q_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">96</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Q_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">97</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">98</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">99</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">100</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">101</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWrk" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">102</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsRst" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">103</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWR" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">104</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">105</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">106</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">107</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="error" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">108</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="InOut">
          <Member Name="FwdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">112</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">144</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Static">
          <Member Name="CylinderCmd" Datatype="Struct">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">176</IntegerAttribute>
            </AttributeList>
            <Member Name="FwdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IWP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IHP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QFwd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QBkd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValve" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="Moving" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">17</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">18</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">19</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">20</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">21</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">22</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">23</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">24</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">25</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">26</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">27</IntegerAttribute>
              </AttributeList>
            </Member>
          </Member>
          <Member Name="PanelIndex" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">208</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpw" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">224</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">225</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag1" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">226</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag2" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">227</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnOneTime" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">240</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
      </Sections>
    </Member>
    <Member Name="NA1C501左缓存料盘定位气缸" Datatype="&quot;FB4000_Valve&quot;" Accessibility="Public">
      <AttributeList>
        <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
        <BooleanAttribute Name="ExternalAccessible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalVisible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalWritable" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserVisible" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserReadOnly" Informative="true" SystemDefined="true">false</BooleanAttribute>
        <BooleanAttribute Name="UserDeletable" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="SetPoint" SystemDefined="true">true</BooleanAttribute>
      </AttributeList>
      <Comment>
        <MultiLanguageText Lang="zh-CN">左缓存挡料气缸1</MultiLanguageText>
        <MultiLanguageText Lang="en-GB">CYL01 cylinder</MultiLanguageText>
      </Comment>
      <Sections>
        <Section Name="Input">
          <Member Name="I_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="I_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="VentValveAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ManualMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="AutoRunMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="E_StopStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ResetStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exFwdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exBkdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CmdClearFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CondFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="simulation" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">48</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="hold" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">80</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Output">
          <Member Name="Q_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">96</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Q_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">97</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">98</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">99</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">100</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">101</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWrk" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">102</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsRst" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">103</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWR" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">104</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">105</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">106</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">107</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="error" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">108</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="InOut">
          <Member Name="FwdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">112</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">144</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Static">
          <Member Name="CylinderCmd" Datatype="Struct">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">176</IntegerAttribute>
            </AttributeList>
            <Member Name="FwdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IWP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IHP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QFwd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QBkd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValve" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="Moving" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">17</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">18</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">19</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">20</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">21</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">22</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">23</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">24</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">25</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">26</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">27</IntegerAttribute>
              </AttributeList>
            </Member>
          </Member>
          <Member Name="PanelIndex" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">208</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpw" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">224</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">225</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag1" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">226</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag2" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">227</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnOneTime" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">240</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
      </Sections>
    </Member>
    <Member Name="NA1C601左缓存料盒搬运升降气缸" Datatype="&quot;FB4000_Valve&quot;" Accessibility="Public">
      <AttributeList>
        <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
        <BooleanAttribute Name="ExternalAccessible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalVisible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalWritable" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserVisible" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserReadOnly" Informative="true" SystemDefined="true">false</BooleanAttribute>
        <BooleanAttribute Name="UserDeletable" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="SetPoint" SystemDefined="true">true</BooleanAttribute>
      </AttributeList>
      <Comment>
        <MultiLanguageText Lang="zh-CN">左缓存挡料气缸1</MultiLanguageText>
        <MultiLanguageText Lang="en-GB">CYL01 cylinder</MultiLanguageText>
      </Comment>
      <Sections>
        <Section Name="Input">
          <Member Name="I_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="I_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="VentValveAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ManualMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="AutoRunMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="E_StopStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ResetStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exFwdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exBkdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CmdClearFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CondFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="simulation" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">48</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="hold" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">80</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Output">
          <Member Name="Q_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">96</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Q_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">97</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">98</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">99</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">100</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">101</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWrk" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">102</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsRst" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">103</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWR" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">104</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">105</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">106</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">107</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="error" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">108</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="InOut">
          <Member Name="FwdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">112</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">144</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Static">
          <Member Name="CylinderCmd" Datatype="Struct">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">176</IntegerAttribute>
            </AttributeList>
            <Member Name="FwdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IWP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IHP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QFwd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QBkd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValve" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="Moving" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">17</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">18</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">19</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">20</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">21</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">22</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">23</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">24</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">25</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">26</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">27</IntegerAttribute>
              </AttributeList>
            </Member>
          </Member>
          <Member Name="PanelIndex" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">208</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpw" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">224</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">225</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag1" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">226</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag2" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">227</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnOneTime" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">240</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
      </Sections>
    </Member>
    <Member Name="NA1C701左缓存料盒搬运夹爪气缸" Datatype="&quot;FB4000_Valve&quot;" Accessibility="Public">
      <AttributeList>
        <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
        <BooleanAttribute Name="ExternalAccessible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalVisible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalWritable" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserVisible" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserReadOnly" Informative="true" SystemDefined="true">false</BooleanAttribute>
        <BooleanAttribute Name="UserDeletable" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="SetPoint" SystemDefined="true">true</BooleanAttribute>
      </AttributeList>
      <Comment>
        <MultiLanguageText Lang="zh-CN">左缓存挡料气缸1</MultiLanguageText>
        <MultiLanguageText Lang="en-GB">CYL01 cylinder</MultiLanguageText>
      </Comment>
      <Sections>
        <Section Name="Input">
          <Member Name="I_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="I_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="VentValveAuto" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPmt" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ManualMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="AutoRunMod" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="E_StopStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="ResetStatus" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exFwdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="exBkdBtn" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CmdClearFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="CondFoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="simulation" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPresetTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">48</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="hold" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">80</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Output">
          <Member Name="Q_WRK" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">96</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Q_BAS" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">97</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">98</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdPos" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">99</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">100</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">101</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWrk" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">102</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsRst" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">103</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Flt_SnsWR" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">104</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FwdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">105</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdLockErr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">106</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="FoolProofing" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">107</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="error" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">108</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="InOut">
          <Member Name="FwdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">112</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="BkdUsedTime" Datatype="Real">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">144</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
        <Section Name="Static">
          <Member Name="CylinderCmd" Datatype="Struct">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">176</IntegerAttribute>
            </AttributeList>
            <Member Name="FwdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">0</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">1</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPmt" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">3</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">4</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPos" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">5</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IWP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">6</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="IHP" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">7</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QFwd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">8</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="QBkd" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">9</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">10</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">11</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveAuto" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">12</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValveTPPB" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">13</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="VentValve" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">14</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="Moving" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">15</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">16</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdIng" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">17</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">18</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">19</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">20</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdLockErr" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">21</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">22</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdPls" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">23</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">24</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdTPPB2" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">25</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="FwdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">26</IntegerAttribute>
              </AttributeList>
            </Member>
            <Member Name="BkdEnableButton" Datatype="Bool">
              <AttributeList>
                <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">27</IntegerAttribute>
              </AttributeList>
            </Member>
          </Member>
          <Member Name="PanelIndex" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">208</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpw" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">224</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="Mpr" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">225</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag1" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">226</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnSameTimeFlag2" Datatype="Bool">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">227</IntegerAttribute>
            </AttributeList>
          </Member>
          <Member Name="sensorOnOneTime" Datatype="Int">
            <AttributeList>
              <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">240</IntegerAttribute>
            </AttributeList>
          </Member>
        </Section>
      </Sections>
    </Member>
    <Member Name="staManual" Datatype="Bool" Remanence="SetInIDB" Accessibility="Public">
      <AttributeList>
        <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2048</IntegerAttribute>
        <BooleanAttribute Name="ExternalAccessible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalVisible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalWritable" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserVisible" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserReadOnly" Informative="true" SystemDefined="true">false</BooleanAttribute>
        <BooleanAttribute Name="UserDeletable" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="SetPoint" SystemDefined="true">true</BooleanAttribute>
      </AttributeList>
    </Member>
    <Member Name="staAuto" Datatype="Bool" Remanence="SetInIDB" Accessibility="Public">
      <AttributeList>
        <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2049</IntegerAttribute>
        <BooleanAttribute Name="ExternalAccessible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalVisible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalWritable" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserVisible" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserReadOnly" Informative="true" SystemDefined="true">false</BooleanAttribute>
        <BooleanAttribute Name="UserDeletable" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="SetPoint" SystemDefined="true">true</BooleanAttribute>
      </AttributeList>
    </Member>
    <Member Name="statReset" Datatype="Bool" Remanence="SetInIDB" Accessibility="Public">
      <AttributeList>
        <IntegerAttribute Name="Offset" Informative="true" SystemDefined="true">2050</IntegerAttribute>
        <BooleanAttribute Name="ExternalAccessible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalVisible" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="ExternalWritable" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserVisible" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="UserReadOnly" Informative="true" SystemDefined="true">false</BooleanAttribute>
        <BooleanAttribute Name="UserDeletable" Informative="true" SystemDefined="true">true</BooleanAttribute>
        <BooleanAttribute Name="SetPoint" SystemDefined="true">true</BooleanAttribute>
      </AttributeList>
    </Member>
  </Section>
  <Section Name="Temp" />
  <Section Name="Constant" />
</Sections></Interface>
      <InterfaceModifiedDate ReadOnly="true">2025-05-20T08:38:25.4983309Z</InterfaceModifiedDate>
      <IsConsistent ReadOnly="true">true</IsConsistent>
      <IsIECCheckEnabled>false</IsIECCheckEnabled>
      <IsKnowHowProtected ReadOnly="true">false</IsKnowHowProtected>
      <ISMultiInstanceCapable ReadOnly="true">true</ISMultiInstanceCapable>
      <IsWriteProtected ReadOnly="true">false</IsWriteProtected>
      <LibraryConformanceStatus ReadOnly="true">错误： 该块中包含单实例调用。
警告： 该对象包含对全局数据块的访问。
警告： 该块中包含对输入、输出、位存储器的直接访问，对寄存器的间接访问或对编译时未知的外部存储区的间接访问。
</LibraryConformanceStatus>
      <MemoryLayout>Standard</MemoryLayout>
      <ModifiedDate ReadOnly="true">2025-05-20T08:39:11.9116629Z</ModifiedDate>
      <Name>FB_ManualCtl_Row2</Name>
      <Namespace />
      <Number>87</Number>
      <ParameterModified ReadOnly="true">2025-04-19T10:29:52.467511Z</ParameterModified>
      <PLCSimAdvancedSupport ReadOnly="true">true</PLCSimAdvancedSupport>
      <ProgrammingLanguage>LAD</ProgrammingLanguage>
      <SetENOAutomatically>false</SetENOAutomatically>
      <StructureModified ReadOnly="true">2025-04-19T10:29:52.467511Z</StructureModified>
      <UDABlockProperties />
      <UDAEnableTagReadback>false</UDAEnableTagReadback>
    </AttributeList>
    <ObjectList>
      <MultilingualText ID="1" CompositionName="Comment">
        <ObjectList>
          <MultilingualTextItem ID="2" CompositionName="Items">
            <AttributeList>
              <Culture>zh-CN</Culture>
              <Text />
            </AttributeList>
          </MultilingualTextItem>
          <MultilingualTextItem ID="3" CompositionName="Items">
            <AttributeList>
              <Culture>en-GB</Culture>
              <Text />
            </AttributeList>
          </MultilingualTextItem>
        </ObjectList>
      </MultilingualText>
      <SW.Blocks.CompileUnit ID="4" CompositionName="CompileUnits">
        <AttributeList>
          <NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
  <Parts>
    <Access Scope="GlobalVariable" UId="21">
      <Symbol>
        <Component Name="setupNo" />
        <Component Name="hmi0_manual_no" />
        <Address Area="None" Type="Int" BlockNumber="3" BitOffset="32" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="22">
      <Constant>
        <ConstantType>Int</ConstantType>
        <ConstantValue>12</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="23">
      <Constant>
        <ConstantType>Byte</ConstantType>
        <ConstantValue>0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Dec_unsigned</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="24">
      <Constant>
        <ConstantType>Int</ConstantType>
        <ConstantValue>8</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="GlobalVariable" UId="25">
      <Symbol>
        <Component Name="FB_CylinderlControlEm8_DB" />
        <Address Area="DB" Type="FB_CylinderlControlEm8" BlockNumber="67" BitOffset="0" Informative="true" />
      </Symbol>
    </Access>
    <Part Name="Eq" UId="26">
      <TemplateValue Name="SrcType" Type="Type">Int</TemplateValue>
    </Part>
    <Call UId="27">
      <CallInfo Name="FC5002_ManualDisplayMove" BlockType="FC">
        <IntegerAttribute Name="BlockNumber" Informative="true">5002</IntegerAttribute>
        <DateAttribute Name="ParameterModifiedTS" Informative="true">2018-12-26T08:23:58</DateAttribute>
        <Parameter Name="PanelIndex" Section="Input" Type="Byte">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ManualMax" Section="Input" Type="Int">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="DBmanual" Section="Input" Type="DB_ANY">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
      </CallInfo>
    </Call>
  </Parts>
  <Wires>
    <Wire UId="28">
      <Powerrail />
      <NameCon UId="26" Name="pre" />
    </Wire>
    <Wire UId="29">
      <IdentCon UId="21" />
      <NameCon UId="26" Name="in1" />
    </Wire>
    <Wire UId="30">
      <IdentCon UId="22" />
      <NameCon UId="26" Name="in2" />
    </Wire>
    <Wire UId="31">
      <NameCon UId="26" Name="out" />
      <NameCon UId="27" Name="en" />
    </Wire>
    <Wire UId="32">
      <IdentCon UId="23" />
      <NameCon UId="27" Name="PanelIndex" />
    </Wire>
    <Wire UId="33">
      <IdentCon UId="24" />
      <NameCon UId="27" Name="ManualMax" />
    </Wire>
    <Wire UId="34">
      <IdentCon UId="25" />
      <NameCon UId="27" Name="DBmanual" />
    </Wire>
  </Wires>
</FlgNet></NetworkSource>
          <ProgrammingLanguage>LAD</ProgrammingLanguage>
        </AttributeList>
        <ObjectList>
          <MultilingualText ID="5" CompositionName="Comment">
            <ObjectList>
              <MultilingualTextItem ID="6" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="7" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
          <MultilingualText ID="8" CompositionName="Title">
            <ObjectList>
              <MultilingualTextItem ID="9" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>HMI display</Text>
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="A" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>
      <SW.Blocks.CompileUnit ID="B" CompositionName="CompileUnits">
        <AttributeList>
          <NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
  <Parts>
    <Access Scope="GlobalVariable" UId="21">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              <ConstantValue>12</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="mode" />
        <Component Name="manual" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4768" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="22">
      <Symbol>
        <Component Name="staManual" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="23">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              <ConstantValue>12</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="mode" />
        <Component Name="inCycle" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4792" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="24">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              <ConstantValue>12</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="mode" />
        <Component Name="inStep" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4776" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="25">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              <ConstantValue>12</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="mode" />
        <Component Name="inHoming" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4784" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="26">
      <Symbol>
        <Component Name="staAuto" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="27">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              <ConstantValue>12</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="RESET" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4936" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="28">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Part Name="Contact" UId="29" />
    <Part Name="Coil" UId="30" />
    <Part Name="Contact" UId="31" />
    <Part Name="Contact" UId="32" />
    <Part Name="Contact" UId="33" />
    <Part Name="O" UId="34">
      <TemplateValue Name="Card" Type="Cardinality">3</TemplateValue>
    </Part>
    <Part Name="Coil" UId="35" />
    <Part Name="Contact" UId="36" />
    <Part Name="Coil" UId="37" />
  </Parts>
  <Wires>
    <Wire UId="38">
      <Powerrail />
      <NameCon UId="29" Name="in" />
      <NameCon UId="31" Name="in" />
      <NameCon UId="32" Name="in" />
      <NameCon UId="33" Name="in" />
      <NameCon UId="36" Name="in" />
    </Wire>
    <Wire UId="39">
      <IdentCon UId="21" />
      <NameCon UId="29" Name="operand" />
    </Wire>
    <Wire UId="40">
      <NameCon UId="29" Name="out" />
      <NameCon UId="30" Name="in" />
    </Wire>
    <Wire UId="41">
      <IdentCon UId="22" />
      <NameCon UId="30" Name="operand" />
    </Wire>
    <Wire UId="42">
      <IdentCon UId="23" />
      <NameCon UId="31" Name="operand" />
    </Wire>
    <Wire UId="43">
      <NameCon UId="31" Name="out" />
      <NameCon UId="34" Name="in1" />
    </Wire>
    <Wire UId="44">
      <IdentCon UId="24" />
      <NameCon UId="32" Name="operand" />
    </Wire>
    <Wire UId="45">
      <NameCon UId="32" Name="out" />
      <NameCon UId="34" Name="in2" />
    </Wire>
    <Wire UId="46">
      <IdentCon UId="25" />
      <NameCon UId="33" Name="operand" />
    </Wire>
    <Wire UId="47">
      <NameCon UId="33" Name="out" />
      <NameCon UId="34" Name="in3" />
    </Wire>
    <Wire UId="48">
      <NameCon UId="34" Name="out" />
      <NameCon UId="35" Name="in" />
    </Wire>
    <Wire UId="49">
      <IdentCon UId="26" />
      <NameCon UId="35" Name="operand" />
    </Wire>
    <Wire UId="50">
      <IdentCon UId="27" />
      <NameCon UId="36" Name="operand" />
    </Wire>
    <Wire UId="51">
      <NameCon UId="36" Name="out" />
      <NameCon UId="37" Name="in" />
    </Wire>
    <Wire UId="52">
      <IdentCon UId="28" />
      <NameCon UId="37" Name="operand" />
    </Wire>
  </Wires>
</FlgNet></NetworkSource>
          <ProgrammingLanguage>LAD</ProgrammingLanguage>
        </AttributeList>
        <ObjectList>
          <MultilingualText ID="C" CompositionName="Comment">
            <ObjectList>
              <MultilingualTextItem ID="D" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="E" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
          <MultilingualText ID="F" CompositionName="Title">
            <ObjectList>
              <MultilingualTextItem ID="10" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>mode</Text>
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="11" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>
      <SW.Blocks.CompileUnit ID="12" CompositionName="CompileUnits">
        <AttributeList>
          <NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
  <Parts>
    <Access Scope="GlobalVariable" UId="21">
      <Symbol>
        <Component Name="&quot;I132.0 左UPIN缓存挡料1气缸 伸出到位 NA1-C101a&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1056" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="22">
      <Symbol>
        <Component Name="&quot;I132.1 左UPIN缓存挡料1气缸 缩回到位 NA1-C101b&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1057" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="23">
      <Symbol>
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13008" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="24">
      <Symbol>
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13016" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="25">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="26">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LocalVariable" UId="27">
      <Symbol>
        <Component Name="staManual" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="28">
      <Symbol>
        <Component Name="staAuto" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="29">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              $112</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="condition" />
        <Component Name="Estop" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4888" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="30">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="31">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="32">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="33">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="34">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="GlobalVariable" UId="35">
      <Symbol>
        <Component Name="&quot;Q216.4 NA1C101挡料气缸1伸出&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1732" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="36">
      <Symbol>
        <Component Name="&quot;Q216.5 NA1C101挡料气缸1缩回&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1733" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="37">
      <Symbol>
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13024" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="38">
      <Symbol>
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Component Name="NA1C101左缓存挡料气缸1" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13032" Informative="true" />
      </Symbol>
    </Access>
    <Call UId="39">
      <CallInfo Name="FB4000_Valve" BlockType="FB">
        <IntegerAttribute Name="BlockNumber" Informative="true">4000</IntegerAttribute>
        <DateAttribute Name="ParameterModifiedTS" Informative="true">2025-03-30T09:25:34</DateAttribute>
        <Instance Scope="LocalVariable" UId="40">
          <Component Name="NA1C101左缓存挡料气缸1" />
        </Instance>
        <Parameter Name="I_WRK" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="I_BAS" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="VentValveAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ManualMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="AutoRunMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="E_StopStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ResetStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exFwdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exBkdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CmdClearFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CondFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="simulation" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="hold" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_WRK" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_BAS" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWrk" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsRst" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWR" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FoolProofing" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="error" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
      </CallInfo>
    </Call>
  </Parts>
  <Wires>
    <Wire UId="57">
      <Powerrail />
      <NameCon UId="39" Name="en" />
    </Wire>
    <Wire UId="58">
      <IdentCon UId="21" />
      <NameCon UId="39" Name="I_WRK" />
    </Wire>
    <Wire UId="59">
      <IdentCon UId="22" />
      <NameCon UId="39" Name="I_BAS" />
    </Wire>
    <Wire UId="60">
      <IdentCon UId="23" />
      <NameCon UId="39" Name="FwdAuto" />
    </Wire>
    <Wire UId="61">
      <IdentCon UId="24" />
      <NameCon UId="39" Name="BkdAuto" />
    </Wire>
    <Wire UId="62">
      <OpenCon UId="41" />
      <NameCon UId="39" Name="VentValveAuto" />
    </Wire>
    <Wire UId="63">
      <IdentCon UId="25" />
      <NameCon UId="39" Name="FwdPmt" />
    </Wire>
    <Wire UId="64">
      <IdentCon UId="26" />
      <NameCon UId="39" Name="BkdPmt" />
    </Wire>
    <Wire UId="65">
      <IdentCon UId="27" />
      <NameCon UId="39" Name="ManualMod" />
    </Wire>
    <Wire UId="66">
      <IdentCon UId="28" />
      <NameCon UId="39" Name="AutoRunMod" />
    </Wire>
    <Wire UId="67">
      <IdentCon UId="29" />
      <NameCon UId="39" Name="E_StopStatus" />
    </Wire>
    <Wire UId="68">
      <IdentCon UId="30" />
      <NameCon UId="39" Name="ResetStatus" />
    </Wire>
    <Wire UId="69">
      <OpenCon UId="42" />
      <NameCon UId="39" Name="exFwdBtn" />
    </Wire>
    <Wire UId="70">
      <OpenCon UId="43" />
      <NameCon UId="39" Name="exBkdBtn" />
    </Wire>
    <Wire UId="71">
      <IdentCon UId="31" />
      <NameCon UId="39" Name="CmdClearFoolProofing" />
    </Wire>
    <Wire UId="72">
      <OpenCon UId="44" />
      <NameCon UId="39" Name="CondFoolProofing" />
    </Wire>
    <Wire UId="73">
      <OpenCon UId="45" />
      <NameCon UId="39" Name="simulation" />
    </Wire>
    <Wire UId="74">
      <IdentCon UId="32" />
      <NameCon UId="39" Name="FwdPresetTime" />
    </Wire>
    <Wire UId="75">
      <IdentCon UId="33" />
      <NameCon UId="39" Name="BkdPresetTime" />
    </Wire>
    <Wire UId="76">
      <IdentCon UId="34" />
      <NameCon UId="39" Name="hold" />
    </Wire>
    <Wire UId="77">
      <OpenCon UId="46" />
      <NameCon UId="39" Name="FwdUsedTime" />
    </Wire>
    <Wire UId="78">
      <OpenCon UId="47" />
      <NameCon UId="39" Name="BkdUsedTime" />
    </Wire>
    <Wire UId="79">
      <NameCon UId="39" Name="Q_WRK" />
      <IdentCon UId="35" />
    </Wire>
    <Wire UId="80">
      <NameCon UId="39" Name="Q_BAS" />
      <IdentCon UId="36" />
    </Wire>
    <Wire UId="81">
      <NameCon UId="39" Name="FwdPos" />
      <IdentCon UId="37" />
    </Wire>
    <Wire UId="82">
      <NameCon UId="39" Name="BkdPos" />
      <IdentCon UId="38" />
    </Wire>
    <Wire UId="83">
      <NameCon UId="39" Name="FwdErr" />
      <OpenCon UId="48" />
    </Wire>
    <Wire UId="84">
      <NameCon UId="39" Name="BkdErr" />
      <OpenCon UId="49" />
    </Wire>
    <Wire UId="85">
      <NameCon UId="39" Name="Flt_SnsWrk" />
      <OpenCon UId="50" />
    </Wire>
    <Wire UId="86">
      <NameCon UId="39" Name="Flt_SnsRst" />
      <OpenCon UId="51" />
    </Wire>
    <Wire UId="87">
      <NameCon UId="39" Name="Flt_SnsWR" />
      <OpenCon UId="52" />
    </Wire>
    <Wire UId="88">
      <NameCon UId="39" Name="FwdLockErr" />
      <OpenCon UId="53" />
    </Wire>
    <Wire UId="89">
      <NameCon UId="39" Name="BkdLockErr" />
      <OpenCon UId="54" />
    </Wire>
    <Wire UId="90">
      <NameCon UId="39" Name="FoolProofing" />
      <OpenCon UId="55" />
    </Wire>
    <Wire UId="91">
      <NameCon UId="39" Name="error" />
      <OpenCon UId="56" />
    </Wire>
  </Wires>
</FlgNet></NetworkSource>
          <ProgrammingLanguage>LAD</ProgrammingLanguage>
        </AttributeList>
        <ObjectList>
          <MultilingualText ID="13" CompositionName="Comment">
            <ObjectList>
              <MultilingualTextItem ID="14" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="15" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
          <MultilingualText ID="16" CompositionName="Title">
            <ObjectList>
              <MultilingualTextItem ID="17" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>左缓存挡料气缸1</Text>
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="18" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>
      <SW.Blocks.CompileUnit ID="19" CompositionName="CompileUnits">
        <AttributeList>
          <NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
  <Parts>
    <Access Scope="GlobalVariable" UId="21">
      <Symbol>
        <Component Name="&quot;I132.2 左UPIN缓存挡料2气缸 伸出到位 NA1-C201a&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1058" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="22">
      <Symbol>
        <Component Name="&quot;I132.3 左UPIN缓存挡料2气缸 缩回到位 NA1-C201b&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1059" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="23">
      <Symbol>
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13008" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="24">
      <Symbol>
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13016" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="25">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="26">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LocalVariable" UId="27">
      <Symbol>
        <Component Name="staManual" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="28">
      <Symbol>
        <Component Name="staAuto" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="29">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              <ConstantValue>12</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="condition" />
        <Component Name="Estop" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4888" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="30">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="31">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="32">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="33">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="34">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="GlobalVariable" UId="35">
      <Symbol>
        <Component Name="&quot;Q216.6 NA1C201挡料气缸2伸出&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1734" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="36">
      <Symbol>
        <Component Name="&quot;Q216.7 NA1C201挡料气缸2缩回&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1735" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="37">
      <Symbol>
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13024" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="38">
      <Symbol>
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Component Name="NA1C201左缓存挡料气缸2" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13032" Informative="true" />
      </Symbol>
    </Access>
    <Call UId="39">
      <CallInfo Name="FB4000_Valve" BlockType="FB">
        <IntegerAttribute Name="BlockNumber" Informative="true">4000</IntegerAttribute>
        <DateAttribute Name="ParameterModifiedTS" Informative="true">2025-03-30T09:25:34</DateAttribute>
        <Instance Scope="LocalVariable" UId="40">
          <Component Name="NA1C201左缓存挡料气缸2" />
        </Instance>
        <Parameter Name="I_WRK" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="I_BAS" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="VentValveAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ManualMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="AutoRunMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="E_StopStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ResetStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exFwdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exBkdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CmdClearFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CondFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="simulation" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="hold" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_WRK" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_BAS" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWrk" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsRst" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWR" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FoolProofing" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="error" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
      </CallInfo>
    </Call>
  </Parts>
  <Wires>
    <Wire UId="57">
      <Powerrail />
      <NameCon UId="39" Name="en" />
    </Wire>
    <Wire UId="58">
      <IdentCon UId="21" />
      <NameCon UId="39" Name="I_WRK" />
    </Wire>
    <Wire UId="59">
      <IdentCon UId="22" />
      <NameCon UId="39" Name="I_BAS" />
    </Wire>
    <Wire UId="60">
      <IdentCon UId="23" />
      <NameCon UId="39" Name="FwdAuto" />
    </Wire>
    <Wire UId="61">
      <IdentCon UId="24" />
      <NameCon UId="39" Name="BkdAuto" />
    </Wire>
    <Wire UId="62">
      <OpenCon UId="41" />
      <NameCon UId="39" Name="VentValveAuto" />
    </Wire>
    <Wire UId="63">
      <IdentCon UId="25" />
      <NameCon UId="39" Name="FwdPmt" />
    </Wire>
    <Wire UId="64">
      <IdentCon UId="26" />
      <NameCon UId="39" Name="BkdPmt" />
    </Wire>
    <Wire UId="65">
      <IdentCon UId="27" />
      <NameCon UId="39" Name="ManualMod" />
    </Wire>
    <Wire UId="66">
      <IdentCon UId="28" />
      <NameCon UId="39" Name="AutoRunMod" />
    </Wire>
    <Wire UId="67">
      <IdentCon UId="29" />
      <NameCon UId="39" Name="E_StopStatus" />
    </Wire>
    <Wire UId="68">
      <IdentCon UId="30" />
      <NameCon UId="39" Name="ResetStatus" />
    </Wire>
    <Wire UId="69">
      <OpenCon UId="42" />
      <NameCon UId="39" Name="exFwdBtn" />
    </Wire>
    <Wire UId="70">
      <OpenCon UId="43" />
      <NameCon UId="39" Name="exBkdBtn" />
    </Wire>
    <Wire UId="71">
      <IdentCon UId="31" />
      <NameCon UId="39" Name="CmdClearFoolProofing" />
    </Wire>
    <Wire UId="72">
      <OpenCon UId="44" />
      <NameCon UId="39" Name="CondFoolProofing" />
    </Wire>
    <Wire UId="73">
      <OpenCon UId="45" />
      <NameCon UId="39" Name="simulation" />
    </Wire>
    <Wire UId="74">
      <IdentCon UId="32" />
      <NameCon UId="39" Name="FwdPresetTime" />
    </Wire>
    <Wire UId="75">
      <IdentCon UId="33" />
      <NameCon UId="39" Name="BkdPresetTime" />
    </Wire>
    <Wire UId="76">
      <IdentCon UId="34" />
      <NameCon UId="39" Name="hold" />
    </Wire>
    <Wire UId="77">
      <OpenCon UId="46" />
      <NameCon UId="39" Name="FwdUsedTime" />
    </Wire>
    <Wire UId="78">
      <OpenCon UId="47" />
      <NameCon UId="39" Name="BkdUsedTime" />
    </Wire>
    <Wire UId="79">
      <NameCon UId="39" Name="Q_WRK" />
      <IdentCon UId="35" />
    </Wire>
    <Wire UId="80">
      <NameCon UId="39" Name="Q_BAS" />
      <IdentCon UId="36" />
    </Wire>
    <Wire UId="81">
      <NameCon UId="39" Name="FwdPos" />
      <IdentCon UId="37" />
    </Wire>
    <Wire UId="82">
      <NameCon UId="39" Name="BkdPos" />
      <IdentCon UId="38" />
    </Wire>
    <Wire UId="83">
      <NameCon UId="39" Name="FwdErr" />
      <OpenCon UId="48" />
    </Wire>
    <Wire UId="84">
      <NameCon UId="39" Name="BkdErr" />
      <OpenCon UId="49" />
    </Wire>
    <Wire UId="85">
      <NameCon UId="39" Name="Flt_SnsWrk" />
      <OpenCon UId="50" />
    </Wire>
    <Wire UId="86">
      <NameCon UId="39" Name="Flt_SnsRst" />
      <OpenCon UId="51" />
    </Wire>
    <Wire UId="87">
      <NameCon UId="39" Name="Flt_SnsWR" />
      <OpenCon UId="52" />
    </Wire>
    <Wire UId="88">
      <NameCon UId="39" Name="FwdLockErr" />
      <OpenCon UId="53" />
    </Wire>
    <Wire UId="89">
      <NameCon UId="39" Name="BkdLockErr" />
      <OpenCon UId="54" />
    </Wire>
    <Wire UId="90">
      <NameCon UId="39" Name="FoolProofing" />
      <OpenCon UId="55" />
    </Wire>
    <Wire UId="91">
      <NameCon UId="39" Name="error" />
      <OpenCon UId="56" />
    </Wire>
  </Wires>
</FlgNet></NetworkSource>
          <ProgrammingLanguage>LAD</ProgrammingLanguage>
        </AttributeList>
        <ObjectList>
          <MultilingualText ID="1A" CompositionName="Comment">
            <ObjectList>
              <MultilingualTextItem ID="1B" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="1C" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
          <MultilingualText ID="1D" CompositionName="Title">
            <ObjectList>
              <MultilingualTextItem ID="1E" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>左缓存挡料气缸1</Text>
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="1F" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>
      <SW.Blocks.CompileUnit ID="20" CompositionName="CompileUnits">
        <AttributeList>
          <NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
  <Parts>
    <Access Scope="GlobalVariable" UId="21">
      <Symbol>
        <Component Name="&quot;I132.4 左UPIN缓存挡料3气缸 伸出到位 NA1-C301a&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1060" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="22">
      <Symbol>
        <Component Name="&quot;I132.5 左UPIN缓存挡料3气缸 缩回到位 NA1-C301b&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1061" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="23">
      <Symbol>
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13008" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="24">
      <Symbol>
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13016" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="25">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="26">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LocalVariable" UId="27">
      <Symbol>
        <Component Name="staManual" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="28">
      <Symbol>
        <Component Name="staAuto" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="29">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              <ConstantValue>12</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="condition" />
        <Component Name="Estop" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4888" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="30">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="31">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="32">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="33">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="34">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="GlobalVariable" UId="35">
      <Symbol>
        <Component Name="&quot;Q217.0 NA1C301挡料气缸3伸出&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1736" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="36">
      <Symbol>
        <Component Name="&quot;Q217.1 NA1C301挡料气缸3缩回&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1737" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="37">
      <Symbol>
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13024" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="38">
      <Symbol>
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Component Name="NA1C301左缓存挡料气缸3" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13032" Informative="true" />
      </Symbol>
    </Access>
    <Call UId="39">
      <CallInfo Name="FB4000_Valve" BlockType="FB">
        <IntegerAttribute Name="BlockNumber" Informative="true">4000</IntegerAttribute>
        <DateAttribute Name="ParameterModifiedTS" Informative="true">2025-03-30T09:25:34</DateAttribute>
        <Instance Scope="LocalVariable" UId="40">
          <Component Name="NA1C301左缓存挡料气缸3" />
        </Instance>
        <Parameter Name="I_WRK" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="I_BAS" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="VentValveAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ManualMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="AutoRunMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="E_StopStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ResetStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exFwdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exBkdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CmdClearFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CondFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="simulation" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="hold" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_WRK" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_BAS" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWrk" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsRst" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWR" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FoolProofing" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="error" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
      </CallInfo>
    </Call>
  </Parts>
  <Wires>
    <Wire UId="57">
      <Powerrail />
      <NameCon UId="39" Name="en" />
    </Wire>
    <Wire UId="58">
      <IdentCon UId="21" />
      <NameCon UId="39" Name="I_WRK" />
    </Wire>
    <Wire UId="59">
      <IdentCon UId="22" />
      <NameCon UId="39" Name="I_BAS" />
    </Wire>
    <Wire UId="60">
      <IdentCon UId="23" />
      <NameCon UId="39" Name="FwdAuto" />
    </Wire>
    <Wire UId="61">
      <IdentCon UId="24" />
      <NameCon UId="39" Name="BkdAuto" />
    </Wire>
    <Wire UId="62">
      <OpenCon UId="41" />
      <NameCon UId="39" Name="VentValveAuto" />
    </Wire>
    <Wire UId="63">
      <IdentCon UId="25" />
      <NameCon UId="39" Name="FwdPmt" />
    </Wire>
    <Wire UId="64">
      <IdentCon UId="26" />
      <NameCon UId="39" Name="BkdPmt" />
    </Wire>
    <Wire UId="65">
      <IdentCon UId="27" />
      <NameCon UId="39" Name="ManualMod" />
    </Wire>
    <Wire UId="66">
      <IdentCon UId="28" />
      <NameCon UId="39" Name="AutoRunMod" />
    </Wire>
    <Wire UId="67">
      <IdentCon UId="29" />
      <NameCon UId="39" Name="E_StopStatus" />
    </Wire>
    <Wire UId="68">
      <IdentCon UId="30" />
      <NameCon UId="39" Name="ResetStatus" />
    </Wire>
    <Wire UId="69">
      <OpenCon UId="42" />
      <NameCon UId="39" Name="exFwdBtn" />
    </Wire>
    <Wire UId="70">
      <OpenCon UId="43" />
      <NameCon UId="39" Name="exBkdBtn" />
    </Wire>
    <Wire UId="71">
      <IdentCon UId="31" />
      <NameCon UId="39" Name="CmdClearFoolProofing" />
    </Wire>
    <Wire UId="72">
      <OpenCon UId="44" />
      <NameCon UId="39" Name="CondFoolProofing" />
    </Wire>
    <Wire UId="73">
      <OpenCon UId="45" />
      <NameCon UId="39" Name="simulation" />
    </Wire>
    <Wire UId="74">
      <IdentCon UId="32" />
      <NameCon UId="39" Name="FwdPresetTime" />
    </Wire>
    <Wire UId="75">
      <IdentCon UId="33" />
      <NameCon UId="39" Name="BkdPresetTime" />
    </Wire>
    <Wire UId="76">
      <IdentCon UId="34" />
      <NameCon UId="39" Name="hold" />
    </Wire>
    <Wire UId="77">
      <OpenCon UId="46" />
      <NameCon UId="39" Name="FwdUsedTime" />
    </Wire>
    <Wire UId="78">
      <OpenCon UId="47" />
      <NameCon UId="39" Name="BkdUsedTime" />
    </Wire>
    <Wire UId="79">
      <NameCon UId="39" Name="Q_WRK" />
      <IdentCon UId="35" />
    </Wire>
    <Wire UId="80">
      <NameCon UId="39" Name="Q_BAS" />
      <IdentCon UId="36" />
    </Wire>
    <Wire UId="81">
      <NameCon UId="39" Name="FwdPos" />
      <IdentCon UId="37" />
    </Wire>
    <Wire UId="82">
      <NameCon UId="39" Name="BkdPos" />
      <IdentCon UId="38" />
    </Wire>
    <Wire UId="83">
      <NameCon UId="39" Name="FwdErr" />
      <OpenCon UId="48" />
    </Wire>
    <Wire UId="84">
      <NameCon UId="39" Name="BkdErr" />
      <OpenCon UId="49" />
    </Wire>
    <Wire UId="85">
      <NameCon UId="39" Name="Flt_SnsWrk" />
      <OpenCon UId="50" />
    </Wire>
    <Wire UId="86">
      <NameCon UId="39" Name="Flt_SnsRst" />
      <OpenCon UId="51" />
    </Wire>
    <Wire UId="87">
      <NameCon UId="39" Name="Flt_SnsWR" />
      <OpenCon UId="52" />
    </Wire>
    <Wire UId="88">
      <NameCon UId="39" Name="FwdLockErr" />
      <OpenCon UId="53" />
    </Wire>
    <Wire UId="89">
      <NameCon UId="39" Name="BkdLockErr" />
      <OpenCon UId="54" />
    </Wire>
    <Wire UId="90">
      <NameCon UId="39" Name="FoolProofing" />
      <OpenCon UId="55" />
    </Wire>
    <Wire UId="91">
      <NameCon UId="39" Name="error" />
      <OpenCon UId="56" />
    </Wire>
  </Wires>
</FlgNet></NetworkSource>
          <ProgrammingLanguage>LAD</ProgrammingLanguage>
        </AttributeList>
        <ObjectList>
          <MultilingualText ID="21" CompositionName="Comment">
            <ObjectList>
              <MultilingualTextItem ID="22" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="23" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
          <MultilingualText ID="24" CompositionName="Title">
            <ObjectList>
              <MultilingualTextItem ID="25" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>左缓存挡料气缸1</Text>
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="26" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>
      <SW.Blocks.CompileUnit ID="27" CompositionName="CompileUnits">
        <AttributeList>
          <NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
  <Parts>
    <Access Scope="GlobalVariable" UId="21">
      <Symbol>
        <Component Name="&quot;I15.2&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="122" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="22">
      <Symbol>
        <Component Name="&quot;I15.3&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="123" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="23">
      <Symbol>
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13008" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="24">
      <Symbol>
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13016" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="25">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="26">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LocalVariable" UId="27">
      <Symbol>
        <Component Name="staManual" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="28">
      <Symbol>
        <Component Name="staAuto" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="29">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              <ConstantValue>12</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="condition" />
        <Component Name="Estop" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4888" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="30">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="31">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="32">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="33">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="34">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="GlobalVariable" UId="35">
      <Symbol>
        <Component Name="&quot;Q10.5&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="85" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="36">
      <Symbol>
        <Component Name="&quot;Q10.6&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="86" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="37">
      <Symbol>
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13024" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="38">
      <Symbol>
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Component Name="NA1C401左缓存挡料气缸4" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13032" Informative="true" />
      </Symbol>
    </Access>
    <Call UId="39">
      <CallInfo Name="FB4000_Valve" BlockType="FB">
        <IntegerAttribute Name="BlockNumber" Informative="true">4000</IntegerAttribute>
        <DateAttribute Name="ParameterModifiedTS" Informative="true">2025-03-30T09:25:34</DateAttribute>
        <Instance Scope="LocalVariable" UId="40">
          <Component Name="NA1C401左缓存挡料气缸4" />
        </Instance>
        <Parameter Name="I_WRK" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="I_BAS" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="VentValveAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ManualMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="AutoRunMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="E_StopStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ResetStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exFwdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exBkdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CmdClearFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CondFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="simulation" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="hold" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_WRK" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_BAS" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWrk" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsRst" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWR" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FoolProofing" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="error" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
      </CallInfo>
    </Call>
  </Parts>
  <Wires>
    <Wire UId="57">
      <Powerrail />
      <NameCon UId="39" Name="en" />
    </Wire>
    <Wire UId="58">
      <IdentCon UId="21" />
      <NameCon UId="39" Name="I_WRK" />
    </Wire>
    <Wire UId="59">
      <IdentCon UId="22" />
      <NameCon UId="39" Name="I_BAS" />
    </Wire>
    <Wire UId="60">
      <IdentCon UId="23" />
      <NameCon UId="39" Name="FwdAuto" />
    </Wire>
    <Wire UId="61">
      <IdentCon UId="24" />
      <NameCon UId="39" Name="BkdAuto" />
    </Wire>
    <Wire UId="62">
      <OpenCon UId="41" />
      <NameCon UId="39" Name="VentValveAuto" />
    </Wire>
    <Wire UId="63">
      <IdentCon UId="25" />
      <NameCon UId="39" Name="FwdPmt" />
    </Wire>
    <Wire UId="64">
      <IdentCon UId="26" />
      <NameCon UId="39" Name="BkdPmt" />
    </Wire>
    <Wire UId="65">
      <IdentCon UId="27" />
      <NameCon UId="39" Name="ManualMod" />
    </Wire>
    <Wire UId="66">
      <IdentCon UId="28" />
      <NameCon UId="39" Name="AutoRunMod" />
    </Wire>
    <Wire UId="67">
      <IdentCon UId="29" />
      <NameCon UId="39" Name="E_StopStatus" />
    </Wire>
    <Wire UId="68">
      <IdentCon UId="30" />
      <NameCon UId="39" Name="ResetStatus" />
    </Wire>
    <Wire UId="69">
      <OpenCon UId="42" />
      <NameCon UId="39" Name="exFwdBtn" />
    </Wire>
    <Wire UId="70">
      <OpenCon UId="43" />
      <NameCon UId="39" Name="exBkdBtn" />
    </Wire>
    <Wire UId="71">
      <IdentCon UId="31" />
      <NameCon UId="39" Name="CmdClearFoolProofing" />
    </Wire>
    <Wire UId="72">
      <OpenCon UId="44" />
      <NameCon UId="39" Name="CondFoolProofing" />
    </Wire>
    <Wire UId="73">
      <OpenCon UId="45" />
      <NameCon UId="39" Name="simulation" />
    </Wire>
    <Wire UId="74">
      <IdentCon UId="32" />
      <NameCon UId="39" Name="FwdPresetTime" />
    </Wire>
    <Wire UId="75">
      <IdentCon UId="33" />
      <NameCon UId="39" Name="BkdPresetTime" />
    </Wire>
    <Wire UId="76">
      <IdentCon UId="34" />
      <NameCon UId="39" Name="hold" />
    </Wire>
    <Wire UId="77">
      <OpenCon UId="46" />
      <NameCon UId="39" Name="FwdUsedTime" />
    </Wire>
    <Wire UId="78">
      <OpenCon UId="47" />
      <NameCon UId="39" Name="BkdUsedTime" />
    </Wire>
    <Wire UId="79">
      <NameCon UId="39" Name="Q_WRK" />
      <IdentCon UId="35" />
    </Wire>
    <Wire UId="80">
      <NameCon UId="39" Name="Q_BAS" />
      <IdentCon UId="36" />
    </Wire>
    <Wire UId="81">
      <NameCon UId="39" Name="FwdPos" />
      <IdentCon UId="37" />
    </Wire>
    <Wire UId="82">
      <NameCon UId="39" Name="BkdPos" />
      <IdentCon UId="38" />
    </Wire>
    <Wire UId="83">
      <NameCon UId="39" Name="FwdErr" />
      <OpenCon UId="48" />
    </Wire>
    <Wire UId="84">
      <NameCon UId="39" Name="BkdErr" />
      <OpenCon UId="49" />
    </Wire>
    <Wire UId="85">
      <NameCon UId="39" Name="Flt_SnsWrk" />
      <OpenCon UId="50" />
    </Wire>
    <Wire UId="86">
      <NameCon UId="39" Name="Flt_SnsRst" />
      <OpenCon UId="51" />
    </Wire>
    <Wire UId="87">
      <NameCon UId="39" Name="Flt_SnsWR" />
      <OpenCon UId="52" />
    </Wire>
    <Wire UId="88">
      <NameCon UId="39" Name="FwdLockErr" />
      <OpenCon UId="53" />
    </Wire>
    <Wire UId="89">
      <NameCon UId="39" Name="BkdLockErr" />
      <OpenCon UId="54" />
    </Wire>
    <Wire UId="90">
      <NameCon UId="39" Name="FoolProofing" />
      <OpenCon UId="55" />
    </Wire>
    <Wire UId="91">
      <NameCon UId="39" Name="error" />
      <OpenCon UId="56" />
    </Wire>
  </Wires>
</FlgNet></NetworkSource>
          <ProgrammingLanguage>LAD</ProgrammingLanguage>
        </AttributeList>
        <ObjectList>
          <MultilingualText ID="28" CompositionName="Comment">
            <ObjectList>
              <MultilingualTextItem ID="29" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="2A" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
          <MultilingualText ID="2B" CompositionName="Title">
            <ObjectList>
              <MultilingualTextItem ID="2C" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>左缓存挡料气缸1</Text>
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="2D" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>
      <SW.Blocks.CompileUnit ID="2E" CompositionName="CompileUnits">
        <AttributeList>
          <NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
  <Parts>
    <Access Scope="GlobalVariable" UId="21">
      <Symbol>
        <Component Name="&quot;I132.6 左IPIN缓存料盘抽出气缸 伸出到位 NA1-C401a&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1062" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="22">
      <Symbol>
        <Component Name="&quot;I132.7 左IPIN缓存料盘抽出气缸 缩回到位 NA1-C401b&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1063" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="23">
      <Symbol>
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13008" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="24">
      <Symbol>
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13016" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="25">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="26">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LocalVariable" UId="27">
      <Symbol>
        <Component Name="staManual" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="28">
      <Symbol>
        <Component Name="staAuto" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="29">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              <ConstantValue>12</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="condition" />
        <Component Name="Estop" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4888" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="30">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="31">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="32">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="33">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="34">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="GlobalVariable" UId="35">
      <Symbol>
        <Component Name="&quot;Q217.2 NA1C401料盘抽出气缸伸出&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1738" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="36">
      <Symbol>
        <Component Name="&quot;Q217.3 NA1C401料盘抽出气缸缩回&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1739" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="37">
      <Symbol>
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13024" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="38">
      <Symbol>
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Component Name="NA1C401左缓存料盘抽出气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13032" Informative="true" />
      </Symbol>
    </Access>
    <Call UId="39">
      <CallInfo Name="FB4000_Valve" BlockType="FB">
        <IntegerAttribute Name="BlockNumber" Informative="true">4000</IntegerAttribute>
        <DateAttribute Name="ParameterModifiedTS" Informative="true">2025-03-30T09:25:34</DateAttribute>
        <Instance Scope="LocalVariable" UId="40">
          <Component Name="NA1C401左缓存料盘抽出气缸" />
        </Instance>
        <Parameter Name="I_WRK" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="I_BAS" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="VentValveAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ManualMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="AutoRunMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="E_StopStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ResetStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exFwdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exBkdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CmdClearFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CondFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="simulation" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="hold" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_WRK" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_BAS" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWrk" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsRst" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWR" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FoolProofing" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="error" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
      </CallInfo>
    </Call>
  </Parts>
  <Wires>
    <Wire UId="57">
      <Powerrail />
      <NameCon UId="39" Name="en" />
    </Wire>
    <Wire UId="58">
      <IdentCon UId="21" />
      <NameCon UId="39" Name="I_WRK" />
    </Wire>
    <Wire UId="59">
      <IdentCon UId="22" />
      <NameCon UId="39" Name="I_BAS" />
    </Wire>
    <Wire UId="60">
      <IdentCon UId="23" />
      <NameCon UId="39" Name="FwdAuto" />
    </Wire>
    <Wire UId="61">
      <IdentCon UId="24" />
      <NameCon UId="39" Name="BkdAuto" />
    </Wire>
    <Wire UId="62">
      <OpenCon UId="41" />
      <NameCon UId="39" Name="VentValveAuto" />
    </Wire>
    <Wire UId="63">
      <IdentCon UId="25" />
      <NameCon UId="39" Name="FwdPmt" />
    </Wire>
    <Wire UId="64">
      <IdentCon UId="26" />
      <NameCon UId="39" Name="BkdPmt" />
    </Wire>
    <Wire UId="65">
      <IdentCon UId="27" />
      <NameCon UId="39" Name="ManualMod" />
    </Wire>
    <Wire UId="66">
      <IdentCon UId="28" />
      <NameCon UId="39" Name="AutoRunMod" />
    </Wire>
    <Wire UId="67">
      <IdentCon UId="29" />
      <NameCon UId="39" Name="E_StopStatus" />
    </Wire>
    <Wire UId="68">
      <IdentCon UId="30" />
      <NameCon UId="39" Name="ResetStatus" />
    </Wire>
    <Wire UId="69">
      <OpenCon UId="42" />
      <NameCon UId="39" Name="exFwdBtn" />
    </Wire>
    <Wire UId="70">
      <OpenCon UId="43" />
      <NameCon UId="39" Name="exBkdBtn" />
    </Wire>
    <Wire UId="71">
      <IdentCon UId="31" />
      <NameCon UId="39" Name="CmdClearFoolProofing" />
    </Wire>
    <Wire UId="72">
      <OpenCon UId="44" />
      <NameCon UId="39" Name="CondFoolProofing" />
    </Wire>
    <Wire UId="73">
      <OpenCon UId="45" />
      <NameCon UId="39" Name="simulation" />
    </Wire>
    <Wire UId="74">
      <IdentCon UId="32" />
      <NameCon UId="39" Name="FwdPresetTime" />
    </Wire>
    <Wire UId="75">
      <IdentCon UId="33" />
      <NameCon UId="39" Name="BkdPresetTime" />
    </Wire>
    <Wire UId="76">
      <IdentCon UId="34" />
      <NameCon UId="39" Name="hold" />
    </Wire>
    <Wire UId="77">
      <OpenCon UId="46" />
      <NameCon UId="39" Name="FwdUsedTime" />
    </Wire>
    <Wire UId="78">
      <OpenCon UId="47" />
      <NameCon UId="39" Name="BkdUsedTime" />
    </Wire>
    <Wire UId="79">
      <NameCon UId="39" Name="Q_WRK" />
      <IdentCon UId="35" />
    </Wire>
    <Wire UId="80">
      <NameCon UId="39" Name="Q_BAS" />
      <IdentCon UId="36" />
    </Wire>
    <Wire UId="81">
      <NameCon UId="39" Name="FwdPos" />
      <IdentCon UId="37" />
    </Wire>
    <Wire UId="82">
      <NameCon UId="39" Name="BkdPos" />
      <IdentCon UId="38" />
    </Wire>
    <Wire UId="83">
      <NameCon UId="39" Name="FwdErr" />
      <OpenCon UId="48" />
    </Wire>
    <Wire UId="84">
      <NameCon UId="39" Name="BkdErr" />
      <OpenCon UId="49" />
    </Wire>
    <Wire UId="85">
      <NameCon UId="39" Name="Flt_SnsWrk" />
      <OpenCon UId="50" />
    </Wire>
    <Wire UId="86">
      <NameCon UId="39" Name="Flt_SnsRst" />
      <OpenCon UId="51" />
    </Wire>
    <Wire UId="87">
      <NameCon UId="39" Name="Flt_SnsWR" />
      <OpenCon UId="52" />
    </Wire>
    <Wire UId="88">
      <NameCon UId="39" Name="FwdLockErr" />
      <OpenCon UId="53" />
    </Wire>
    <Wire UId="89">
      <NameCon UId="39" Name="BkdLockErr" />
      <OpenCon UId="54" />
    </Wire>
    <Wire UId="90">
      <NameCon UId="39" Name="FoolProofing" />
      <OpenCon UId="55" />
    </Wire>
    <Wire UId="91">
      <NameCon UId="39" Name="error" />
      <OpenCon UId="56" />
    </Wire>
  </Wires>
</FlgNet></NetworkSource>
          <ProgrammingLanguage>LAD</ProgrammingLanguage>
        </AttributeList>
        <ObjectList>
          <MultilingualText ID="2F" CompositionName="Comment">
            <ObjectList>
              <MultilingualTextItem ID="30" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="31" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
          <MultilingualText ID="32" CompositionName="Title">
            <ObjectList>
              <MultilingualTextItem ID="33" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>左缓存挡料气缸1</Text>
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="34" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>
      <SW.Blocks.CompileUnit ID="35" CompositionName="CompileUnits">
        <AttributeList>
          <NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
  <Parts>
    <Access Scope="GlobalVariable" UId="21">
      <Symbol>
        <Component Name="&quot;I133.0 左IPIN缓存料盘定位气缸 伸出到位 NA1-C501a&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1064" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="22">
      <Symbol>
        <Component Name="&quot;I133.2 左IPIN缓存料盘定位气缸 伸出到位 NA1-C501c&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1066" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="23">
      <Symbol>
        <Component Name="&quot;I133.1 左IPIN缓存料盘定位气缸 缩回到位 NA1-C501b&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1065" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="24">
      <Symbol>
        <Component Name="&quot;I133.3 左IPIN缓存料盘定位气缸 缩回到位 NA1-C501d&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1067" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="25">
      <Symbol>
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13168" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="26">
      <Symbol>
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13176" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="27">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="28">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LocalVariable" UId="29">
      <Symbol>
        <Component Name="staManual" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="30">
      <Symbol>
        <Component Name="staAuto" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="31">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              <ConstantValue>12</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="condition" />
        <Component Name="Estop" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4888" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="32">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="33">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="34">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="35">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="36">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="GlobalVariable" UId="37">
      <Symbol>
        <Component Name="&quot;Q217.4 NA1C501料盘定位气缸伸出&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1740" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="38">
      <Symbol>
        <Component Name="&quot;Q217.5 NA1C501料盘定位气缸缩回&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1741" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="39">
      <Symbol>
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13184" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="40">
      <Symbol>
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Component Name="NA1C501左缓存料盘定位气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13192" Informative="true" />
      </Symbol>
    </Access>
    <Part Name="Contact" UId="41" />
    <Part Name="Contact" UId="42" />
    <Part Name="Contact" UId="43" />
    <Part Name="Contact" UId="44" />
    <Call UId="45">
      <CallInfo Name="FB4000_Valve" BlockType="FB">
        <IntegerAttribute Name="BlockNumber" Informative="true">4000</IntegerAttribute>
        <DateAttribute Name="ParameterModifiedTS" Informative="true">2025-03-30T09:25:34</DateAttribute>
        <Instance Scope="LocalVariable" UId="46">
          <Component Name="NA1C501左缓存料盘定位气缸" />
        </Instance>
        <Parameter Name="I_WRK" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="I_BAS" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="VentValveAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ManualMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="AutoRunMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="E_StopStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ResetStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exFwdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exBkdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CmdClearFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CondFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="simulation" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="hold" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_WRK" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_BAS" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWrk" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsRst" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWR" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FoolProofing" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="error" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
      </CallInfo>
    </Call>
  </Parts>
  <Wires>
    <Wire UId="63">
      <Powerrail />
      <NameCon UId="45" Name="en" />
      <NameCon UId="41" Name="in" />
      <NameCon UId="43" Name="in" />
    </Wire>
    <Wire UId="64">
      <IdentCon UId="21" />
      <NameCon UId="41" Name="operand" />
    </Wire>
    <Wire UId="65">
      <NameCon UId="41" Name="out" />
      <NameCon UId="42" Name="in" />
    </Wire>
    <Wire UId="66">
      <IdentCon UId="22" />
      <NameCon UId="42" Name="operand" />
    </Wire>
    <Wire UId="67">
      <NameCon UId="42" Name="out" />
      <NameCon UId="45" Name="I_WRK" />
    </Wire>
    <Wire UId="68">
      <IdentCon UId="23" />
      <NameCon UId="43" Name="operand" />
    </Wire>
    <Wire UId="69">
      <NameCon UId="43" Name="out" />
      <NameCon UId="44" Name="in" />
    </Wire>
    <Wire UId="70">
      <IdentCon UId="24" />
      <NameCon UId="44" Name="operand" />
    </Wire>
    <Wire UId="71">
      <NameCon UId="44" Name="out" />
      <NameCon UId="45" Name="I_BAS" />
    </Wire>
    <Wire UId="72">
      <IdentCon UId="25" />
      <NameCon UId="45" Name="FwdAuto" />
    </Wire>
    <Wire UId="73">
      <IdentCon UId="26" />
      <NameCon UId="45" Name="BkdAuto" />
    </Wire>
    <Wire UId="74">
      <OpenCon UId="47" />
      <NameCon UId="45" Name="VentValveAuto" />
    </Wire>
    <Wire UId="75">
      <IdentCon UId="27" />
      <NameCon UId="45" Name="FwdPmt" />
    </Wire>
    <Wire UId="76">
      <IdentCon UId="28" />
      <NameCon UId="45" Name="BkdPmt" />
    </Wire>
    <Wire UId="77">
      <IdentCon UId="29" />
      <NameCon UId="45" Name="ManualMod" />
    </Wire>
    <Wire UId="78">
      <IdentCon UId="30" />
      <NameCon UId="45" Name="AutoRunMod" />
    </Wire>
    <Wire UId="79">
      <IdentCon UId="31" />
      <NameCon UId="45" Name="E_StopStatus" />
    </Wire>
    <Wire UId="80">
      <IdentCon UId="32" />
      <NameCon UId="45" Name="ResetStatus" />
    </Wire>
    <Wire UId="81">
      <OpenCon UId="48" />
      <NameCon UId="45" Name="exFwdBtn" />
    </Wire>
    <Wire UId="82">
      <OpenCon UId="49" />
      <NameCon UId="45" Name="exBkdBtn" />
    </Wire>
    <Wire UId="83">
      <IdentCon UId="33" />
      <NameCon UId="45" Name="CmdClearFoolProofing" />
    </Wire>
    <Wire UId="84">
      <OpenCon UId="50" />
      <NameCon UId="45" Name="CondFoolProofing" />
    </Wire>
    <Wire UId="85">
      <OpenCon UId="51" />
      <NameCon UId="45" Name="simulation" />
    </Wire>
    <Wire UId="86">
      <IdentCon UId="34" />
      <NameCon UId="45" Name="FwdPresetTime" />
    </Wire>
    <Wire UId="87">
      <IdentCon UId="35" />
      <NameCon UId="45" Name="BkdPresetTime" />
    </Wire>
    <Wire UId="88">
      <IdentCon UId="36" />
      <NameCon UId="45" Name="hold" />
    </Wire>
    <Wire UId="89">
      <OpenCon UId="52" />
      <NameCon UId="45" Name="FwdUsedTime" />
    </Wire>
    <Wire UId="90">
      <OpenCon UId="53" />
      <NameCon UId="45" Name="BkdUsedTime" />
    </Wire>
    <Wire UId="91">
      <NameCon UId="45" Name="Q_WRK" />
      <IdentCon UId="37" />
    </Wire>
    <Wire UId="92">
      <NameCon UId="45" Name="Q_BAS" />
      <IdentCon UId="38" />
    </Wire>
    <Wire UId="93">
      <NameCon UId="45" Name="FwdPos" />
      <IdentCon UId="39" />
    </Wire>
    <Wire UId="94">
      <NameCon UId="45" Name="BkdPos" />
      <IdentCon UId="40" />
    </Wire>
    <Wire UId="95">
      <NameCon UId="45" Name="FwdErr" />
      <OpenCon UId="54" />
    </Wire>
    <Wire UId="96">
      <NameCon UId="45" Name="BkdErr" />
      <OpenCon UId="55" />
    </Wire>
    <Wire UId="97">
      <NameCon UId="45" Name="Flt_SnsWrk" />
      <OpenCon UId="56" />
    </Wire>
    <Wire UId="98">
      <NameCon UId="45" Name="Flt_SnsRst" />
      <OpenCon UId="57" />
    </Wire>
    <Wire UId="99">
      <NameCon UId="45" Name="Flt_SnsWR" />
      <OpenCon UId="58" />
    </Wire>
    <Wire UId="100">
      <NameCon UId="45" Name="FwdLockErr" />
      <OpenCon UId="59" />
    </Wire>
    <Wire UId="101">
      <NameCon UId="45" Name="BkdLockErr" />
      <OpenCon UId="60" />
    </Wire>
    <Wire UId="102">
      <NameCon UId="45" Name="FoolProofing" />
      <OpenCon UId="61" />
    </Wire>
    <Wire UId="103">
      <NameCon UId="45" Name="error" />
      <OpenCon UId="62" />
    </Wire>
  </Wires>
</FlgNet></NetworkSource>
          <ProgrammingLanguage>LAD</ProgrammingLanguage>
        </AttributeList>
        <ObjectList>
          <MultilingualText ID="36" CompositionName="Comment">
            <ObjectList>
              <MultilingualTextItem ID="37" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="38" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
          <MultilingualText ID="39" CompositionName="Title">
            <ObjectList>
              <MultilingualTextItem ID="3A" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>左缓存料盘定位气缸</Text>
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="3B" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>
      <SW.Blocks.CompileUnit ID="3C" CompositionName="CompileUnits">
        <AttributeList>
          <NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
  <Parts>
    <Access Scope="GlobalVariable" UId="21">
      <Symbol>
        <Component Name="&quot;I133.4 左IPIN料盒搬运升降气缸 伸出到位 NA1-C601a&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1068" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="22">
      <Symbol>
        <Component Name="&quot;I133.5 左IPIN料盒搬运升降气缸 缩回到位 NA1-C601b&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1069" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="23">
      <Symbol>
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13008" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="24">
      <Symbol>
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13016" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="25">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="26">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LocalVariable" UId="27">
      <Symbol>
        <Component Name="staManual" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="28">
      <Symbol>
        <Component Name="staAuto" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="29">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              <ConstantValue>12</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="condition" />
        <Component Name="Estop" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4888" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="30">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="31">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="32">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="33">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="34">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="GlobalVariable" UId="35">
      <Symbol>
        <Component Name="&quot;Q216.0 NA1C601料盒搬运升降气缸伸出&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1728" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="36">
      <Symbol>
        <Component Name="&quot;Q216.1 NA1C601料盒搬运升降气缸缩回&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1729" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="37">
      <Symbol>
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13024" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="38">
      <Symbol>
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13032" Informative="true" />
      </Symbol>
    </Access>
    <Call UId="39">
      <CallInfo Name="FB4000_Valve" BlockType="FB">
        <IntegerAttribute Name="BlockNumber" Informative="true">4000</IntegerAttribute>
        <DateAttribute Name="ParameterModifiedTS" Informative="true">2025-03-30T09:25:34</DateAttribute>
        <Instance Scope="LocalVariable" UId="40">
          <Component Name="NA1C601左缓存料盒搬运升降气缸" />
        </Instance>
        <Parameter Name="I_WRK" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="I_BAS" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="VentValveAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ManualMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="AutoRunMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="E_StopStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ResetStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exFwdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exBkdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CmdClearFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CondFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="simulation" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="hold" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_WRK" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_BAS" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWrk" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsRst" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWR" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FoolProofing" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="error" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
      </CallInfo>
    </Call>
  </Parts>
  <Wires>
    <Wire UId="57">
      <Powerrail />
      <NameCon UId="39" Name="en" />
    </Wire>
    <Wire UId="58">
      <IdentCon UId="21" />
      <NameCon UId="39" Name="I_WRK" />
    </Wire>
    <Wire UId="59">
      <IdentCon UId="22" />
      <NameCon UId="39" Name="I_BAS" />
    </Wire>
    <Wire UId="60">
      <IdentCon UId="23" />
      <NameCon UId="39" Name="FwdAuto" />
    </Wire>
    <Wire UId="61">
      <IdentCon UId="24" />
      <NameCon UId="39" Name="BkdAuto" />
    </Wire>
    <Wire UId="62">
      <OpenCon UId="41" />
      <NameCon UId="39" Name="VentValveAuto" />
    </Wire>
    <Wire UId="63">
      <IdentCon UId="25" />
      <NameCon UId="39" Name="FwdPmt" />
    </Wire>
    <Wire UId="64">
      <IdentCon UId="26" />
      <NameCon UId="39" Name="BkdPmt" />
    </Wire>
    <Wire UId="65">
      <IdentCon UId="27" />
      <NameCon UId="39" Name="ManualMod" />
    </Wire>
    <Wire UId="66">
      <IdentCon UId="28" />
      <NameCon UId="39" Name="AutoRunMod" />
    </Wire>
    <Wire UId="67">
      <IdentCon UId="29" />
      <NameCon UId="39" Name="E_StopStatus" />
    </Wire>
    <Wire UId="68">
      <IdentCon UId="30" />
      <NameCon UId="39" Name="ResetStatus" />
    </Wire>
    <Wire UId="69">
      <OpenCon UId="42" />
      <NameCon UId="39" Name="exFwdBtn" />
    </Wire>
    <Wire UId="70">
      <OpenCon UId="43" />
      <NameCon UId="39" Name="exBkdBtn" />
    </Wire>
    <Wire UId="71">
      <IdentCon UId="31" />
      <NameCon UId="39" Name="CmdClearFoolProofing" />
    </Wire>
    <Wire UId="72">
      <OpenCon UId="44" />
      <NameCon UId="39" Name="CondFoolProofing" />
    </Wire>
    <Wire UId="73">
      <OpenCon UId="45" />
      <NameCon UId="39" Name="simulation" />
    </Wire>
    <Wire UId="74">
      <IdentCon UId="32" />
      <NameCon UId="39" Name="FwdPresetTime" />
    </Wire>
    <Wire UId="75">
      <IdentCon UId="33" />
      <NameCon UId="39" Name="BkdPresetTime" />
    </Wire>
    <Wire UId="76">
      <IdentCon UId="34" />
      <NameCon UId="39" Name="hold" />
    </Wire>
    <Wire UId="77">
      <OpenCon UId="46" />
      <NameCon UId="39" Name="FwdUsedTime" />
    </Wire>
    <Wire UId="78">
      <OpenCon UId="47" />
      <NameCon UId="39" Name="BkdUsedTime" />
    </Wire>
    <Wire UId="79">
      <NameCon UId="39" Name="Q_WRK" />
      <IdentCon UId="35" />
    </Wire>
    <Wire UId="80">
      <NameCon UId="39" Name="Q_BAS" />
      <IdentCon UId="36" />
    </Wire>
    <Wire UId="81">
      <NameCon UId="39" Name="FwdPos" />
      <IdentCon UId="37" />
    </Wire>
    <Wire UId="82">
      <NameCon UId="39" Name="BkdPos" />
      <IdentCon UId="38" />
    </Wire>
    <Wire UId="83">
      <NameCon UId="39" Name="FwdErr" />
      <OpenCon UId="48" />
    </Wire>
    <Wire UId="84">
      <NameCon UId="39" Name="BkdErr" />
      <OpenCon UId="49" />
    </Wire>
    <Wire UId="85">
      <NameCon UId="39" Name="Flt_SnsWrk" />
      <OpenCon UId="50" />
    </Wire>
    <Wire UId="86">
      <NameCon UId="39" Name="Flt_SnsRst" />
      <OpenCon UId="51" />
    </Wire>
    <Wire UId="87">
      <NameCon UId="39" Name="Flt_SnsWR" />
      <OpenCon UId="52" />
    </Wire>
    <Wire UId="88">
      <NameCon UId="39" Name="FwdLockErr" />
      <OpenCon UId="53" />
    </Wire>
    <Wire UId="89">
      <NameCon UId="39" Name="BkdLockErr" />
      <OpenCon UId="54" />
    </Wire>
    <Wire UId="90">
      <NameCon UId="39" Name="FoolProofing" />
      <OpenCon UId="55" />
    </Wire>
    <Wire UId="91">
      <NameCon UId="39" Name="error" />
      <OpenCon UId="56" />
    </Wire>
  </Wires>
</FlgNet></NetworkSource>
          <ProgrammingLanguage>LAD</ProgrammingLanguage>
        </AttributeList>
        <ObjectList>
          <MultilingualText ID="3D" CompositionName="Comment">
            <ObjectList>
              <MultilingualTextItem ID="3E" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="3F" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
          <MultilingualText ID="40" CompositionName="Title">
            <ObjectList>
              <MultilingualTextItem ID="41" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>左缓存挡料气缸1</Text>
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="42" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>
      <SW.Blocks.CompileUnit ID="43" CompositionName="CompileUnits">
        <AttributeList>
          <NetworkSource><FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
  <Parts>
    <Access Scope="GlobalVariable" UId="21">
      <Symbol>
        <Component Name="&quot;I133.6 左IPIN料盒夹爪气缸 伸出到位 NA1-C701a&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1070" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="22">
      <Symbol>
        <Component Name="&quot;I15.0&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="120" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="23">
      <Symbol>
        <Component Name="&quot;I133.7 左IPIN料盒夹爪气缸 缩回到位 NA1-C701b&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="1071" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="24">
      <Symbol>
        <Component Name="&quot;I15.1&quot;" />
        <Address Area="Input" Type="Bool" BitOffset="121" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="25">
      <Symbol>
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13168" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="26">
      <Symbol>
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13176" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="27">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="28">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LocalVariable" UId="29">
      <Symbol>
        <Component Name="staManual" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="30">
      <Symbol>
        <Component Name="staAuto" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="31">
      <Symbol>
        <Component Name="unitMode" />
        <Component Name="unit" AccessModifier="Array">
          <Access Scope="LiteralConstant">
            <Constant>
              <ConstantType>DInt</ConstantType>
              <ConstantValue>12</ConstantValue>
              <StringAttribute Name="Format" Informative="true">Dec_signed</StringAttribute>
            </Constant>
          </Access>
        </Component>
        <Component Name="unitMode" />
        <Component Name="condition" />
        <Component Name="Estop" />
        <Address Area="None" Type="Bool" BlockNumber="30001" BitOffset="4888" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="32">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LocalVariable" UId="33">
      <Symbol>
        <Component Name="statReset" />
      </Symbol>
    </Access>
    <Access Scope="LiteralConstant" UId="34">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="35">
      <Constant>
        <ConstantType>Real</ConstantType>
        <ConstantValue>10.0</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Real</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="LiteralConstant" UId="36">
      <Constant>
        <ConstantType>Bool</ConstantType>
        <ConstantValue>TRUE</ConstantValue>
        <StringAttribute Name="Format" Informative="true">Bool</StringAttribute>
      </Constant>
    </Access>
    <Access Scope="GlobalVariable" UId="37">
      <Symbol>
        <Component Name="&quot;Q216.2 NA1C701料盒搬运夹爪气缸伸出&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1730" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="38">
      <Symbol>
        <Component Name="&quot;Q216.3 NA1C701料盒搬运夹爪气缸缩回&quot;" />
        <Address Area="Output" Type="Bool" BitOffset="1731" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="39">
      <Symbol>
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13184" Informative="true" />
      </Symbol>
    </Access>
    <Access Scope="GlobalVariable" UId="40">
      <Symbol>
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        <Address Area="None" Type="Bool" BlockNumber="62" BitOffset="13192" Informative="true" />
      </Symbol>
    </Access>
    <Part Name="Contact" UId="41" />
    <Part Name="Contact" UId="42" />
    <Part Name="Contact" UId="43" />
    <Part Name="Contact" UId="44" />
    <Call UId="45">
      <CallInfo Name="FB4000_Valve" BlockType="FB">
        <IntegerAttribute Name="BlockNumber" Informative="true">4000</IntegerAttribute>
        <DateAttribute Name="ParameterModifiedTS" Informative="true">2025-03-30T09:25:34</DateAttribute>
        <Instance Scope="LocalVariable" UId="46">
          <Component Name="NA1C701左缓存料盒搬运夹爪气缸" />
        </Instance>
        <Parameter Name="I_WRK" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="I_BAS" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="VentValveAuto" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPmt" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ManualMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="AutoRunMod" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="E_StopStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="ResetStatus" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exFwdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="exBkdBtn" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CmdClearFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="CondFoolProofing" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="simulation" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPresetTime" Section="Input" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="hold" Section="Input" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_WRK" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Q_BAS" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdPos" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWrk" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsRst" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="Flt_SnsWR" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdLockErr" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FoolProofing" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="error" Section="Output" Type="Bool">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="FwdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
        <Parameter Name="BkdUsedTime" Section="InOut" Type="Real">
          <StringAttribute Name="InterfaceFlags" Informative="true">S7_Visible</StringAttribute>
        </Parameter>
      </CallInfo>
    </Call>
  </Parts>
  <Wires>
    <Wire UId="63">
      <Powerrail />
      <NameCon UId="45" Name="en" />
      <NameCon UId="41" Name="in" />
      <NameCon UId="43" Name="in" />
    </Wire>
    <Wire UId="64">
      <IdentCon UId="21" />
      <NameCon UId="41" Name="operand" />
    </Wire>
    <Wire UId="65">
      <NameCon UId="41" Name="out" />
      <NameCon UId="42" Name="in" />
    </Wire>
    <Wire UId="66">
      <IdentCon UId="22" />
      <NameCon UId="42" Name="operand" />
    </Wire>
    <Wire UId="67">
      <NameCon UId="42" Name="out" />
      <NameCon UId="45" Name="I_WRK" />
    </Wire>
    <Wire UId="68">
      <IdentCon UId="23" />
      <NameCon UId="43" Name="operand" />
    </Wire>
    <Wire UId="69">
      <NameCon UId="43" Name="out" />
      <NameCon UId="44" Name="in" />
    </Wire>
    <Wire UId="70">
      <IdentCon UId="24" />
      <NameCon UId="44" Name="operand" />
    </Wire>
    <Wire UId="71">
      <NameCon UId="44" Name="out" />
      <NameCon UId="45" Name="I_BAS" />
    </Wire>
    <Wire UId="72">
      <IdentCon UId="25" />
      <NameCon UId="45" Name="FwdAuto" />
    </Wire>
    <Wire UId="73">
      <IdentCon UId="26" />
      <NameCon UId="45" Name="BkdAuto" />
    </Wire>
    <Wire UId="74">
      <OpenCon UId="47" />
      <NameCon UId="45" Name="VentValveAuto" />
    </Wire>
    <Wire UId="75">
      <IdentCon UId="27" />
      <NameCon UId="45" Name="FwdPmt" />
    </Wire>
    <Wire UId="76">
      <IdentCon UId="28" />
      <NameCon UId="45" Name="BkdPmt" />
    </Wire>
    <Wire UId="77">
      <IdentCon UId="29" />
      <NameCon UId="45" Name="ManualMod" />
    </Wire>
    <Wire UId="78">
      <IdentCon UId="30" />
      <NameCon UId="45" Name="AutoRunMod" />
    </Wire>
    <Wire UId="79">
      <IdentCon UId="31" />
      <NameCon UId="45" Name="E_StopStatus" />
    </Wire>
    <Wire UId="80">
      <IdentCon UId="32" />
      <NameCon UId="45" Name="ResetStatus" />
    </Wire>
    <Wire UId="81">
      <OpenCon UId="48" />
      <NameCon UId="45" Name="exFwdBtn" />
    </Wire>
    <Wire UId="82">
      <OpenCon UId="49" />
      <NameCon UId="45" Name="exBkdBtn" />
    </Wire>
    <Wire UId="83">
      <IdentCon UId="33" />
      <NameCon UId="45" Name="CmdClearFoolProofing" />
    </Wire>
    <Wire UId="84">
      <OpenCon UId="50" />
      <NameCon UId="45" Name="CondFoolProofing" />
    </Wire>
    <Wire UId="85">
      <OpenCon UId="51" />
      <NameCon UId="45" Name="simulation" />
    </Wire>
    <Wire UId="86">
      <IdentCon UId="34" />
      <NameCon UId="45" Name="FwdPresetTime" />
    </Wire>
    <Wire UId="87">
      <IdentCon UId="35" />
      <NameCon UId="45" Name="BkdPresetTime" />
    </Wire>
    <Wire UId="88">
      <IdentCon UId="36" />
      <NameCon UId="45" Name="hold" />
    </Wire>
    <Wire UId="89">
      <OpenCon UId="52" />
      <NameCon UId="45" Name="FwdUsedTime" />
    </Wire>
    <Wire UId="90">
      <OpenCon UId="53" />
      <NameCon UId="45" Name="BkdUsedTime" />
    </Wire>
    <Wire UId="91">
      <NameCon UId="45" Name="Q_WRK" />
      <IdentCon UId="37" />
    </Wire>
    <Wire UId="92">
      <NameCon UId="45" Name="Q_BAS" />
      <IdentCon UId="38" />
    </Wire>
    <Wire UId="93">
      <NameCon UId="45" Name="FwdPos" />
      <IdentCon UId="39" />
    </Wire>
    <Wire UId="94">
      <NameCon UId="45" Name="BkdPos" />
      <IdentCon UId="40" />
    </Wire>
    <Wire UId="95">
      <NameCon UId="45" Name="FwdErr" />
      <OpenCon UId="54" />
    </Wire>
    <Wire UId="96">
      <NameCon UId="45" Name="BkdErr" />
      <OpenCon UId="55" />
    </Wire>
    <Wire UId="97">
      <NameCon UId="45" Name="Flt_SnsWrk" />
      <OpenCon UId="56" />
    </Wire>
    <Wire UId="98">
      <NameCon UId="45" Name="Flt_SnsRst" />
      <OpenCon UId="57" />
    </Wire>
    <Wire UId="99">
      <NameCon UId="45" Name="Flt_SnsWR" />
      <OpenCon UId="58" />
    </Wire>
    <Wire UId="100">
      <NameCon UId="45" Name="FwdLockErr" />
      <OpenCon UId="59" />
    </Wire>
    <Wire UId="101">
      <NameCon UId="45" Name="BkdLockErr" />
      <OpenCon UId="60" />
    </Wire>
    <Wire UId="102">
      <NameCon UId="45" Name="FoolProofing" />
      <OpenCon UId="61" />
    </Wire>
    <Wire UId="103">
      <NameCon UId="45" Name="error" />
      <OpenCon UId="62" />
    </Wire>
  </Wires>
</FlgNet></NetworkSource>
          <ProgrammingLanguage>LAD</ProgrammingLanguage>
        </AttributeList>
        <ObjectList>
          <MultilingualText ID="44" CompositionName="Comment">
            <ObjectList>
              <MultilingualTextItem ID="45" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="46" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
          <MultilingualText ID="47" CompositionName="Title">
            <ObjectList>
              <MultilingualTextItem ID="48" CompositionName="Items">
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>左缓存料盒搬运夹爪气缸</Text>
                </AttributeList>
              </MultilingualTextItem>
              <MultilingualTextItem ID="49" CompositionName="Items">
                <AttributeList>
                  <Culture>en-GB</Culture>
                  <Text />
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>
      <MultilingualText ID="4A" CompositionName="Title">
        <ObjectList>
          <MultilingualTextItem ID="4B" CompositionName="Items">
            <AttributeList>
              <Culture>zh-CN</Culture>
              <Text>去漆皮部套</Text>
            </AttributeList>
          </MultilingualTextItem>
          <MultilingualTextItem ID="4C" CompositionName="Items">
            <AttributeList>
              <Culture>en-GB</Culture>
              <Text />
            </AttributeList>
          </MultilingualTextItem>
        </ObjectList>
      </MultilingualText>
    </ObjectList>
  </SW.Blocks.FB>
</Document>