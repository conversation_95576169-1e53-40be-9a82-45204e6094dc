using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace OpennessTool.Core
{
    /// <summary>
    /// 模板处理器，负责读取和处理XML模板文件
    /// 对应Python版本的模板处理功能
    /// </summary>
    public class TemplateProcessor
    {
        private readonly string _templateFilePath;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="templateFilePath">模板文件路径</param>
        public TemplateProcessor(string templateFilePath)
        {
            _templateFilePath = templateFilePath ?? throw new ArgumentNullException(nameof(templateFilePath));

            if (!File.Exists(_templateFilePath))
            {
                throw new FileNotFoundException($"模板文件不存在: {_templateFilePath}");
            }
        }

        /// <summary>
        /// 读取模板文件的指定行范围
        /// </summary>
        /// <param name="startLine">起始行号（1-based）</param>
        /// <param name="endLine">结束行号（1-based，包含）</param>
        /// <returns>指定范围的行内容</returns>
        public List<string> GetTemplateLines(int startLine, int endLine)
        {
            var result = new List<string>();

            try
            {
                string[] allLines = File.ReadAllLines(_templateFilePath, Encoding.UTF8);

                // 转换为0-based索引
                int startIndex = Math.Max(0, startLine - 1);
                int endIndex = Math.Min(allLines.Length - 1, endLine - 1);

                if (startIndex <= endIndex && startIndex < allLines.Length)
                {
                    for (int i = startIndex; i <= endIndex; i++)
                    {
                        result.Add(allLines[i]);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"读取模板文件行 {startLine}-{endLine} 时发生错误: {ex.Message}", ex);
            }

            return result;
        }

        /// <summary>
        /// 获取XML头部内容（1-65行）
        /// </summary>
        /// <returns>XML头部行列表</returns>
        public List<string> GetXmlHeader()
        {
            return GetTemplateLines(1, 65);
        }

        /// <summary>
        /// 获取实例声明模板（66-436行）
        /// </summary>
        /// <returns>实例声明模板行列表</returns>
        public List<string> GetInstanceDeclarationTemplate()
        {
            return GetTemplateLines(66, 436);
        }

        /// <summary>
        /// 获取固定变量声明模板（3028-3076行）
        /// </summary>
        /// <returns>固定变量声明模板行列表</returns>
        public List<string> GetFixedVariablesTemplate()
        {
            return GetTemplateLines(3028, 3076);
        }

        /// <summary>
        /// 获取HMI显示和模式固定内容（3077-3459行）
        /// </summary>
        /// <returns>HMI固定内容行列表</returns>
        public List<string> GetHmiFixedContent()
        {
            return GetTemplateLines(3077, 3459);
        }

        /// <summary>
        /// 获取单输入实例程序模板（3460-3892行）
        /// </summary>
        /// <returns>单输入程序模板行列表</returns>
        public List<string> GetSingleInputProgramTemplate()
        {
            return GetTemplateLines(3460, 3892);
        }

        /// <summary>
        /// 获取双输入实例程序模板（5625-6099行）
        /// </summary>
        /// <returns>双输入程序模板行列表</returns>
        public List<string> GetDoubleInputProgramTemplate()
        {
            return GetTemplateLines(5625, 6099);
        }

        /// <summary>
        /// 获取最后实例的替换内容（6967-7023行）
        /// </summary>
        /// <returns>最后实例替换内容行列表</returns>
        public List<string> GetLastInstanceReplacement()
        {
            return GetTemplateLines(6967, 7023);
        }

        /// <summary>
        /// 修复XML格式问题，特别是Component和Address标签在同一行的情况
        /// </summary>
        /// <param name="lines">要修复的行列表</param>
        /// <returns>修复后的行列表</returns>
        public List<string> FixXmlFormat(List<string> lines)
        {
            var result = new List<string>();
            int fixedCount = 0;

            foreach (var line in lines)
            {
                if (line.Contains("<Component ") && line.Contains("<Address "))
                {
                    // 获取行的缩进
                    var indentMatch = Regex.Match(line, @"^(\s*)");
                    string indent = indentMatch.Success ? indentMatch.Groups[1].Value : "";

                    // 分离Component和Address标签
                    var parts = new List<string>();
                    string remaining = line;

                    // 处理Component标签
                    while (remaining.Contains("<Component "))
                    {
                        int compStart = remaining.IndexOf("<Component ");
                        if (compStart > 0)
                        {
                            parts.Add(remaining.Substring(0, compStart));
                            remaining = remaining.Substring(compStart);
                        }

                        int compEnd = remaining.IndexOf("/>", compStart);
                        if (compEnd > 0)
                        {
                            string compTag = remaining.Substring(0, compEnd + 2);
                            parts.Add(compTag);
                            remaining = remaining.Substring(compEnd + 2);
                        }
                        else
                        {
                            break;
                        }
                    }

                    // 处理Address标签
                    while (remaining.Contains("<Address "))
                    {
                        int addrStart = remaining.IndexOf("<Address ");
                        if (addrStart > 0)
                        {
                            parts.Add(remaining.Substring(0, addrStart));
                            remaining = remaining.Substring(addrStart);
                        }

                        int addrEnd = remaining.IndexOf("/>", addrStart);
                        if (addrEnd > 0)
                        {
                            string addrTag = remaining.Substring(0, addrEnd + 2);
                            parts.Add(indent + addrTag); // 为Address标签添加缩进
                            remaining = remaining.Substring(addrEnd + 2);
                        }
                        else
                        {
                            parts.Add(remaining);
                            remaining = "";
                            break;
                        }
                    }

                    // 处理剩余部分
                    if (!string.IsNullOrEmpty(remaining))
                    {
                        parts.Add(indent + remaining.Trim());
                    }

                    // 添加所有部分
                    foreach (var part in parts)
                    {
                        if (!string.IsNullOrWhiteSpace(part))
                        {
                            result.Add(part);
                        }
                    }

                    fixedCount++;
                }
                else
                {
                    result.Add(line);
                }
            }

            return result;
        }

        /// <summary>
        /// 修改XML行中的属性值
        /// </summary>
        /// <param name="line">要修改的行</param>
        /// <param name="tagName">标签名</param>
        /// <param name="attributeName">属性名</param>
        /// <param name="newValue">新值</param>
        /// <returns>修改后的行和是否成功的标志</returns>
        public (string ModifiedLine, bool Success) ModifyLineAttribute(string line, string tagName, string attributeName, string newValue)
        {
            string escapedTag = Regex.Escape(tagName);
            string escapedAttr = Regex.Escape(attributeName);
            string escapedValue = XmlEscape(newValue);

            string pattern = $@"(<{escapedTag}[^>]*)({escapedAttr}\s*=\s*"")([^""]+)("")[^>]*>";

            var match = Regex.Match(line, pattern);
            if (match.Success)
            {
                string modifiedLine = line.Replace(match.Groups[3].Value, escapedValue);
                return (modifiedLine, true);
            }

            return (line, false);
        }

        /// <summary>
        /// 修改XML行中的文本内容
        /// </summary>
        /// <param name="line">要修改的行</param>
        /// <param name="tagName">标签名</param>
        /// <param name="newText">新文本</param>
        /// <returns>修改后的行和是否成功的标志</returns>
        public (string ModifiedLine, bool Success) ModifyLineTextContent(string line, string tagName, string newText)
        {
            string escapedText = XmlEscape(newText);

            // 处理普通标签 <tag>content</tag>
            string pattern1 = $@"(<{tagName}[^>]*>)[^<]*(</{tagName}>)";
            string modifiedLine = Regex.Replace(line, pattern1, $"$1{escapedText}$2");

            // 如果没有匹配，尝试处理自闭合标签中的数值 <tag>value</tag>
            if (modifiedLine == line)
            {
                string pattern2 = $@"(<{tagName}[^>]*>)([^<]*)(</{tagName}>)";
                modifiedLine = Regex.Replace(line, pattern2, $"$1{escapedText}$3");
            }

            bool success = modifiedLine != line;
            return (modifiedLine, success);
        }

        /// <summary>
        /// XML转义特殊字符
        /// </summary>
        /// <param name="text">要转义的文本</param>
        /// <returns>转义后的文本</returns>
        public string XmlEscape(string text)
        {
            if (string.IsNullOrEmpty(text)) return string.Empty;

            return text.Replace("&", "&amp;")
                      .Replace("<", "&lt;")
                      .Replace(">", "&gt;")
                      .Replace("\"", "&quot;")
                      .Replace("'", "&apos;");
        }

        /// <summary>
        /// 计算I/O地址的位偏移量
        /// </summary>
        /// <param name="ioAddress">I/O地址字符串，如"I132.0"</param>
        /// <returns>位偏移量</returns>
        public int GetBitOffset(string ioAddress)
        {
            if (string.IsNullOrWhiteSpace(ioAddress))
                return -1;

            var match = Regex.Match(ioAddress, @"^[IQM](\d+)\.(\d+)$", RegexOptions.IgnoreCase);
            if (match.Success)
            {
                int bytePart = int.Parse(match.Groups[1].Value);
                int bitPart = int.Parse(match.Groups[2].Value);

                if (bitPart >= 0 && bitPart <= 7)
                {
                    return bytePart * 8 + bitPart;
                }
            }

            return -1;
        }

        /// <summary>
        /// 验证模板文件的完整性
        /// </summary>
        /// <returns>验证结果</returns>
        public (bool IsValid, string ErrorMessage) ValidateTemplate()
        {
            try
            {
                if (!File.Exists(_templateFilePath))
                    return (false, "模板文件不存在");

                var fileInfo = new FileInfo(_templateFilePath);

                // 检查文件大小
                if (fileInfo.Length == 0)
                    return (false, "模板文件为空");

                // 检查文件扩展名
                if (!fileInfo.Extension.Equals(".xml", StringComparison.OrdinalIgnoreCase))
                    return (false, "模板文件必须是XML格式");

                // 尝试读取文件的前几行来验证基本格式
                try
                {
                    using (var reader = new StreamReader(_templateFilePath, Encoding.UTF8))
                    {
                        string firstLine = reader.ReadLine();
                        if (string.IsNullOrWhiteSpace(firstLine))
                            return (false, "模板文件格式错误：第一行为空");

                        // 检查是否包含XML声明或XML标签
                        if (!firstLine.Contains("<?xml") && !firstLine.Contains("<"))
                        {
                            // 继续读取几行
                            for (int i = 0; i < 10 && !reader.EndOfStream; i++)
                            {
                                string line = reader.ReadLine();
                                if (!string.IsNullOrWhiteSpace(line) && line.Contains("<"))
                                {
                                    return (true, string.Empty); // 找到XML标签
                                }
                            }
                            return (false, "模板文件不是有效的XML格式");
                        }
                    }
                }
                catch (Exception readEx)
                {
                    return (false, $"无法读取模板文件: {readEx.Message}");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"验证模板文件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取模板文件信息
        /// </summary>
        /// <returns>模板文件信息</returns>
        public (string FilePath, long FileSize, int LineCount, DateTime LastModified) GetTemplateInfo()
        {
            var fileInfo = new FileInfo(_templateFilePath);
            string[] lines = File.ReadAllLines(_templateFilePath, Encoding.UTF8);

            return (
                FilePath: _templateFilePath,
                FileSize: fileInfo.Length,
                LineCount: lines.Length,
                LastModified: fileInfo.LastWriteTime
            );
        }
    }
}
