using System;

namespace OpennessTool.Core
{
    /// <summary>
    /// ID生成器，用于生成唯一的十六进制ID
    /// 对应Python版本的IdGenerator类
    /// </summary>
    public class IdGenerator
    {
        private int _currentId;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="startId">起始ID，默认为0x13 (19)</param>
        public IdGenerator(int startId = 0x13)
        {
            _currentId = startId;
        }

        /// <summary>
        /// 获取下一个ID
        /// </summary>
        /// <returns>十六进制格式的ID字符串</returns>
        public string GetNextId()
        {
            int nextIdVal = _currentId;
            _currentId++;
            string hexId = nextIdVal.ToString("X"); // 转换为大写十六进制
            return hexId;
        }

        /// <summary>
        /// 重置ID生成器
        /// </summary>
        /// <param name="startId">新的起始ID</param>
        public void Reset(int startId = 0x13)
        {
            _currentId = startId;
        }

        /// <summary>
        /// 获取当前ID值（不递增）
        /// </summary>
        /// <returns>当前ID的十六进制字符串</returns>
        public string GetCurrentId()
        {
            return _currentId.ToString("X");
        }

        /// <summary>
        /// 设置当前ID值
        /// </summary>
        /// <param name="id">要设置的ID值</param>
        public void SetCurrentId(int id)
        {
            _currentId = id;
        }

        /// <summary>
        /// 批量生成ID
        /// </summary>
        /// <param name="count">要生成的ID数量</param>
        /// <returns>ID数组</returns>
        public string[] GenerateIds(int count)
        {
            string[] ids = new string[count];
            for (int i = 0; i < count; i++)
            {
                ids[i] = GetNextId();
            }
            return ids;
        }

        /// <summary>
        /// 跳过指定数量的ID
        /// </summary>
        /// <param name="count">要跳过的ID数量</param>
        public void Skip(int count)
        {
            _currentId += count;
        }

        /// <summary>
        /// 检查ID是否有效
        /// </summary>
        /// <param name="id">要检查的ID字符串</param>
        /// <returns>是否为有效的十六进制ID</returns>
        public static bool IsValidHexId(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                return false;

            return int.TryParse(id, System.Globalization.NumberStyles.HexNumber, null, out _);
        }

        /// <summary>
        /// 将十六进制ID字符串转换为整数
        /// </summary>
        /// <param name="hexId">十六进制ID字符串</param>
        /// <returns>对应的整数值</returns>
        public static int HexIdToInt(string hexId)
        {
            if (string.IsNullOrWhiteSpace(hexId))
                throw new ArgumentException("ID不能为空", nameof(hexId));

            if (!int.TryParse(hexId, System.Globalization.NumberStyles.HexNumber, null, out int result))
                throw new ArgumentException($"无效的十六进制ID: {hexId}", nameof(hexId));

            return result;
        }

        /// <summary>
        /// 将整数转换为十六进制ID字符串
        /// </summary>
        /// <param name="id">整数ID</param>
        /// <returns>十六进制ID字符串</returns>
        public static string IntToHexId(int id)
        {
            return id.ToString("X");
        }

        /// <summary>
        /// 比较两个十六进制ID的大小
        /// </summary>
        /// <param name="id1">第一个ID</param>
        /// <param name="id2">第二个ID</param>
        /// <returns>比较结果：-1表示id1小于id2，0表示相等，1表示id1大于id2</returns>
        public static int CompareHexIds(string id1, string id2)
        {
            int int1 = HexIdToInt(id1);
            int int2 = HexIdToInt(id2);
            return int1.CompareTo(int2);
        }

        /// <summary>
        /// 获取两个ID之间的差值
        /// </summary>
        /// <param name="startId">起始ID</param>
        /// <param name="endId">结束ID</param>
        /// <returns>ID差值</returns>
        public static int GetIdDifference(string startId, string endId)
        {
            int start = HexIdToInt(startId);
            int end = HexIdToInt(endId);
            return end - start;
        }

        /// <summary>
        /// 生成指定范围内的随机ID
        /// </summary>
        /// <param name="minId">最小ID值</param>
        /// <param name="maxId">最大ID值</param>
        /// <returns>随机ID字符串</returns>
        public static string GenerateRandomId(int minId = 0x13, int maxId = 0xFFFF)
        {
            Random random = new Random();
            int randomId = random.Next(minId, maxId + 1);
            return IntToHexId(randomId);
        }

        /// <summary>
        /// 克隆当前ID生成器
        /// </summary>
        /// <returns>新的ID生成器实例</returns>
        public IdGenerator Clone()
        {
            return new IdGenerator(_currentId);
        }

        /// <summary>
        /// 获取字符串表示
        /// </summary>
        /// <returns>当前状态的字符串描述</returns>
        public override string ToString()
        {
            return $"IdGenerator(CurrentId: {GetCurrentId()}, NextId: {(_currentId).ToString("X")})";
        }
    }
}
