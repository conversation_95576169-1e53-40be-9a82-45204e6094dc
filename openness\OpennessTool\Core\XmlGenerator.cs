using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Linq;
using OpennessTool.Models;

namespace OpennessTool.Core
{
    /// <summary>
    /// XML生成器主类，对应Python版本的XMLGenerator类
    /// 严格按照prompt.md、Network.md、Parts_modify.md的要求生成XML
    /// </summary>
    public class XmlGenerator
    {
        private readonly string _templateFilePath;
        private readonly string _outputFilePath;
        private readonly string _excelFilePath;

        private readonly TemplateProcessor _templateProcessor;
        private readonly ExcelDataReader _excelReader;
        private readonly NetworkGenerator _networkGenerator;
        private readonly IdGenerator _idGenerator;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="templateFilePath">模板XML文件路径</param>
        /// <param name="outputFilePath">输出XML文件路径</param>
        /// <param name="excelFilePath">Excel数据文件路径</param>
        public XmlGenerator(string templateFilePath, string outputFilePath, string excelFilePath)
        {
            _templateFilePath = templateFilePath ?? throw new ArgumentNullException(nameof(templateFilePath));
            _outputFilePath = outputFilePath ?? throw new ArgumentNullException(nameof(outputFilePath));
            _excelFilePath = excelFilePath ?? throw new ArgumentNullException(nameof(excelFilePath));

            // 验证文件存在性
            if (!File.Exists(_templateFilePath))
                throw new FileNotFoundException($"模板文件不存在: {_templateFilePath}");
            if (!File.Exists(_excelFilePath))
                throw new FileNotFoundException($"Excel文件不存在: {_excelFilePath}");

            // 初始化组件
            _templateProcessor = new TemplateProcessor(_templateFilePath);
            _excelReader = new ExcelDataReader();
            _idGenerator = new IdGenerator();
            _networkGenerator = new NetworkGenerator(_templateProcessor, _idGenerator);
        }

        /// <summary>
        /// 生成XML文件
        /// </summary>
        /// <returns>生成是否成功</returns>
        public bool GenerateXml()
        {
            try
            {
                // 1. 验证模板文件
                var (isValid, errorMessage) = _templateProcessor.ValidateTemplate();
                if (!isValid)
                {
                    throw new InvalidOperationException($"模板文件验证失败: {errorMessage}");
                }

                // 如果有警告信息，记录但继续执行
                if (!string.IsNullOrEmpty(errorMessage) && errorMessage.StartsWith("警告"))
                {
                    // 这里可以添加日志记录，暂时忽略警告
                }

                // 2. 读取Excel数据
                var (instances, opModeComment) = _excelReader.ReadExcelData(_excelFilePath);
                if (instances == null || instances.Count == 0)
                {
                    throw new InvalidOperationException("Excel文件中没有找到有效的实例数据");
                }

                // 3. 验证实例数据
                foreach (var instance in instances)
                {
                    var (instanceValid, instanceError) = instance.Validate();
                    if (!instanceValid)
                    {
                        throw new InvalidOperationException($"实例 '{instance.Name}' 数据验证失败: {instanceError}");
                    }
                }

                // 4. 生成XML内容
                var xmlContent = GenerateXmlContent(instances, opModeComment);

                // 5. 写入输出文件
                File.WriteAllText(_outputFilePath, xmlContent, Encoding.UTF8);

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"生成XML文件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 生成XML内容
        /// </summary>
        /// <param name="instances">实例数据列表</param>
        /// <param name="opModeComment">OpMode注释</param>
        /// <returns>生成的XML内容</returns>
        private string GenerateXmlContent(List<InstanceData> instances, string opModeComment)
        {
            var result = new StringBuilder();

            // 1. XML头文件（1-65行）- 完全复制模板，不作任何修改
            var xmlHeader = _templateProcessor.GetXmlHeader();
            foreach (var line in xmlHeader)
            {
                result.AppendLine(line);
            }

            // 2. 变量声明区：实例声明内容生成
            var staticMembers = GenerateStaticMembers(instances);
            foreach (var line in staticMembers)
            {
                result.AppendLine(line);
            }

            // 3. 网络区：生成所有网络代码
            var networkContent = _networkGenerator.GenerateAllNetworks(instances, opModeComment);
            foreach (var line in networkContent)
            {
                result.AppendLine(line);
            }

            return result.ToString();
        }

        /// <summary>
        /// 生成静态成员变量
        /// </summary>
        /// <param name="instances">实例数据列表</param>
        /// <returns>静态成员变量行列表</returns>
        private List<string> GenerateStaticMembers(List<InstanceData> instances)
        {
            var result = new List<string>();

            // 1. 生成实例声明内容
            result.AddRange(GenerateInstanceDeclarations(instances));

            // 2. 生成固定变量的声明
            result.AddRange(GenerateFixedVariables(instances));

            return result;
        }

        /// <summary>
        /// 生成实例声明内容（基于模板66-436行）
        /// </summary>
        /// <param name="instances">实例数据列表</param>
        /// <returns>实例声明行列表</returns>
        private List<string> GenerateInstanceDeclarations(List<InstanceData> instances)
        {
            var result = new List<string>();
            var instanceTemplate = _templateProcessor.GetInstanceDeclarationTemplate();

            for (int i = 0; i < instances.Count; i++)
            {
                var instance = instances[i];
                var instanceLines = new List<string>(instanceTemplate);

                // 修改实例声明的参数
                instanceLines = ModifyInstanceDeclaration(instanceLines, instance, i);

                result.AddRange(instanceLines);
            }

            return result;
        }

        /// <summary>
        /// 修改实例声明的参数
        /// </summary>
        /// <param name="instanceLines">实例声明行</param>
        /// <param name="instance">实例数据</param>
        /// <param name="instanceIndex">实例索引</param>
        /// <returns>修改后的实例声明行</returns>
        private List<string> ModifyInstanceDeclaration(List<string> instanceLines, InstanceData instance, int instanceIndex)
        {
            var result = new List<string>(instanceLines);

            for (int i = 0; i < result.Count; i++)
            {
                string line = result[i];

                // 2.1 修改Member Name（第1行，相对于66行）
                if (i == 0 && line.Contains("<Member Name="))
                {
                    var (modifiedLine, success) = _templateProcessor.ModifyLineAttribute(
                        line, "Member", "Name", instance.Name);
                    if (success)
                    {
                        result[i] = modifiedLine;
                    }
                }

                // 2.2 修改注释（约第13行，相对于78行）
                if (line.Contains("<MultiLanguageText Lang=\"zh-CN\">") &&
                    line.Contains("左缓存挡料气缸1"))
                {
                    var (modifiedLine, success) = _templateProcessor.ModifyLineTextContent(
                        line, "MultiLanguageText", instance.Comment);
                    if (success)
                    {
                        result[i] = modifiedLine;
                    }
                }

                // 2.3 修改偏移量（约第3行，相对于68行）
                // 实例的偏移量特征：包含Informative="true" SystemDefined="true"，且在实例声明的前几行
                if (line.Contains("<IntegerAttribute Name=\"Offset\"") &&
                    line.Contains("Informative=\"true\"") &&
                    line.Contains("SystemDefined=\"true\"") &&
                    i < 10) // 确保是实例声明前几行的偏移量，不是实例内部的偏移量
                {
                    int offset = instanceIndex * 256; // 每个实例偏移量递增256

                    // 使用更精确的正则表达式替换偏移量
                    string pattern = @"(<IntegerAttribute[^>]*Name=""Offset""[^>]*>)(\d+)(</IntegerAttribute>)";
                    string replacement = $"$1{offset}$3";
                    string modifiedLine = Regex.Replace(line, pattern, replacement);

                    if (modifiedLine != line)
                    {
                        result[i] = modifiedLine;
                        // 添加调试信息
                        System.Diagnostics.Debug.WriteLine($"实例 {instanceIndex} ({instance.Name}) 偏移量已设置为: {offset}");
                    }
                    else
                    {
                        // 如果正则表达式失败，尝试简单的字符串替换
                        if (line.Contains(">0</IntegerAttribute>"))
                        {
                            modifiedLine = line.Replace(">0</IntegerAttribute>", $">{offset}</IntegerAttribute>");
                            result[i] = modifiedLine;
                            System.Diagnostics.Debug.WriteLine($"实例 {instanceIndex} ({instance.Name}) 偏移量已通过简单替换设置为: {offset}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"警告: 无法为实例 {instanceIndex} ({instance.Name}) 设置偏移量，行内容: {line.Trim()}");
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 生成固定变量的声明（基于模板3028-3076行）
        /// </summary>
        /// <param name="instances">实例数据列表</param>
        /// <returns>固定变量声明行列表</returns>
        private List<string> GenerateFixedVariables(List<InstanceData> instances)
        {
            var result = new List<string>();
            var fixedTemplate = _templateProcessor.GetFixedVariablesTemplate();

            // 计算最后一个实例的偏移地址
            int lastInstanceOffset = (instances.Count - 1) * 256;
            int staManualOffset = lastInstanceOffset + 256;
            int staAutoOffset = staManualOffset + 1;
            int statResetOffset = staAutoOffset + 1;

            var offsets = new Dictionary<string, int>
            {
                ["staManual"] = staManualOffset,
                ["staAuto"] = staAutoOffset,
                ["statReset"] = statResetOffset
            };

            string currentVariableName = null;

            foreach (var line in fixedTemplate)
            {
                string modifiedLine = line;

                // 检测当前变量名
                if (line.Contains("<Member Name="))
                {
                    if (line.Contains("staManual"))
                        currentVariableName = "staManual";
                    else if (line.Contains("staAuto"))
                        currentVariableName = "staAuto";
                    else if (line.Contains("statReset"))
                        currentVariableName = "statReset";
                }

                // 修改偏移地址
                if (line.Contains("<IntegerAttribute Name=\"Offset\"") &&
                    line.Contains("Informative=\"true\"") &&
                    line.Contains("SystemDefined=\"true\"") &&
                    currentVariableName != null &&
                    offsets.ContainsKey(currentVariableName))
                {
                    var (modifiedOffsetLine, success) = _templateProcessor.ModifyLineTextContent(
                        line, "IntegerAttribute", offsets[currentVariableName].ToString());
                    if (success)
                    {
                        modifiedLine = modifiedOffsetLine;
                    }
                }

                result.Add(modifiedLine);
            }

            return result;
        }

        /// <summary>
        /// 获取生成器信息
        /// </summary>
        /// <returns>生成器信息</returns>
        public (string TemplatePath, string ExcelPath, string OutputPath, string Status) GetGeneratorInfo()
        {
            string status = "就绪";

            try
            {
                if (!File.Exists(_templateFilePath))
                    status = "模板文件不存在";
                else if (!File.Exists(_excelFilePath))
                    status = "Excel文件不存在";
                else
                {
                    var (isValid, errorMessage) = _templateProcessor.ValidateTemplate();
                    if (!isValid)
                        status = $"模板验证失败: {errorMessage}";
                }
            }
            catch (Exception ex)
            {
                status = $"检查状态时出错: {ex.Message}";
            }

            return (
                TemplatePath: _templateFilePath,
                ExcelPath: _excelFilePath,
                OutputPath: _outputFilePath,
                Status: status
            );
        }

        /// <summary>
        /// 预览生成结果
        /// </summary>
        /// <param name="maxLines">最大预览行数</param>
        /// <returns>预览内容</returns>
        public string PreviewGeneration(int maxLines = 100)
        {
            try
            {
                var (instances, opModeComment) = _excelReader.ReadExcelData(_excelFilePath);
                if (instances == null || instances.Count == 0)
                {
                    return "Excel文件中没有找到有效的实例数据";
                }

                var xmlContent = GenerateXmlContent(instances, opModeComment);
                var lines = xmlContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                if (lines.Length <= maxLines)
                {
                    return xmlContent;
                }
                else
                {
                    var previewLines = lines.Take(maxLines).ToArray();
                    return string.Join(Environment.NewLine, previewLines) +
                           Environment.NewLine +
                           $"... (还有 {lines.Length - maxLines} 行)";
                }
            }
            catch (Exception ex)
            {
                return $"预览生成时发生错误: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取实例统计信息
        /// </summary>
        /// <returns>实例统计信息</returns>
        public (int TotalInstances, int SingleInputCount, int DoubleInputCount, List<string> InstanceNames) GetInstanceStatistics()
        {
            try
            {
                var (instances, _) = _excelReader.ReadExcelData(_excelFilePath);
                if (instances == null)
                {
                    return (0, 0, 0, new List<string>());
                }

                int singleInputCount = instances.Count(i => !i.IsDoubleInputType);
                int doubleInputCount = instances.Count(i => i.IsDoubleInputType);
                var instanceNames = instances.Select(i => i.Name).ToList();

                return (instances.Count, singleInputCount, doubleInputCount, instanceNames);
            }
            catch
            {
                return (0, 0, 0, new List<string>());
            }
        }
    }
}
