###<Parts>内容修改方式：
针对单输入点实例程序，需要修改的元素有：

1. UId="21"的元素中:
"<Component Name="I132.0 左UPIN缓存挡料1气缸 伸出到位 NA1-C101a" />"的值需要替换为表格中I列的内容，也就是“I点-工作位1”；
"<Address Area="Input" Type="Bool" BitOffset="1056" Informative="true" />"，其中的"BitOffset"值，需要替换成表格中J列的内容，也就是“I点-工作位1偏移量”；
2. UId="22"的元素中:
"<Component Name="I132.1 左UPIN缓存挡料1气缸 缩回到位 NA1-C101b" />"的值需要替换为表格中M列的内容，也就是“I点-原位1”；
"<Address Area="Input" Type="Bool" BitOffset="1056" Informative="true" />"，其中的"BitOffset"值，需要替换成表格中N列的内容，也就是“I点-原位1偏移量”；
3. UId="23"的元素中：
"<Component Name="DB_ControlNodesC5" />"的值需要替换为表格中E列的内容，也就是“手动行DB块名称”；
"<Component Name="em12" />"的值需要替换为表格中B列的内容，也就是“OpMode名称”；
"<Component Name="NA1C101左缓存挡料气缸1" />"的值，需要替换成表格中G列的内容，也就是“实例名称”；
4. UId="24"的改法与 UId="23"的改法相同；
5. UId="29"的元素中：
"<ConstantValue>12</ConstantValue>"其中的12这个值，需要替换为表格中D列的内容，也就是“OpMode编号”；
6. UId="35"的元素中：
"<Component Name="Q216.4 NA1C101挡料气缸1伸出" />"的值，需要替换为表格中Q列的内容，也就是“Q点-工作位”；
"<Address Area="Output" Type="Bool" BitOffset="1732" Informative="true" />",其中的"BitOffset"值，需要替换成表格中R列的内容，也就是“Q点-工作位偏移量”；
7. UId="36"的元素中：
"<Component Name="Q216.5 NA1C101挡料气缸1缩回" />"的值，需要替换为表格中S列的内容，也就是“Q点-原位”；
"<Address Area="Output" Type="Bool" BitOffset="1733" Informative="true" />",其中的"BitOffset"值，需要替换成表格中T列的内容，也就是“Q点-原位偏移量”；
8. UId="37"的元素中：
"<Component Name="DB_ControlNodesC5" />"的值需要替换为表格中E列的内容，也就是“手动行DB块名称”；
"<Component Name="em12" />"的值需要替换为表格中B列的内容，也就是“OpMode名称”；
"<Component Name="NA1C101挡料气缸1" />"的值，需要替换成表格中G列的内容，也就是“实例名称”；
9. UId="38"的改法与 UId="37"的改法相同；
10. UId="39"的元素有很多，只需要改：
"<Component Name="NA1C101左缓存挡料气缸1" />"的值，需要替换为表格中G列的内容，也就是“实例名称”；



针对双输入点实例程序，需要修改的元素有：

1. UId="21"的元素中:
"<Component Name="I133.0 左IPIN缓存料盘定位气缸 伸出到位 NA1-C501a" />"的值需要替换为表格中I列的内容，也就是“I点-工作位1”；
"<Address Area="Input" Type="Bool" BitOffset="1064" Informative="true" />"，其中的"BitOffset"值，需要替换成表格中J列的内容，也就是“I点-工作位1偏移量”；
2. UId="22"的元素中:
"<Component Name="I133.2 左IPIN缓存料盘定位气缸 伸出到位 NA1-C501c" />"的值需要替换为表格中K列的内容，也就是“I点-工作位2”；
"<Address Area="Input" Type="Bool" BitOffset="1066" Informative="true" />"，其中的"BitOffset"值，需要替换成表格中L列的内容，也就是“I点-工作位2偏移量”；

3. UId="23"的元素中:
"<Component Name="I133.1 左IPIN缓存料盘定位气缸 缩回到位 NA1-C501b" />"的值需要替换为表格中M列的内容，也就是“I点-原位1”；
"<Address Area="Input" Type="Bool" BitOffset="1065" Informative="true" />"，其中的"BitOffset"值，需要替换成表格中N列的内容，也就是“I点-原位1偏移量”；
4. UId="24"的元素中:
"<Component Name="I133.3 左IPIN缓存料盘定位气缸 缩回到位 NA1-C501d" />"的值需要替换为表格中O列的内容，也就是“I点-原位2”；
"<Address Area="Input" Type="Bool" BitOffset="1067" Informative="true" />"，其中的"BitOffset"值，需要替换成表格中P列的内容，也就是“I点-原位2偏移量”；

5. UId="25"的元素中：
"<Component Name="DB_ControlNodesC5" />"的值需要替换为表格中E列的内容，也就是“手动行DB块名称”；
"<Component Name="em12" />"的值需要替换为表格中B列的内容，也就是“OpMode名称”；
"<Component Name="NA1C501左缓存料盘定位气缸" />"的值，需要替换成表格中G列的内容，也就是“实例名称”；
6. UId="26"的改法与 UId="25"的改法相同；

7. UId="31"的元素中：
"<ConstantValue>12</ConstantValue>"其中的12这个值，需要替换为表格中D列的内容，也就是“OpMode编号”；
8. UId="37"的元素中：
"<Component Name="Q217.4 NA1C501料盘定位气缸伸出" />"的值，需要替换为表格中Q列的内容，也就是“Q点-工作位”；
"<Address Area="Output" Type="Bool" BitOffset="1740" Informative="true" />",其中的"BitOffset"值，需要替换成表格中R列的内容，也就是“Q点-工作位偏移量”；
9. UId="38"的元素中：
"<Component Name="Q217.5 NA1C501料盘定位气缸缩回" />"的值，需要替换为表格中S列的内容，也就是“Q点-原位”；
"<Address Area="Output" Type="Bool" BitOffset="1741" Informative="true" />",其中的"BitOffset"值，需要替换成表格中T列的内容，也就是“Q点-原位偏移量”；
10. UId="39"的元素中：
"<Component Name="DB_ControlNodesC5" />"的值需要替换为表格中E列的内容，也就是“手动行DB块名称”；
"<Component Name="em12" />"的值需要替换为表格中B列的内容，也就是“OpMode名称”；
"<Component Name="NA1C501左缓存料盘定位气缸" />"的值，需要替换成表格中G列的内容，也就是“实例名称”；
11. UId="40"的改法与 UId="39"的改法相同；
12. UId="45"的元素有很多，只需要改：
"<Component Name="NA1C501左缓存料盘定位气缸" />"的值，需要替换为表格中G列的内容，也就是“实例名称”；


除了以上中提到的UID元素需要修改，其它未提及到的，请直接复制模板xml中的内容即可，不需要作任何修改；